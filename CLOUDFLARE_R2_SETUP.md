# Cloudflare R2 Setup Guide

## ✅ Migration Complete: Supabase Storage → Cloudflare R2

Your blog application has been successfully migrated from Supabase Storage to Cloudflare R2 for image uploads and storage.

## 🗄️ What Was Changed

### 1. **Removed Supabase Storage**
- ❌ Deleted Supabase storage bucket and policies
- ❌ Removed Supabase storage dependencies from code
- ✅ Created Cloudflare R2 bucket: `blog-images`

### 2. **Updated Database Schema**
- ✅ Added R2-specific columns to `attachments` table:
  - `r2_key` - Cloudflare R2 object key/path
  - `r2_bucket` - R2 bucket name (default: 'blog-images')
  - `cdn_url` - CDN URL for fast delivery
- ✅ Removed `storage_path` column (Supabase-specific)
- ✅ Updated indexes for R2 key lookups

### 3. **Updated Code**
- ✅ Created `src/lib/cloudflareR2.js` - R2 upload utility
- ✅ Updated `ImageUploader.jsx` to use R2 instead of Supabase
- ✅ Added AWS SDK dependencies for R2 integration
- ✅ Created API endpoints for secure uploads

## 🔧 Required Configuration

### Step 1: Get Cloudflare R2 Credentials

1. **Log in to Cloudflare Dashboard**
2. **Go to R2 Object Storage**
3. **Create API Token**:
   - Go to "Manage R2 API Tokens"
   - Click "Create API Token"
   - Select permissions: Object Read & Write
   - Note down: Account ID, Access Key ID, Secret Access Key

### Step 2: Configure Environment Variables

Update your environment files with these values:

#### `.env` and `.env.local`
```env
# Cloudflare R2 Configuration
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id_here
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key_here
CLOUDFLARE_R2_BUCKET_NAME=blog-images
CLOUDFLARE_R2_PUBLIC_URL=https://blog-images.your-account.r2.cloudflarestorage.com
CLOUDFLARE_R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
```

#### `migration/.env`
```env
# Add the same Cloudflare R2 configuration
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id_here
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key_here
CLOUDFLARE_R2_BUCKET_NAME=blog-images
CLOUDFLARE_R2_PUBLIC_URL=https://blog-images.your-account.r2.cloudflarestorage.com
CLOUDFLARE_R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
```

### Step 3: Set Up Custom Domain (Optional)

For better performance and branding:

1. **In Cloudflare Dashboard**:
   - Go to R2 → Settings
   - Add custom domain: `images.yourdomain.com`
   - Update `CLOUDFLARE_R2_PUBLIC_URL` to use your custom domain

## 🏗️ Architecture

### Upload Flow
1. **Frontend**: User selects image in `ImageUploader` component
2. **Compression**: Image is compressed using `imageUtils`
3. **R2 Upload**: File is uploaded to Cloudflare R2 bucket
4. **Database**: Image URL and metadata saved to `attachments` table
5. **Display**: Image served from R2 CDN

### File Structure
```
blog-images/
├── *************-5ci1zp2wm8.jpg
├── *************-abc123def4.png
└── *************-xyz789ghi0.webp
```

### URL Structure
```
Public URL: https://blog-images.your-account.r2.cloudflarestorage.com/blog-images/filename.jpg
Custom Domain: https://images.yourdomain.com/blog-images/filename.jpg
```

## 🔒 Security Features

### 1. **Access Control**
- ✅ R2 bucket configured for public read access
- ✅ Write access only through authenticated API
- ✅ File type validation (JPEG, PNG, GIF, WebP only)
- ✅ File size limits (10MB max)

### 2. **Database Security**
- ✅ Row Level Security (RLS) on attachments table
- ✅ Users can only manage their own uploads
- ✅ Admins can manage all attachments

## 📊 Benefits of R2 vs Supabase Storage

| Feature | Supabase Storage | Cloudflare R2 |
|---------|------------------|---------------|
| **Cost** | $0.021/GB/month | $0.015/GB/month |
| **Bandwidth** | $0.09/GB | Free egress |
| **CDN** | Limited | Global CDN included |
| **Performance** | Good | Excellent |
| **Scalability** | Good | Unlimited |

## 🧪 Testing the Setup

### 1. **Install Dependencies**
```bash
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
```

### 2. **Test Upload**
```bash
npm run dev
# Navigate to admin panel and try uploading an image
```

### 3. **Verify Database**
```sql
-- Check uploaded images
SELECT id, filename, url, r2_key, r2_bucket, created_at 
FROM attachments 
ORDER BY created_at DESC 
LIMIT 5;
```

## 🚀 Production Deployment

### 1. **Environment Variables**
- Set all Cloudflare R2 credentials in production environment
- Use secure secret management (not plain text)

### 2. **CDN Configuration**
- Set up custom domain for better performance
- Configure cache headers for optimal delivery

### 3. **Monitoring**
- Monitor R2 usage in Cloudflare Dashboard
- Set up alerts for storage quotas

## 🔧 Troubleshooting

### Common Issues

1. **Upload fails with 400 error**
   - Check R2 credentials are correct
   - Verify bucket name matches configuration
   - Ensure API token has correct permissions

2. **Images not displaying**
   - Check public URL configuration
   - Verify bucket has public read access
   - Test direct URL access

3. **CORS errors**
   - Configure CORS policy in R2 bucket settings
   - Allow your domain for uploads

### Debug Commands
```bash
# Test R2 connection
npm run test:connection

# Check environment variables
echo $CLOUDFLARE_R2_BUCKET_NAME

# Verify bucket exists
# (Use Cloudflare dashboard or CLI)
```

## ✅ Next Steps

1. **Configure your R2 credentials** in environment files
2. **Test image uploads** in development
3. **Set up custom domain** for production
4. **Monitor usage** and costs
5. **Implement backup strategy** if needed

Your blog is now ready with Cloudflare R2 for fast, cost-effective image storage! 🎉
