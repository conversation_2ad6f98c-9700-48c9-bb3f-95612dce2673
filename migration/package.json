{"name": "wordpress-to-supabase-migration", "version": "1.0.0", "description": "Migration script to transfer WordPress data to Supabase", "main": "extract-data.js", "scripts": {"extract": "node extract-data.js", "migrate": "node migrate-to-supabase.js", "start": "npm run extract && npm run migrate"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.11.0", "dotenv": "^16.3.1", "fs-extra": "^11.2.0", "he": "^1.2.0", "mysql2": "^3.6.5", "node-sql-parser": "^5.3.10"}, "devDependencies": {"lighthouse": "^12.8.0", "nodemon": "^3.0.2"}, "keywords": ["wordpress", "supabase", "migration"], "author": "WordPress Migration Tool", "license": "MIT"}