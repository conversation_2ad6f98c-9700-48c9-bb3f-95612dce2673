# Supabase Configuration (blog-website project)
SUPABASE_URL=https://cgmlpbxwmqynmshecaqn.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# WordPress Database Configuration (if using direct DB connection)
WP_DB_HOST=localhost
WP_DB_USER=your_wp_db_user
WP_DB_PASSWORD=your_wp_db_password
WP_DB_NAME=your_wp_db_name

# WordPress SQL File Path
WP_SQL_FILE=../u957990218_GpBKT.sql

# WordPress Site URL (for downloading images)
WORDPRESS_BASE_URL=https://your-wordpress-site.com

# Migration Settings
BATCH_SIZE=100
DRY_RUN=false

# Cloudflare R2 Configuration for Image Storage
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id_here
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key_here
CLOUDFLARE_R2_BUCKET_NAME=blog-images
CLOUDFLARE_R2_PUBLIC_URL=https://blog-images.your-account.r2.cloudflarestorage.com
CLOUDFLARE_R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
