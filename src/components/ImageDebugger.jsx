/**
 * Debug component to help troubleshoot image loading issues
 * Shows original URLs, converted URLs, and loading status
 */

import { useState, useEffect } from 'react'
import { convertSupabaseToR2Url, getThumbnailImage } from '../utils/contentUtils'

const ImageDebugger = ({ post, showDebug = false }) => {
  const [debugInfo, setDebugInfo] = useState(null)
  
  useEffect(() => {
    if (!showDebug || !post) return
    
    const originalUrl = post.featured_image_url
    const convertedUrl = convertSupabaseToR2Url(originalUrl)
    const thumbnailUrl = getThumbnailImage(post.content, post.featured_image_url)
    
    // Check R2 configuration
    const r2PublicUrl = import.meta.env.VITE_CLOUDFLARE_R2_PUBLIC_URL
    const isR2Configured = r2PublicUrl && !r2PublicUrl.includes('your_account_id')
    
    setDebugInfo({
      originalUrl,
      convertedUrl,
      thumbnailUrl,
      r2PublicUrl,
      isR2Configured,
      wasConverted: originalUrl !== convertedUrl,
      isSupabaseUrl: originalUrl?.includes('supabase.co'),
      isPlaceholderR2: originalUrl?.includes('your-account.r2.cloudflarestorage.com'),
      isExternalUrl: originalUrl && !originalUrl.includes('supabase.co') && !originalUrl.includes('your-account.r2.cloudflarestorage.com'),
      hasImage: !!originalUrl
    })
  }, [post, showDebug])
  
  if (!showDebug || !debugInfo) return null
  
  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontSize: '12px',
      maxWidth: '400px',
      zIndex: 9999,
      fontFamily: 'monospace'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#4CAF50' }}>🐛 Image Debug Info</h4>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Post:</strong> {post.title}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>R2 Configured:</strong> 
        <span style={{ color: debugInfo.isR2Configured ? '#4CAF50' : '#f44336' }}>
          {debugInfo.isR2Configured ? ' ✅ Yes' : ' ❌ No'}
        </span>
      </div>
      
      {debugInfo.r2PublicUrl && (
        <div style={{ marginBottom: '8px' }}>
          <strong>R2 URL:</strong> {debugInfo.r2PublicUrl}
        </div>
      )}
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Original URL:</strong>
        <div style={{ 
          background: 'rgba(255, 255, 255, 0.1)', 
          padding: '4px', 
          borderRadius: '4px',
          wordBreak: 'break-all',
          fontSize: '10px'
        }}>
          {debugInfo.originalUrl || 'None'}
        </div>
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Converted URL:</strong>
        <div style={{ 
          background: 'rgba(255, 255, 255, 0.1)', 
          padding: '4px', 
          borderRadius: '4px',
          wordBreak: 'break-all',
          fontSize: '10px'
        }}>
          {debugInfo.convertedUrl || 'None'}
        </div>
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Status:</strong>
        {!debugInfo.hasImage && (
          <span style={{ color: '#9e9e9e' }}> ℹ️ No featured image</span>
        )}
        {debugInfo.isSupabaseUrl && debugInfo.wasConverted && (
          <span style={{ color: '#4CAF50' }}> ✅ Supabase URL converted to R2</span>
        )}
        {debugInfo.isSupabaseUrl && !debugInfo.wasConverted && (
          <span style={{ color: '#ff9800' }}> ⚠️ Supabase URL not converted (R2 not configured)</span>
        )}
        {debugInfo.isPlaceholderR2 && debugInfo.wasConverted && (
          <span style={{ color: '#4CAF50' }}> ✅ Placeholder R2 URL converted</span>
        )}
        {debugInfo.isPlaceholderR2 && !debugInfo.wasConverted && (
          <span style={{ color: '#f44336' }}> ❌ Placeholder R2 URL (R2 not configured)</span>
        )}
        {debugInfo.isExternalUrl && (
          <span style={{ color: '#2196F3' }}> ℹ️ External URL (no conversion needed)</span>
        )}
      </div>
      
      {debugInfo.thumbnailUrl && (
        <div style={{ marginTop: '10px' }}>
          <strong>Image Test:</strong>
          <img 
            src={debugInfo.thumbnailUrl} 
            alt="Debug test"
            style={{ 
              width: '100px', 
              height: '60px', 
              objectFit: 'cover',
              border: '1px solid #ccc',
              borderRadius: '4px',
              display: 'block',
              marginTop: '5px'
            }}
            onLoad={() => console.log('✅ Image loaded successfully:', debugInfo.thumbnailUrl)}
            onError={() => console.error('❌ Image failed to load:', debugInfo.thumbnailUrl)}
          />
        </div>
      )}
    </div>
  )
}

export default ImageDebugger
