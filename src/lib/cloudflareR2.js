/**
 * Cloudflare R2 Storage Utility
 * Handles image uploads to Cloudflare R2 bucket
 */

// R2 Configuration from environment variables
const R2_CONFIG = {
  accountId: import.meta.env.CLOUDFLARE_ACCOUNT_ID,
  accessKeyId: import.meta.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
  secretAccessKey: import.meta.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  bucketName: import.meta.env.CLOUDFLARE_R2_BUCKET_NAME || 'blog-images',
  publicUrl: import.meta.env.CLOUDFLARE_R2_PUBLIC_URL,
  endpoint: import.meta.env.CLOUDFLARE_R2_ENDPOINT
}

/**
 * Generate a unique filename for uploaded images
 * @param {string} originalName - Original filename
 * @returns {string} - Unique filename with timestamp and random string
 */
function generateUniqueFilename(originalName) {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const extension = originalName.split('.').pop()
  return `blog-images/${timestamp}-${randomString}.${extension}`
}

/**
 * Create AWS Signature Version 4 for R2 authentication
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {string} payload - Request payload
 * @param {Date} date - Request date
 * @returns {string} - Authorization header
 */
async function createSignature(method, url, payload, date) {
  const { accessKeyId, secretAccessKey, accountId } = R2_CONFIG
  
  if (!accessKeyId || !secretAccessKey) {
    throw new Error('Cloudflare R2 credentials not configured')
  }

  // This is a simplified version - in production, use a proper AWS SDK or signing library
  const region = 'auto'
  const service = 's3'
  
  // For now, we'll use a simpler approach with presigned URLs
  // In a real implementation, you'd want to use AWS SDK or implement full AWS4 signing
  return `AWS4-HMAC-SHA256 Credential=${accessKeyId}/${date.toISOString().split('T')[0]}/${region}/${service}/aws4_request`
}

/**
 * Upload image to Cloudflare R2 using Cloudflare API
 * @param {File} file - Image file to upload
 * @param {Object} options - Upload options
 * @returns {Promise<Object>} - Upload result with URL and metadata
 */
export async function uploadImageToR2(file, options = {}) {
  try {
    if (!file) {
      throw new Error('No file provided')
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.')
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      throw new Error('File size too large. Maximum size is 10MB.')
    }

    const filename = generateUniqueFilename(file.name)
    const { bucketName, publicUrl } = R2_CONFIG

    // Call progress callback if provided
    if (options.onProgress) {
      options.onProgress(10) // Starting upload
    }

    // Convert file to base64 for API upload
    const fileBuffer = await file.arrayBuffer()
    const base64File = btoa(String.fromCharCode(...new Uint8Array(fileBuffer)))

    if (options.onProgress) {
      options.onProgress(30) // File processed
    }

    // Use our Cloudflare integration to upload
    const uploadResult = await uploadToCloudflareR2(filename, base64File, file.type)

    if (options.onProgress) {
      options.onProgress(90) // Upload complete
    }

    if (!uploadResult.success) {
      throw new Error(uploadResult.error || 'Upload failed')
    }

    if (options.onProgress) {
      options.onProgress(100) // Finished
    }

    // Generate public URL
    const imageUrl = publicUrl ? `${publicUrl}/${filename}` : uploadResult.url

    return {
      success: true,
      url: imageUrl,
      filename,
      size: file.size,
      type: file.type,
      uploadedAt: new Date().toISOString()
    }

  } catch (error) {
    console.error('R2 Upload Error:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Upload file to Cloudflare R2 using our backend integration
 * @param {string} filename - Target filename
 * @param {string} base64Data - Base64 encoded file data
 * @param {string} contentType - File MIME type
 * @returns {Promise<Object>} - Upload result
 */
async function uploadToCloudflareR2(filename, base64Data, contentType) {
  try {
    // This would typically call your backend API or use Cloudflare Workers
    // For now, we'll simulate the upload

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500))

    // Mock successful upload
    return {
      success: true,
      url: `https://blog-images.your-account.r2.cloudflarestorage.com/${filename}`,
      filename
    }

  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Delete image from Cloudflare R2
 * @param {string} filename - Filename to delete
 * @returns {Promise<Object>} - Deletion result
 */
export async function deleteImageFromR2(filename) {
  try {
    const response = await fetch('/api/delete-image', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filename })
    })

    if (!response.ok) {
      throw new Error('Failed to delete image')
    }

    return {
      success: true,
      message: 'Image deleted successfully'
    }

  } catch (error) {
    console.error('R2 Delete Error:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Get public URL for an image
 * @param {string} filename - Image filename
 * @returns {string} - Public URL
 */
export function getImageUrl(filename) {
  const { publicUrl } = R2_CONFIG
  if (!publicUrl) {
    console.warn('R2 public URL not configured')
    return filename
  }
  
  // Remove leading slash if present
  const cleanFilename = filename.startsWith('/') ? filename.slice(1) : filename
  return `${publicUrl}/${cleanFilename}`
}

/**
 * Validate R2 configuration
 * @returns {boolean} - Whether R2 is properly configured
 */
export function isR2Configured() {
  const { accountId, accessKeyId, secretAccessKey, bucketName, endpoint } = R2_CONFIG
  return !!(accountId && accessKeyId && secretAccessKey && bucketName && endpoint)
}

export default {
  uploadImageToR2,
  deleteImageFromR2,
  getImageUrl,
  isR2Configured
}
