// Utility functions for content processing and optimization

/**
 * Extract clean text excerpt from HTML content
 * @param {string} content - HTML content
 * @param {number} maxLength - Maximum length of excerpt
 * @returns {string} Clean text excerpt
 */
export const getExcerpt = (content, maxLength = 150) => {
  if (!content) return ''
  
  // Remove HTML tags and decode entities
  const textContent = content
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
    .replace(/&amp;/g, '&') // Decode ampersands
    .replace(/&lt;/g, '<') // Decode less than
    .replace(/&gt;/g, '>') // Decode greater than
    .replace(/&quot;/g, '"') // Decode quotes
    .replace(/&#39;/g, "'") // Decode apostrophes
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
  
  if (textContent.length <= maxLength) {
    return textContent
  }
  
  // Find the last complete word within the limit
  const truncated = textContent.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')
  
  if (lastSpaceIndex > maxLength * 0.8) {
    return truncated.substring(0, lastSpaceIndex) + '...'
  }
  
  return truncated + '...'
}

/**
 * Extract the first image URL from HTML content or featured image
 * @param {string} content - HTML content
 * @param {string} featuredImageUrl - Featured image URL from database
 * @returns {string|null} First image URL or null
 */
export const getThumbnailImage = (content, featuredImageUrl = null) => {
  // First, check if there's a featured image URL
  if (featuredImageUrl) {
    // Convert Supabase URLs to R2 URLs if needed
    return convertSupabaseToR2Url(featuredImageUrl)
  }

  if (!content) return null

  // Extract the first image from the content
  const imgMatch = content.match(/<img[^>]+src="([^"]*)"[^>]*>/i)
  if (imgMatch) {
    // Convert Supabase URLs to R2 URLs if needed
    return convertSupabaseToR2Url(imgMatch[1])
  }

  return null
}

/**
 * Format date for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date
 */
export const formatDate = (dateString) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  // Show relative dates for recent posts
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
  
  // Show formatted date for older posts
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

/**
 * Debounce function for search optimization
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * Convert placeholder or Supabase Storage URLs to properly configured Cloudflare R2 URLs
 * @param {string} originalUrl - Original image URL (Supabase, placeholder R2, or other)
 * @returns {string} Converted R2 URL or original URL if conversion not needed/possible
 */
export const convertSupabaseToR2Url = (originalUrl) => {
  if (!originalUrl) return null

  // Get R2 configuration from environment
  const r2PublicUrl = import.meta.env.VITE_CLOUDFLARE_R2_PUBLIC_URL ||
                     import.meta.env.CLOUDFLARE_R2_PUBLIC_URL

  // Check if R2 URL is properly configured (not placeholder)
  const isR2Configured = r2PublicUrl && !r2PublicUrl.includes('your_account_id')

  // Case 1: Convert Supabase storage URLs
  if (originalUrl.includes('supabase.co/storage/v1/object/public/images/')) {
    try {
      const urlParts = originalUrl.split('/images/')
      if (urlParts.length === 2) {
        const filename = urlParts[1]

        if (isR2Configured) {
          const cleanR2Url = r2PublicUrl.replace(/\/$/, '')
          return `${cleanR2Url}/${filename}`
        } else {
          console.warn('Cloudflare R2 not properly configured. Please set VITE_CLOUDFLARE_R2_PUBLIC_URL in your .env file.')
          console.warn('Falling back to original Supabase URL:', originalUrl)
        }
      }
    } catch (error) {
      console.warn('Failed to convert Supabase URL to R2:', error)
    }
  }

  // Case 2: Convert placeholder R2 URLs (from migration scripts)
  if (originalUrl.includes('your-account.r2.cloudflarestorage.com') ||
      originalUrl.includes('blog-images.your-account.r2.cloudflarestorage.com')) {
    try {
      // Extract filename from placeholder R2 URL
      const urlParts = originalUrl.split('/blog-images/')
      if (urlParts.length === 2) {
        const filename = urlParts[1]

        if (isR2Configured) {
          const cleanR2Url = r2PublicUrl.replace(/\/$/, '')
          return `${cleanR2Url}/${filename}`
        } else {
          console.warn('Cloudflare R2 not properly configured. Placeholder R2 URL detected:', originalUrl)
          console.warn('Please set VITE_CLOUDFLARE_R2_PUBLIC_URL in your .env file.')
        }
      }
    } catch (error) {
      console.warn('Failed to convert placeholder R2 URL:', error)
    }
  }

  return originalUrl
}

/**
 * Optimize image URL for both Supabase storage and Cloudflare R2
 * @param {string} url - Original image URL
 * @param {number} width - Desired width
 * @param {number} quality - Image quality (1-100)
 * @returns {string} Optimized image URL
 */
export const optimizeImageUrl = (url, width = 800, quality = 80) => {
  if (!url) return null

  // First, try to convert Supabase URLs to R2 URLs
  const convertedUrl = convertSupabaseToR2Url(url)

  // Check if it's a Supabase storage URL (for backward compatibility)
  if (convertedUrl.includes('supabase.co/storage/v1/object/public/')) {
    try {
      const urlObj = new URL(convertedUrl)
      urlObj.searchParams.set('width', width)
      urlObj.searchParams.set('quality', quality)
      urlObj.searchParams.set('format', 'webp')
      return urlObj.toString()
    } catch (error) {
      console.warn('Failed to optimize Supabase image URL:', error)
    }
  }

  // For R2 URLs, we don't have built-in optimization yet
  // Return the converted URL as-is
  return convertedUrl
}

/**
 * Preload critical resources
 * @param {Array} urls - Array of URLs to preload
 * @param {string} as - Resource type (image, script, style, etc.)
 */
export const preloadResources = (urls, as = 'image') => {
  urls.forEach(url => {
    if (!url) return
    
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = as
    link.href = url
    
    if (as === 'image') {
      link.crossOrigin = 'anonymous'
    }
    
    document.head.appendChild(link)
  })
}

/**
 * Create intersection observer for lazy loading
 * @param {Function} callback - Callback function when element intersects
 * @param {Object} options - Intersection observer options
 * @returns {IntersectionObserver} Observer instance
 */
export const createIntersectionObserver = (callback, options = {}) => {
  const defaultOptions = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  }
  
  return new IntersectionObserver(callback, defaultOptions)
}
