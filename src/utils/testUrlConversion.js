/**
 * Test script for URL conversion functionality
 * Run this to verify Supabase to R2 URL conversion is working
 */

import { convertSupabaseToR2Url, getThumbnailImage } from './contentUtils.js'

// Test URLs from your actual data
const testUrls = [
  'https://ckjpejxjpcfmlyopqabt.supabase.co/storage/v1/object/public/images/blog-images/1753893790773-yxpu6s6i6v.png',
  'https://ckjpejxjpcfmlyopqabt.supabase.co/storage/v1/object/public/images/1753736774755-z714ar-Merry-Christmas-Wishes-1-1024x1024.jpg',
  'https://ckjpejxjpcfmlyopqabt.supabase.co/storage/v1/object/public/images/1753736810996-eten<PERSON>-<PERSON>lika-<PERSON>-Images-1024x1024.webp',
  'https://example.com/regular-image.jpg' // Non-Supabase URL for testing
]

export const testUrlConversion = () => {
  console.log('🧪 Testing URL Conversion...')
  console.log('=' .repeat(50))
  
  testUrls.forEach((url, index) => {
    console.log(`\nTest ${index + 1}:`)
    console.log(`Original: ${url}`)
    
    const converted = convertSupabaseToR2Url(url)
    console.log(`Converted: ${converted}`)
    
    const isSupabaseUrl = url.includes('supabase.co')
    const wasConverted = converted !== url
    
    if (isSupabaseUrl && wasConverted) {
      console.log('✅ Supabase URL successfully converted to R2')
    } else if (isSupabaseUrl && !wasConverted) {
      console.log('⚠️  Supabase URL not converted (R2 may not be configured)')
    } else if (!isSupabaseUrl && !wasConverted) {
      console.log('✅ Non-Supabase URL left unchanged (correct)')
    }
  })
  
  console.log('\n' + '=' .repeat(50))
  console.log('🧪 Testing getThumbnailImage function...')
  
  // Test with featured image URL
  const testPost = {
    featured_image_url: 'https://ckjpejxjpcfmlyopqabt.supabase.co/storage/v1/object/public/images/1753736810996-etenja-Holika-Dahan-Images-1024x1024.webp',
    content: '<p>Some content with <img src="https://ckjpejxjpcfmlyopqabt.supabase.co/storage/v1/object/public/images/blog-images/1753893790773-yxpu6s6i6v.png" alt="test"> image</p>'
  }
  
  const thumbnailUrl = getThumbnailImage(testPost.content, testPost.featured_image_url)
  console.log(`\nThumbnail URL: ${thumbnailUrl}`)
  
  if (thumbnailUrl && thumbnailUrl !== testPost.featured_image_url) {
    console.log('✅ Featured image URL was converted')
  } else if (thumbnailUrl === testPost.featured_image_url) {
    console.log('⚠️  Featured image URL was not converted (R2 may not be configured)')
  }
  
  console.log('\n✨ URL conversion test completed!')
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testUrlConversion = testUrlConversion
}
