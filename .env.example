# Supabase Configuration for React App (blog-website project)
# Get these values from your Supabase project dashboard
VITE_SUPABASE_URL=https://cgmlpbxwmqynmshecaqn.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Supabase Service Key for Migration Scripts (KEEP PRIVATE!)
SUPABASE_URL=https://cgmlpbxwmqynmshecaqn.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_key_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Development Settings
NODE_ENV=development

# Cloudflare R2 Configuration for Image Storage
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id_here
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key_here
CLOUDFLARE_R2_BUCKET_NAME=blog-images
CLOUDFLARE_R2_PUBLIC_URL=https://blog-images.your-account.r2.cloudflarestorage.com
CLOUDFLARE_R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
