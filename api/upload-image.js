/**
 * API Endpoint for Image Upload to Cloudflare R2
 * Simple proxy for direct uploads
 */

// For now, let's create a simple mock endpoint
// In production, you'd implement proper R2 integration

// R2 Configuration
const R2_CONFIG = {
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
  accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
  secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'blog-images',
  publicUrl: process.env.CLOUDFLARE_R2_PUBLIC_URL,
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT
}

// Create S3 client for R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: R2_CONFIG.endpoint,
  credentials: {
    accessKeyId: R2_CONFIG.accessKeyId,
    secretAccessKey: R2_CONFIG.secretAccessKey,
  },
})

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { filename, contentType, size } = req.body

    // Validate input
    if (!filename || !contentType) {
      return res.status(400).json({ error: 'Missing filename or contentType' })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(contentType)) {
      return res.status(400).json({ error: 'Invalid file type' })
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024
    if (size && size > maxSize) {
      return res.status(400).json({ error: 'File too large' })
    }

    // Check R2 configuration
    if (!R2_CONFIG.accessKeyId || !R2_CONFIG.secretAccessKey || !R2_CONFIG.endpoint) {
      return res.status(500).json({ error: 'R2 not configured' })
    }

    // Create the put object command
    const command = new PutObjectCommand({
      Bucket: R2_CONFIG.bucketName,
      Key: filename,
      ContentType: contentType,
      // Set cache control for images
      CacheControl: 'public, max-age=31536000', // 1 year
      // Set metadata
      Metadata: {
        'uploaded-by': 'blog-app',
        'upload-date': new Date().toISOString()
      }
    })

    // Generate presigned URL (valid for 5 minutes)
    const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 300 })

    // Generate public URL
    const publicUrl = `${R2_CONFIG.publicUrl}/${filename}`

    return res.status(200).json({
      uploadUrl,
      publicUrl,
      filename,
      expiresIn: 300
    })

  } catch (error) {
    console.error('Upload URL generation error:', error)
    return res.status(500).json({ 
      error: 'Failed to generate upload URL',
      details: error.message 
    })
  }
}
