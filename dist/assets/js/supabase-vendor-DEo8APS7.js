import{H as b,n as g,A as v,P as _,S as T,F as m,R as O}from"./vendor-CJchTyIO.js";const w="2.53.0";let d="";typeof Deno!="undefined"?d="deno":typeof document!="undefined"?d="web":typeof navigator!="undefined"&&navigator.product==="ReactNative"?d="react-native":d="node";const A={"X-Client-Info":`supabase-js-${d}/${w}`},y={headers:A},U={schema:"public"},S={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},E={};var k=function(o,e,t,s){function l(a){return a instanceof t?a:new t(function(r){r(a)})}return new(t||(t=Promise))(function(a,r){function h(n){try{c(s.next(n))}catch(i){r(i)}}function u(n){try{c(s.throw(n))}catch(i){r(i)}}function c(n){n.done?a(n.value):l(n.value).then(h,u)}c((s=s.apply(o,e||[])).next())})};const j=o=>{let e;return o?e=o:typeof fetch=="undefined"?e=g:e=fetch,(...t)=>e(...t)},C=()=>typeof Headers=="undefined"?b:Headers,L=(o,e,t)=>{const s=j(t),l=C();return(a,r)=>k(void 0,void 0,void 0,function*(){var h;const u=(h=yield e())!==null&&h!==void 0?h:o;let c=new l(r==null?void 0:r.headers);return c.has("apikey")||c.set("apikey",o),c.has("Authorization")||c.set("Authorization",`Bearer ${u}`),s(a,Object.assign(Object.assign({},r),{headers:c}))})};var D=function(o,e,t,s){function l(a){return a instanceof t?a:new t(function(r){r(a)})}return new(t||(t=Promise))(function(a,r){function h(n){try{c(s.next(n))}catch(i){r(i)}}function u(n){try{c(s.throw(n))}catch(i){r(i)}}function c(n){n.done?a(n.value):l(n.value).then(h,u)}c((s=s.apply(o,e||[])).next())})};function F(o){return o.endsWith("/")?o:o+"/"}function R(o,e){var t,s;const{db:l,auth:a,realtime:r,global:h}=o,{db:u,auth:c,realtime:n,global:i}=e,f={db:Object.assign(Object.assign({},u),l),auth:Object.assign(Object.assign({},c),a),realtime:Object.assign(Object.assign({},n),r),storage:{},global:Object.assign(Object.assign(Object.assign({},i),h),{headers:Object.assign(Object.assign({},(t=i==null?void 0:i.headers)!==null&&t!==void 0?t:{}),(s=h==null?void 0:h.headers)!==null&&s!==void 0?s:{})}),accessToken:()=>D(this,void 0,void 0,function*(){return""})};return o.accessToken?f.accessToken=o.accessToken:delete f.accessToken,f}class I extends v{constructor(e){super(e)}}var N=function(o,e,t,s){function l(a){return a instanceof t?a:new t(function(r){r(a)})}return new(t||(t=Promise))(function(a,r){function h(n){try{c(s.next(n))}catch(i){r(i)}}function u(n){try{c(s.throw(n))}catch(i){r(i)}}function c(n){n.done?a(n.value):l(n.value).then(h,u)}c((s=s.apply(o,e||[])).next())})};class H{constructor(e,t,s){var l,a,r;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const h=F(e),u=new URL(h);this.realtimeUrl=new URL("realtime/v1",u),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",u),this.storageUrl=new URL("storage/v1",u),this.functionsUrl=new URL("functions/v1",u);const c=`sb-${u.hostname.split(".")[0]}-auth-token`,n={db:U,realtime:E,auth:Object.assign(Object.assign({},S),{storageKey:c}),global:y},i=R(s!=null?s:{},n);this.storageKey=(l=i.auth.storageKey)!==null&&l!==void 0?l:"",this.headers=(a=i.global.headers)!==null&&a!==void 0?a:{},i.accessToken?(this.accessToken=i.accessToken,this.auth=new Proxy({},{get:(f,p)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(p)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((r=i.auth)!==null&&r!==void 0?r:{},this.headers,i.global.fetch),this.fetch=L(t,this._getAccessToken.bind(this),i.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},i.realtime)),this.rest=new _(new URL("rest/v1",u).href,{headers:this.headers,schema:i.db.schema,fetch:this.fetch}),this.storage=new T(this.storageUrl.href,this.headers,this.fetch,s==null?void 0:s.storage),i.accessToken||this._listenForAuthEvents()}get functions(){return new m(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return N(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return(t=(e=s.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:l,storageKey:a,flowType:r,lock:h,debug:u},c,n){const i={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new I({url:this.authUrl.href,headers:Object.assign(Object.assign({},i),c),storageKey:a,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:l,flowType:r,lock:h,debug:u,fetch:n,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new O(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,s)=>{this._handleTokenChanged(t,"CLIENT",s==null?void 0:s.access_token)})}_handleTokenChanged(e,t,s){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==s?this.changedAccessToken=s:e==="SIGNED_OUT"&&(this.realtime.setAuth(),t=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const K=(o,e,t)=>new H(o,e,t);function $(){if(typeof window!="undefined"||typeof process=="undefined"||process.version===void 0||process.version===null)return!1;const o=process.version.match(/^v(\d+)\./);return o?parseInt(o[1],10)<=18:!1}$();export{K as c};
