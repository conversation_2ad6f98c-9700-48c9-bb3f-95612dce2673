var K=(l,r,e)=>new Promise((t,n)=>{var i=f=>{try{c(e.next(f))}catch(y){n(y)}},a=f=>{try{c(e.throw(f))}catch(y){n(y)}},c=f=>f.done?t(f.value):Promise.resolve(f.value).then(i,a);c((e=e.apply(l,r)).next())});import{g as So,d as Va,e as lt}from"./react-vendor-QWuLYCV5.js";var kt={exports:{}},At={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ln;function To(){return Ln||(Ln=1,function(l){function r(H,D){var L=H.length;H.push(D);e:for(;0<L;){var P=L-1>>>1,N=H[P];if(0<n(N,D))H[P]=D,H[L]=N,L=P;else break e}}function e(H){return H.length===0?null:H[0]}function t(H){if(H.length===0)return null;var D=H[0],L=H.pop();if(L!==D){H[0]=L;e:for(var P=0,N=H.length,C=N>>>1;P<C;){var $=2*(P+1)-1,B=H[$],j=$+1,M=H[j];if(0>n(B,L))j<N&&0>n(M,B)?(H[P]=M,H[j]=L,P=j):(H[P]=B,H[$]=L,P=$);else if(j<N&&0>n(M,L))H[P]=M,H[j]=L,P=j;else break e}}return D}function n(H,D){var L=H.sortIndex-D.sortIndex;return L!==0?L:H.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;l.unstable_now=function(){return i.now()}}else{var a=Date,c=a.now();l.unstable_now=function(){return a.now()-c}}var f=[],y=[],d=1,u=null,o=3,s=!1,_=!1,m=!1,g=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate!="undefined"?setImmediate:null;typeof navigator!="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function b(H){for(var D=e(y);D!==null;){if(D.callback===null)t(y);else if(D.startTime<=H)t(y),D.sortIndex=D.expirationTime,r(f,D);else break;D=e(y)}}function h(H){if(m=!1,b(H),!_)if(e(f)!==null)_=!0,U(w);else{var D=e(y);D!==null&&F(h,D.startTime-H)}}function w(H,D){_=!1,m&&(m=!1,v(T),T=-1),s=!0;var L=o;try{for(b(D),u=e(f);u!==null&&(!(u.expirationTime>D)||H&&!O());){var P=u.callback;if(typeof P=="function"){u.callback=null,o=u.priorityLevel;var N=P(u.expirationTime<=D);D=l.unstable_now(),typeof N=="function"?u.callback=N:u===e(f)&&t(f),b(D)}else t(f);u=e(f)}if(u!==null)var C=!0;else{var $=e(y);$!==null&&F(h,$.startTime-D),C=!1}return C}finally{u=null,o=L,s=!1}}var S=!1,E=null,T=-1,R=5,A=-1;function O(){return!(l.unstable_now()-A<R)}function k(){if(E!==null){var H=l.unstable_now();A=H;var D=!0;try{D=E(!0,H)}finally{D?x():(S=!1,E=null)}}else S=!1}var x;if(typeof p=="function")x=function(){p(k)};else if(typeof MessageChannel!="undefined"){var q=new MessageChannel,I=q.port2;q.port1.onmessage=k,x=function(){I.postMessage(null)}}else x=function(){g(k,0)};function U(H){E=H,S||(S=!0,x())}function F(H,D){T=g(function(){H(l.unstable_now())},D)}l.unstable_IdlePriority=5,l.unstable_ImmediatePriority=1,l.unstable_LowPriority=4,l.unstable_NormalPriority=3,l.unstable_Profiling=null,l.unstable_UserBlockingPriority=2,l.unstable_cancelCallback=function(H){H.callback=null},l.unstable_continueExecution=function(){_||s||(_=!0,U(w))},l.unstable_forceFrameRate=function(H){0>H||125<H||(R=0<H?Math.floor(1e3/H):5)},l.unstable_getCurrentPriorityLevel=function(){return o},l.unstable_getFirstCallbackNode=function(){return e(f)},l.unstable_next=function(H){switch(o){case 1:case 2:case 3:var D=3;break;default:D=o}var L=o;o=D;try{return H()}finally{o=L}},l.unstable_pauseExecution=function(){},l.unstable_requestPaint=function(){},l.unstable_runWithPriority=function(H,D){switch(H){case 1:case 2:case 3:case 4:case 5:break;default:H=3}var L=o;o=H;try{return D()}finally{o=L}},l.unstable_scheduleCallback=function(H,D,L){var P=l.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?P+L:P):L=P,H){case 1:var N=-1;break;case 2:N=250;break;case 5:N=1073741823;break;case 4:N=1e4;break;default:N=5e3}return N=L+N,H={id:d++,callback:D,priorityLevel:H,startTime:L,expirationTime:N,sortIndex:-1},L>P?(H.sortIndex=L,r(y,H),e(f)===null&&H===e(y)&&(m?(v(T),T=-1):m=!0,F(h,L-P))):(H.sortIndex=N,r(f,H),_||s||(_=!0,U(w))),H},l.unstable_shouldYield=O,l.unstable_wrapCallback=function(H){var D=o;return function(){var L=o;o=D;try{return H.apply(this,arguments)}finally{o=L}}}}(At)),At}var In;function Lu(){return In||(In=1,kt.exports=To()),kt.exports}const Po="modulepreload",xo=function(l){return"/"+l},Cn={},ot=function(r,e,t){let n=Promise.resolve();if(e&&e.length>0){let f=function(y){return Promise.all(y.map(d=>Promise.resolve(d).then(u=>({status:"fulfilled",value:u}),u=>({status:"rejected",reason:u}))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),c=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));n=f(e.map(y=>{if(y=xo(y),y in Cn)return;Cn[y]=!0;const d=y.endsWith(".css"),u=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${y}"]${u}`))return;const o=document.createElement("link");if(o.rel=d?"stylesheet":Po,d||(o.as="script"),o.crossOrigin="",o.href=y,c&&o.setAttribute("nonce",c),document.head.appendChild(o),d)return new Promise((s,_)=>{o.addEventListener("load",s),o.addEventListener("error",()=>_(new Error(`Unable to preload CSS for ${y}`)))})}))}function i(a){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=a,window.dispatchEvent(c),!c.defaultPrevented)throw a}return n.then(a=>{for(const c of a||[])c.status==="rejected"&&i(c.reason);return r().catch(i)})};let Ro={data:""},qo=l=>typeof window=="object"?((l?l.querySelector("#_goober"):window._goober)||Object.assign((l||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:l||Ro,jo=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,No=/\/\*[^]*?\*\/|  +/g,Dn=/\n+/g,Re=(l,r)=>{let e="",t="",n="";for(let i in l){let a=l[i];i[0]=="@"?i[1]=="i"?e=i+" "+a+";":t+=i[1]=="f"?Re(a,i):i+"{"+Re(a,i[1]=="k"?"":r)+"}":typeof a=="object"?t+=Re(a,r?r.replace(/([^,])+/g,c=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,f=>/&/.test(f)?f.replace(/&/g,c):c?c+" "+f:f)):i):a!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=Re.p?Re.p(i,a):i+":"+a+";")}return e+(r&&n?r+"{"+n+"}":n)+t},Oe={},Ja=l=>{if(typeof l=="object"){let r="";for(let e in l)r+=e+Ja(l[e]);return r}return l},Lo=(l,r,e,t,n)=>{let i=Ja(l),a=Oe[i]||(Oe[i]=(f=>{let y=0,d=11;for(;y<f.length;)d=101*d+f.charCodeAt(y++)>>>0;return"go"+d})(i));if(!Oe[a]){let f=i!==l?l:(y=>{let d,u,o=[{}];for(;d=jo.exec(y.replace(No,""));)d[4]?o.shift():d[3]?(u=d[3].replace(Dn," ").trim(),o.unshift(o[0][u]=o[0][u]||{})):o[0][d[1]]=d[2].replace(Dn," ").trim();return o[0]})(l);Oe[a]=Re(n?{["@keyframes "+a]:f}:f,e?"":"."+a)}let c=e&&Oe.g?Oe.g:null;return e&&(Oe.g=Oe[a]),((f,y,d,u)=>{u?y.data=y.data.replace(u,f):y.data.indexOf(f)===-1&&(y.data=d?f+y.data:y.data+f)})(Oe[a],r,t,c),a},Io=(l,r,e)=>l.reduce((t,n,i)=>{let a=r[i];if(a&&a.call){let c=a(e),f=c&&c.props&&c.props.className||/^go/.test(c)&&c;a=f?"."+f:c&&typeof c=="object"?c.props?"":Re(c,""):c===!1?"":c}return t+n+(a==null?"":a)},"");function An(l){let r=this||{},e=l.call?l(r.p):l;return Lo(e.unshift?e.raw?Io(e,[].slice.call(arguments,1),r.p):e.reduce((t,n)=>Object.assign(t,n&&n.call?n(r.p):n),{}):e,qo(r.target),r.g,r.o,r.k)}let Ya,dn,vn;An.bind({g:1});let Iu=An.bind({k:1});function Cu(l,r,e,t){Re.p=r,Ya=l,dn=e,vn=t}function Du(l,r){let e=this||{};return function(){let t=arguments;function n(i,a){let c=Object.assign({},i),f=c.className||n.className;e.p=Object.assign({theme:dn&&dn()},c),e.o=/ *go\d+/.test(f),c.className=An.apply(e,t)+(f?" "+f:"");let y=l;return l[0]&&(y=c.as||l,delete c.as),vn&&y[0]&&vn(c),Ya(y,c)}return n}}const Co=l=>{let r;return l?r=l:typeof fetch=="undefined"?r=(...e)=>ot(()=>K(null,null,function*(){const{default:t}=yield Promise.resolve().then(()=>Ze);return{default:t}}),void 0).then(({default:t})=>t(...e)):r=fetch,(...e)=>r(...e)};class Sn extends Error{constructor(r,e="FunctionsError",t){super(r),this.name=e,this.context=t}}class Do extends Sn{constructor(r){super("Failed to send a request to the Edge Function","FunctionsFetchError",r)}}class Mn extends Sn{constructor(r){super("Relay Error invoking the Edge Function","FunctionsRelayError",r)}}class Bn extends Sn{constructor(r){super("Edge Function returned a non-2xx status code","FunctionsHttpError",r)}}var pn;(function(l){l.Any="any",l.ApNortheast1="ap-northeast-1",l.ApNortheast2="ap-northeast-2",l.ApSouth1="ap-south-1",l.ApSoutheast1="ap-southeast-1",l.ApSoutheast2="ap-southeast-2",l.CaCentral1="ca-central-1",l.EuCentral1="eu-central-1",l.EuWest1="eu-west-1",l.EuWest2="eu-west-2",l.EuWest3="eu-west-3",l.SaEast1="sa-east-1",l.UsEast1="us-east-1",l.UsWest1="us-west-1",l.UsWest2="us-west-2"})(pn||(pn={}));var Mo=function(l,r,e,t){function n(i){return i instanceof e?i:new e(function(a){a(i)})}return new(e||(e=Promise))(function(i,a){function c(d){try{y(t.next(d))}catch(u){a(u)}}function f(d){try{y(t.throw(d))}catch(u){a(u)}}function y(d){d.done?i(d.value):n(d.value).then(c,f)}y((t=t.apply(l,r||[])).next())})};class Mu{constructor(r,{headers:e={},customFetch:t,region:n=pn.Any}={}){this.url=r,this.headers=e,this.region=n,this.fetch=Co(t)}setAuth(r){this.headers.Authorization=`Bearer ${r}`}invoke(r,e={}){var t;return Mo(this,void 0,void 0,function*(){try{const{headers:n,method:i,body:a}=e;let c={},{region:f}=e;f||(f=this.region);const y=new URL(`${this.url}/${r}`);f&&f!=="any"&&(c["x-region"]=f,y.searchParams.set("forceFunctionRegion",f));let d;a&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&(typeof Blob!="undefined"&&a instanceof Blob||a instanceof ArrayBuffer?(c["Content-Type"]="application/octet-stream",d=a):typeof a=="string"?(c["Content-Type"]="text/plain",d=a):typeof FormData!="undefined"&&a instanceof FormData?d=a:(c["Content-Type"]="application/json",d=JSON.stringify(a)));const u=yield this.fetch(y.toString(),{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},c),this.headers),n),body:d}).catch(m=>{throw new Do(m)}),o=u.headers.get("x-relay-error");if(o&&o==="true")throw new Mn(u);if(!u.ok)throw new Bn(u);let s=((t=u.headers.get("Content-Type"))!==null&&t!==void 0?t:"text/plain").split(";")[0].trim(),_;return s==="application/json"?_=yield u.json():s==="application/octet-stream"?_=yield u.blob():s==="text/event-stream"?_=u:s==="multipart/form-data"?_=yield u.formData():_=yield u.text(),{data:_,error:null,response:u}}catch(n){return{data:null,error:n,response:n instanceof Bn||n instanceof Mn?n.context:void 0}}})}}var de={},Me={},Be={},Ue={},$e={},Fe={},Bo=function(){if(typeof self!="undefined")return self;if(typeof window!="undefined")return window;if(typeof global!="undefined")return global;throw new Error("unable to locate global object")},Ye=Bo();const Uo=Ye.fetch,$o=Ye.fetch.bind(Ye),Fo=Ye.Headers,Ho=Ye.Request,Ko=Ye.Response,Ze=Object.freeze(Object.defineProperty({__proto__:null,Headers:Fo,Request:Ho,Response:Ko,default:$o,fetch:Uo},Symbol.toStringTag,{value:"Module"})),zo=So(Ze);var ut={},Un;function Za(){if(Un)return ut;Un=1,Object.defineProperty(ut,"__esModule",{value:!0});class l extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}return ut.default=l,ut}var $n;function Qa(){if($n)return Fe;$n=1;var l=Fe&&Fe.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(Fe,"__esModule",{value:!0});const r=l(zo),e=l(Za());class t{constructor(i){this.shouldThrowOnError=!1,this.method=i.method,this.url=i.url,this.headers=i.headers,this.schema=i.schema,this.body=i.body,this.shouldThrowOnError=i.shouldThrowOnError,this.signal=i.signal,this.isMaybeSingle=i.isMaybeSingle,i.fetch?this.fetch=i.fetch:typeof fetch=="undefined"?this.fetch=r.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(i,a){return this.headers=Object.assign({},this.headers),this.headers[i]=a,this}then(i,a){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const c=this.fetch;let f=c(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(y=>K(this,null,function*(){var d,u,o;let s=null,_=null,m=null,g=y.status,v=y.statusText;if(y.ok){if(this.method!=="HEAD"){const w=yield y.text();w===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?_=w:_=JSON.parse(w))}const b=(d=this.headers.Prefer)===null||d===void 0?void 0:d.match(/count=(exact|planned|estimated)/),h=(u=y.headers.get("content-range"))===null||u===void 0?void 0:u.split("/");b&&h&&h.length>1&&(m=parseInt(h[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(_)&&(_.length>1?(s={code:"PGRST116",details:`Results contain ${_.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},_=null,m=null,g=406,v="Not Acceptable"):_.length===1?_=_[0]:_=null)}else{const b=yield y.text();try{s=JSON.parse(b),Array.isArray(s)&&y.status===404&&(_=[],s=null,g=200,v="OK")}catch(h){y.status===404&&b===""?(g=204,v="No Content"):s={message:b}}if(s&&this.isMaybeSingle&&(!((o=s==null?void 0:s.details)===null||o===void 0)&&o.includes("0 rows"))&&(s=null,g=200,v="OK"),s&&this.shouldThrowOnError)throw new e.default(s)}return{error:s,data:_,count:m,status:g,statusText:v}}));return this.shouldThrowOnError||(f=f.catch(y=>{var d,u,o;return{error:{message:`${(d=y==null?void 0:y.name)!==null&&d!==void 0?d:"FetchError"}: ${y==null?void 0:y.message}`,details:`${(u=y==null?void 0:y.stack)!==null&&u!==void 0?u:""}`,hint:"",code:`${(o=y==null?void 0:y.code)!==null&&o!==void 0?o:""}`},data:null,count:null,status:0,statusText:""}})),f.then(i,a)}returns(){return this}overrideTypes(){return this}}return Fe.default=t,Fe}var Fn;function Xa(){if(Fn)return $e;Fn=1;var l=$e&&$e.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty($e,"__esModule",{value:!0});const r=l(Qa());class e extends r.default{select(n){let i=!1;const a=(n!=null?n:"*").split("").map(c=>/\s/.test(c)&&!i?"":(c==='"'&&(i=!i),c)).join("");return this.url.searchParams.set("select",a),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(n,{ascending:i=!0,nullsFirst:a,foreignTable:c,referencedTable:f=c}={}){const y=f?`${f}.order`:"order",d=this.url.searchParams.get(y);return this.url.searchParams.set(y,`${d?`${d},`:""}${n}.${i?"asc":"desc"}${a===void 0?"":a?".nullsfirst":".nullslast"}`),this}limit(n,{foreignTable:i,referencedTable:a=i}={}){const c=typeof a=="undefined"?"limit":`${a}.limit`;return this.url.searchParams.set(c,`${n}`),this}range(n,i,{foreignTable:a,referencedTable:c=a}={}){const f=typeof c=="undefined"?"offset":`${c}.offset`,y=typeof c=="undefined"?"limit":`${c}.limit`;return this.url.searchParams.set(f,`${n}`),this.url.searchParams.set(y,`${i-n+1}`),this}abortSignal(n){return this.signal=n,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:n=!1,verbose:i=!1,settings:a=!1,buffers:c=!1,wal:f=!1,format:y="text"}={}){var d;const u=[n?"analyze":null,i?"verbose":null,a?"settings":null,c?"buffers":null,f?"wal":null].filter(Boolean).join("|"),o=(d=this.headers.Accept)!==null&&d!==void 0?d:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${y}; for="${o}"; options=${u};`,y==="json"?this:this}rollback(){var n;return((n=this.headers.Prefer)!==null&&n!==void 0?n:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return $e.default=e,$e}var Hn;function Tn(){if(Hn)return Ue;Hn=1;var l=Ue&&Ue.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ue,"__esModule",{value:!0});const r=l(Xa());class e extends r.default{eq(n,i){return this.url.searchParams.append(n,`eq.${i}`),this}neq(n,i){return this.url.searchParams.append(n,`neq.${i}`),this}gt(n,i){return this.url.searchParams.append(n,`gt.${i}`),this}gte(n,i){return this.url.searchParams.append(n,`gte.${i}`),this}lt(n,i){return this.url.searchParams.append(n,`lt.${i}`),this}lte(n,i){return this.url.searchParams.append(n,`lte.${i}`),this}like(n,i){return this.url.searchParams.append(n,`like.${i}`),this}likeAllOf(n,i){return this.url.searchParams.append(n,`like(all).{${i.join(",")}}`),this}likeAnyOf(n,i){return this.url.searchParams.append(n,`like(any).{${i.join(",")}}`),this}ilike(n,i){return this.url.searchParams.append(n,`ilike.${i}`),this}ilikeAllOf(n,i){return this.url.searchParams.append(n,`ilike(all).{${i.join(",")}}`),this}ilikeAnyOf(n,i){return this.url.searchParams.append(n,`ilike(any).{${i.join(",")}}`),this}is(n,i){return this.url.searchParams.append(n,`is.${i}`),this}in(n,i){const a=Array.from(new Set(i)).map(c=>typeof c=="string"&&new RegExp("[,()]").test(c)?`"${c}"`:`${c}`).join(",");return this.url.searchParams.append(n,`in.(${a})`),this}contains(n,i){return typeof i=="string"?this.url.searchParams.append(n,`cs.${i}`):Array.isArray(i)?this.url.searchParams.append(n,`cs.{${i.join(",")}}`):this.url.searchParams.append(n,`cs.${JSON.stringify(i)}`),this}containedBy(n,i){return typeof i=="string"?this.url.searchParams.append(n,`cd.${i}`):Array.isArray(i)?this.url.searchParams.append(n,`cd.{${i.join(",")}}`):this.url.searchParams.append(n,`cd.${JSON.stringify(i)}`),this}rangeGt(n,i){return this.url.searchParams.append(n,`sr.${i}`),this}rangeGte(n,i){return this.url.searchParams.append(n,`nxl.${i}`),this}rangeLt(n,i){return this.url.searchParams.append(n,`sl.${i}`),this}rangeLte(n,i){return this.url.searchParams.append(n,`nxr.${i}`),this}rangeAdjacent(n,i){return this.url.searchParams.append(n,`adj.${i}`),this}overlaps(n,i){return typeof i=="string"?this.url.searchParams.append(n,`ov.${i}`):this.url.searchParams.append(n,`ov.{${i.join(",")}}`),this}textSearch(n,i,{config:a,type:c}={}){let f="";c==="plain"?f="pl":c==="phrase"?f="ph":c==="websearch"&&(f="w");const y=a===void 0?"":`(${a})`;return this.url.searchParams.append(n,`${f}fts${y}.${i}`),this}match(n){return Object.entries(n).forEach(([i,a])=>{this.url.searchParams.append(i,`eq.${a}`)}),this}not(n,i,a){return this.url.searchParams.append(n,`not.${i}.${a}`),this}or(n,{foreignTable:i,referencedTable:a=i}={}){const c=a?`${a}.or`:"or";return this.url.searchParams.append(c,`(${n})`),this}filter(n,i,a){return this.url.searchParams.append(n,`${i}.${a}`),this}}return Ue.default=e,Ue}var Kn;function eo(){if(Kn)return Be;Kn=1;var l=Be&&Be.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Be,"__esModule",{value:!0});const r=l(Tn());class e{constructor(n,{headers:i={},schema:a,fetch:c}){this.url=n,this.headers=i,this.schema=a,this.fetch=c}select(n,{head:i=!1,count:a}={}){const c=i?"HEAD":"GET";let f=!1;const y=(n!=null?n:"*").split("").map(d=>/\s/.test(d)&&!f?"":(d==='"'&&(f=!f),d)).join("");return this.url.searchParams.set("select",y),a&&(this.headers.Prefer=`count=${a}`),new r.default({method:c,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(n,{count:i,defaultToNull:a=!0}={}){const c="POST",f=[];if(this.headers.Prefer&&f.push(this.headers.Prefer),i&&f.push(`count=${i}`),a||f.push("missing=default"),this.headers.Prefer=f.join(","),Array.isArray(n)){const y=n.reduce((d,u)=>d.concat(Object.keys(u)),[]);if(y.length>0){const d=[...new Set(y)].map(u=>`"${u}"`);this.url.searchParams.set("columns",d.join(","))}}return new r.default({method:c,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}upsert(n,{onConflict:i,ignoreDuplicates:a=!1,count:c,defaultToNull:f=!0}={}){const y="POST",d=[`resolution=${a?"ignore":"merge"}-duplicates`];if(i!==void 0&&this.url.searchParams.set("on_conflict",i),this.headers.Prefer&&d.push(this.headers.Prefer),c&&d.push(`count=${c}`),f||d.push("missing=default"),this.headers.Prefer=d.join(","),Array.isArray(n)){const u=n.reduce((o,s)=>o.concat(Object.keys(s)),[]);if(u.length>0){const o=[...new Set(u)].map(s=>`"${s}"`);this.url.searchParams.set("columns",o.join(","))}}return new r.default({method:y,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}update(n,{count:i}={}){const a="PATCH",c=[];return this.headers.Prefer&&c.push(this.headers.Prefer),i&&c.push(`count=${i}`),this.headers.Prefer=c.join(","),new r.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}delete({count:n}={}){const i="DELETE",a=[];return n&&a.push(`count=${n}`),this.headers.Prefer&&a.unshift(this.headers.Prefer),this.headers.Prefer=a.join(","),new r.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return Be.default=e,Be}var Xe={},et={},zn;function Go(){return zn||(zn=1,Object.defineProperty(et,"__esModule",{value:!0}),et.version=void 0,et.version="0.0.0-automated"),et}var Gn;function Wo(){if(Gn)return Xe;Gn=1,Object.defineProperty(Xe,"__esModule",{value:!0}),Xe.DEFAULT_HEADERS=void 0;const l=Go();return Xe.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${l.version}`},Xe}var Wn;function Vo(){if(Wn)return Me;Wn=1;var l=Me&&Me.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(Me,"__esModule",{value:!0});const r=l(eo()),e=l(Tn()),t=Wo();class n{constructor(a,{headers:c={},schema:f,fetch:y}={}){this.url=a,this.headers=Object.assign(Object.assign({},t.DEFAULT_HEADERS),c),this.schemaName=f,this.fetch=y}from(a){const c=new URL(`${this.url}/${a}`);return new r.default(c,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(a){return new n(this.url,{headers:this.headers,schema:a,fetch:this.fetch})}rpc(a,c={},{head:f=!1,get:y=!1,count:d}={}){let u;const o=new URL(`${this.url}/rpc/${a}`);let s;f||y?(u=f?"HEAD":"GET",Object.entries(c).filter(([m,g])=>g!==void 0).map(([m,g])=>[m,Array.isArray(g)?`{${g.join(",")}}`:`${g}`]).forEach(([m,g])=>{o.searchParams.append(m,g)})):(u="POST",s=c);const _=Object.assign({},this.headers);return d&&(_.Prefer=`count=${d}`),new e.default({method:u,url:o,headers:_,schema:this.schemaName,body:s,fetch:this.fetch,allowEmpty:!1})}}return Me.default=n,Me}var Vn;function Jo(){if(Vn)return de;Vn=1;var l=de&&de.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(de,"__esModule",{value:!0}),de.PostgrestError=de.PostgrestBuilder=de.PostgrestTransformBuilder=de.PostgrestFilterBuilder=de.PostgrestQueryBuilder=de.PostgrestClient=void 0;const r=l(Vo());de.PostgrestClient=r.default;const e=l(eo());de.PostgrestQueryBuilder=e.default;const t=l(Tn());de.PostgrestFilterBuilder=t.default;const n=l(Xa());de.PostgrestTransformBuilder=n.default;const i=l(Qa());de.PostgrestBuilder=i.default;const a=l(Za());return de.PostgrestError=a.default,de.default={PostgrestClient:r.default,PostgrestQueryBuilder:e.default,PostgrestFilterBuilder:t.default,PostgrestTransformBuilder:n.default,PostgrestBuilder:i.default,PostgrestError:a.default},de}var Yo=Jo();const Zo=Va(Yo),{PostgrestClient:Bu,PostgrestQueryBuilder:Uu,PostgrestFilterBuilder:$u,PostgrestTransformBuilder:Fu,PostgrestBuilder:Hu,PostgrestError:Ku}=Zo;function Qo(){if(typeof WebSocket!="undefined")return WebSocket;if(typeof global.WebSocket!="undefined")return global.WebSocket;if(typeof window.WebSocket!="undefined")return window.WebSocket;if(typeof self.WebSocket!="undefined")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const Xo=Qo(),es="2.11.15",ts=`realtime-js/${es}`,rs="1.0.0",to=1e4,ns=1e3;var nt;(function(l){l[l.connecting=0]="connecting",l[l.open=1]="open",l[l.closing=2]="closing",l[l.closed=3]="closed"})(nt||(nt={}));var pe;(function(l){l.closed="closed",l.errored="errored",l.joined="joined",l.joining="joining",l.leaving="leaving"})(pe||(pe={}));var be;(function(l){l.close="phx_close",l.error="phx_error",l.join="phx_join",l.reply="phx_reply",l.leave="phx_leave",l.access_token="access_token"})(be||(be={}));var yn;(function(l){l.websocket="websocket"})(yn||(yn={}));var Ce;(function(l){l.Connecting="connecting",l.Open="open",l.Closing="closing",l.Closed="closed"})(Ce||(Ce={}));class is{constructor(){this.HEADER_LENGTH=1}decode(r,e){return r.constructor===ArrayBuffer?e(this._binaryDecode(r)):e(typeof r=="string"?JSON.parse(r):{})}_binaryDecode(r){const e=new DataView(r),t=new TextDecoder;return this._decodeBroadcast(r,e,t)}_decodeBroadcast(r,e,t){const n=e.getUint8(1),i=e.getUint8(2);let a=this.HEADER_LENGTH+2;const c=t.decode(r.slice(a,a+n));a=a+n;const f=t.decode(r.slice(a,a+i));a=a+i;const y=JSON.parse(t.decode(r.slice(a,r.byteLength)));return{ref:null,topic:c,event:f,payload:y}}}class ro{constructor(r,e){this.callback=r,this.timerCalc=e,this.timer=void 0,this.tries=0,this.callback=r,this.timerCalc=e}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var ue;(function(l){l.abstime="abstime",l.bool="bool",l.date="date",l.daterange="daterange",l.float4="float4",l.float8="float8",l.int2="int2",l.int4="int4",l.int4range="int4range",l.int8="int8",l.int8range="int8range",l.json="json",l.jsonb="jsonb",l.money="money",l.numeric="numeric",l.oid="oid",l.reltime="reltime",l.text="text",l.time="time",l.timestamp="timestamp",l.timestamptz="timestamptz",l.timetz="timetz",l.tsrange="tsrange",l.tstzrange="tstzrange"})(ue||(ue={}));const Jn=(l,r,e={})=>{var t;const n=(t=e.skipTypes)!==null&&t!==void 0?t:[];return Object.keys(r).reduce((i,a)=>(i[a]=as(a,l,r,n),i),{})},as=(l,r,e,t)=>{const n=r.find(c=>c.name===l),i=n==null?void 0:n.type,a=e[l];return i&&!t.includes(i)?no(i,a):gn(a)},no=(l,r)=>{if(l.charAt(0)==="_"){const e=l.slice(1,l.length);return us(r,e)}switch(l){case ue.bool:return os(r);case ue.float4:case ue.float8:case ue.int2:case ue.int4:case ue.int8:case ue.numeric:case ue.oid:return ss(r);case ue.json:case ue.jsonb:return ls(r);case ue.timestamp:return cs(r);case ue.abstime:case ue.date:case ue.daterange:case ue.int4range:case ue.int8range:case ue.money:case ue.reltime:case ue.text:case ue.time:case ue.timestamptz:case ue.timetz:case ue.tsrange:case ue.tstzrange:return gn(r);default:return gn(r)}},gn=l=>l,os=l=>{switch(l){case"t":return!0;case"f":return!1;default:return l}},ss=l=>{if(typeof l=="string"){const r=parseFloat(l);if(!Number.isNaN(r))return r}return l},ls=l=>{if(typeof l=="string")try{return JSON.parse(l)}catch(r){return l}return l},us=(l,r)=>{if(typeof l!="string")return l;const e=l.length-1,t=l[e];if(l[0]==="{"&&t==="}"){let i;const a=l.slice(1,e);try{i=JSON.parse("["+a+"]")}catch(c){i=a?a.split(","):[]}return i.map(c=>no(r,c))}return l},cs=l=>typeof l=="string"?l.replace(" ","T"):l,io=l=>{let r=l;return r=r.replace(/^ws/i,"http"),r=r.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),r.replace(/\/+$/,"")};class St{constructor(r,e,t={},n=to){this.channel=r,this.event=e,this.payload=t,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(r){this.timeout=r,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(r){this.payload=Object.assign(Object.assign({},this.payload),r)}receive(r,e){var t;return this._hasReceived(r)&&e((t=this.receivedResp)===null||t===void 0?void 0:t.response),this.recHooks.push({status:r,callback:e}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const r=e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)};this.channel._on(this.refEvent,{},r),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(r,e){this.refEvent&&this.channel._trigger(this.refEvent,{status:r,response:e})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:r,response:e}){this.recHooks.filter(t=>t.status===r).forEach(t=>t.callback(e))}_hasReceived(r){return this.receivedResp&&this.receivedResp.status===r}}var Yn;(function(l){l.SYNC="sync",l.JOIN="join",l.LEAVE="leave"})(Yn||(Yn={}));class it{constructor(r,e){this.channel=r,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const t=(e==null?void 0:e.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(t.state,{},n=>{const{onJoin:i,onLeave:a,onSync:c}=this.caller;this.joinRef=this.channel._joinRef(),this.state=it.syncState(this.state,n,i,a),this.pendingDiffs.forEach(f=>{this.state=it.syncDiff(this.state,f,i,a)}),this.pendingDiffs=[],c()}),this.channel._on(t.diff,{},n=>{const{onJoin:i,onLeave:a,onSync:c}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(n):(this.state=it.syncDiff(this.state,n,i,a),c())}),this.onJoin((n,i,a)=>{this.channel._trigger("presence",{event:"join",key:n,currentPresences:i,newPresences:a})}),this.onLeave((n,i,a)=>{this.channel._trigger("presence",{event:"leave",key:n,currentPresences:i,leftPresences:a})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(r,e,t,n){const i=this.cloneDeep(r),a=this.transformState(e),c={},f={};return this.map(i,(y,d)=>{a[y]||(f[y]=d)}),this.map(a,(y,d)=>{const u=i[y];if(u){const o=d.map(g=>g.presence_ref),s=u.map(g=>g.presence_ref),_=d.filter(g=>s.indexOf(g.presence_ref)<0),m=u.filter(g=>o.indexOf(g.presence_ref)<0);_.length>0&&(c[y]=_),m.length>0&&(f[y]=m)}else c[y]=d}),this.syncDiff(i,{joins:c,leaves:f},t,n)}static syncDiff(r,e,t,n){const{joins:i,leaves:a}={joins:this.transformState(e.joins),leaves:this.transformState(e.leaves)};return t||(t=()=>{}),n||(n=()=>{}),this.map(i,(c,f)=>{var y;const d=(y=r[c])!==null&&y!==void 0?y:[];if(r[c]=this.cloneDeep(f),d.length>0){const u=r[c].map(s=>s.presence_ref),o=d.filter(s=>u.indexOf(s.presence_ref)<0);r[c].unshift(...o)}t(c,d,f)}),this.map(a,(c,f)=>{let y=r[c];if(!y)return;const d=f.map(u=>u.presence_ref);y=y.filter(u=>d.indexOf(u.presence_ref)<0),r[c]=y,n(c,y,f),y.length===0&&delete r[c]}),r}static map(r,e){return Object.getOwnPropertyNames(r).map(t=>e(t,r[t]))}static transformState(r){return r=this.cloneDeep(r),Object.getOwnPropertyNames(r).reduce((e,t)=>{const n=r[t];return"metas"in n?e[t]=n.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):e[t]=n,e},{})}static cloneDeep(r){return JSON.parse(JSON.stringify(r))}onJoin(r){this.caller.onJoin=r}onLeave(r){this.caller.onLeave=r}onSync(r){this.caller.onSync=r}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Zn;(function(l){l.ALL="*",l.INSERT="INSERT",l.UPDATE="UPDATE",l.DELETE="DELETE"})(Zn||(Zn={}));var Qn;(function(l){l.BROADCAST="broadcast",l.PRESENCE="presence",l.POSTGRES_CHANGES="postgres_changes",l.SYSTEM="system"})(Qn||(Qn={}));var ke;(function(l){l.SUBSCRIBED="SUBSCRIBED",l.TIMED_OUT="TIMED_OUT",l.CLOSED="CLOSED",l.CHANNEL_ERROR="CHANNEL_ERROR"})(ke||(ke={}));class Pn{constructor(r,e={config:{}},t){this.topic=r,this.params=e,this.socket=t,this.bindings={},this.state=pe.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=r.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},e.config),this.timeout=this.socket.timeout,this.joinPush=new St(this,be.join,this.params,this.timeout),this.rejoinTimer=new ro(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=pe.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(n=>n.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=pe.closed,this.socket._remove(this)}),this._onError(n=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,n),this.state=pe.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=pe.errored,this.rejoinTimer.scheduleTimeout())}),this._on(be.reply,{},(n,i)=>{this._trigger(this._replyEventName(i),n)}),this.presence=new it(this),this.broadcastEndpointURL=io(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(r,e=this.timeout){var t,n;if(this.socket.isConnected()||this.socket.connect(),this.state==pe.closed){const{config:{broadcast:i,presence:a,private:c}}=this.params;this._onError(d=>r==null?void 0:r(ke.CHANNEL_ERROR,d)),this._onClose(()=>r==null?void 0:r(ke.CLOSED));const f={},y={broadcast:i,presence:a,postgres_changes:(n=(t=this.bindings.postgres_changes)===null||t===void 0?void 0:t.map(d=>d.filter))!==null&&n!==void 0?n:[],private:c};this.socket.accessTokenValue&&(f.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:y},f)),this.joinedOnce=!0,this._rejoin(e),this.joinPush.receive("ok",u=>K(this,[u],function*({postgres_changes:d}){var o;if(this.socket.setAuth(),d===void 0){r==null||r(ke.SUBSCRIBED);return}else{const s=this.bindings.postgres_changes,_=(o=s==null?void 0:s.length)!==null&&o!==void 0?o:0,m=[];for(let g=0;g<_;g++){const v=s[g],{filter:{event:p,schema:b,table:h,filter:w}}=v,S=d&&d[g];if(S&&S.event===p&&S.schema===b&&S.table===h&&S.filter===w)m.push(Object.assign(Object.assign({},v),{id:S.id}));else{this.unsubscribe(),this.state=pe.errored,r==null||r(ke.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=m,r&&r(ke.SUBSCRIBED);return}})).receive("error",d=>{this.state=pe.errored,r==null||r(ke.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(d).join(", ")||"error")))}).receive("timeout",()=>{r==null||r(ke.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(t){return K(this,arguments,function*(r,e={}){return yield this.send({type:"presence",event:"track",payload:r},e.timeout||this.timeout)})}untrack(){return K(this,arguments,function*(r={}){return yield this.send({type:"presence",event:"untrack"},r)})}on(r,e,t){return this._on(r,e,t)}send(t){return K(this,arguments,function*(r,e={}){var n,i;if(!this._canPush()&&r.type==="broadcast"){const{event:a,payload:c}=r,y={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:a,payload:c,private:this.private}]})};try{const d=yield this._fetchWithTimeout(this.broadcastEndpointURL,y,(n=e.timeout)!==null&&n!==void 0?n:this.timeout);return yield(i=d.body)===null||i===void 0?void 0:i.cancel(),d.ok?"ok":"error"}catch(d){return d.name==="AbortError"?"timed out":"error"}}else return new Promise(a=>{var c,f,y;const d=this._push(r.type,r,e.timeout||this.timeout);r.type==="broadcast"&&!(!((y=(f=(c=this.params)===null||c===void 0?void 0:c.config)===null||f===void 0?void 0:f.broadcast)===null||y===void 0)&&y.ack)&&a("ok"),d.receive("ok",()=>a("ok")),d.receive("error",()=>a("error")),d.receive("timeout",()=>a("timed out"))})})}updateJoinPayload(r){this.joinPush.updatePayload(r)}unsubscribe(r=this.timeout){this.state=pe.leaving;const e=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(be.close,"leave",this._joinRef())};this.joinPush.destroy();let t=null;return new Promise(n=>{t=new St(this,be.leave,{},r),t.receive("ok",()=>{e(),n("ok")}).receive("timeout",()=>{e(),n("timed out")}).receive("error",()=>{n("error")}),t.send(),this._canPush()||t.trigger("ok",{})}).finally(()=>{t==null||t.destroy()})}teardown(){this.pushBuffer.forEach(r=>r.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}_fetchWithTimeout(r,e,t){return K(this,null,function*(){const n=new AbortController,i=setTimeout(()=>n.abort(),t),a=yield this.socket.fetch(r,Object.assign(Object.assign({},e),{signal:n.signal}));return clearTimeout(i),a})}_push(r,e,t=this.timeout){if(!this.joinedOnce)throw`tried to push '${r}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new St(this,r,e,t);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(r,e,t){return e}_isMember(r){return this.topic===r}_joinRef(){return this.joinPush.ref}_trigger(r,e,t){var n,i;const a=r.toLocaleLowerCase(),{close:c,error:f,leave:y,join:d}=be;if(t&&[c,f,y,d].indexOf(a)>=0&&t!==this._joinRef())return;let o=this._onMessage(a,e,t);if(e&&!o)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?(n=this.bindings.postgres_changes)===null||n===void 0||n.filter(s=>{var _,m,g;return((_=s.filter)===null||_===void 0?void 0:_.event)==="*"||((g=(m=s.filter)===null||m===void 0?void 0:m.event)===null||g===void 0?void 0:g.toLocaleLowerCase())===a}).map(s=>s.callback(o,t)):(i=this.bindings[a])===null||i===void 0||i.filter(s=>{var _,m,g,v,p,b;if(["broadcast","presence","postgres_changes"].includes(a))if("id"in s){const h=s.id,w=(_=s.filter)===null||_===void 0?void 0:_.event;return h&&((m=e.ids)===null||m===void 0?void 0:m.includes(h))&&(w==="*"||(w==null?void 0:w.toLocaleLowerCase())===((g=e.data)===null||g===void 0?void 0:g.type.toLocaleLowerCase()))}else{const h=(p=(v=s==null?void 0:s.filter)===null||v===void 0?void 0:v.event)===null||p===void 0?void 0:p.toLocaleLowerCase();return h==="*"||h===((b=e==null?void 0:e.event)===null||b===void 0?void 0:b.toLocaleLowerCase())}else return s.type.toLocaleLowerCase()===a}).map(s=>{if(typeof o=="object"&&"ids"in o){const _=o.data,{schema:m,table:g,commit_timestamp:v,type:p,errors:b}=_;o=Object.assign(Object.assign({},{schema:m,table:g,commit_timestamp:v,eventType:p,new:{},old:{},errors:b}),this._getPayloadRecords(_))}s.callback(o,t)})}_isClosed(){return this.state===pe.closed}_isJoined(){return this.state===pe.joined}_isJoining(){return this.state===pe.joining}_isLeaving(){return this.state===pe.leaving}_replyEventName(r){return`chan_reply_${r}`}_on(r,e,t){const n=r.toLocaleLowerCase(),i={type:n,filter:e,callback:t};return this.bindings[n]?this.bindings[n].push(i):this.bindings[n]=[i],this}_off(r,e){const t=r.toLocaleLowerCase();return this.bindings[t]=this.bindings[t].filter(n=>{var i;return!(((i=n.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===t&&Pn.isEqual(n.filter,e))}),this}static isEqual(r,e){if(Object.keys(r).length!==Object.keys(e).length)return!1;for(const t in r)if(r[t]!==e[t])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(r){this._on(be.close,{},r)}_onError(r){this._on(be.error,{},e=>r(e))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(r=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=pe.joining,this.joinPush.resend(r))}_getPayloadRecords(r){const e={new:{},old:{}};return(r.type==="INSERT"||r.type==="UPDATE")&&(e.new=Jn(r.columns,r.record)),(r.type==="UPDATE"||r.type==="DELETE")&&(e.old=Jn(r.columns,r.old_record)),e}}const Xn=()=>{},fs=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class zu{constructor(r,e){var t;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=to,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Xn,this.ref=0,this.logger=Xn,this.conn=null,this.sendBuffer=[],this.serializer=new is,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let a;return i?a=i:typeof fetch=="undefined"?a=(...c)=>ot(()=>K(this,null,function*(){const{default:f}=yield Promise.resolve().then(()=>Ze);return{default:f}}),void 0).then(({default:f})=>f(...c)):a=fetch,(...c)=>a(...c)},this.endPoint=`${r}/${yn.websocket}`,this.httpEndpoint=io(r),e!=null&&e.transport?this.transport=e.transport:this.transport=null,e!=null&&e.params&&(this.params=e.params),e!=null&&e.timeout&&(this.timeout=e.timeout),e!=null&&e.logger&&(this.logger=e.logger),(e!=null&&e.logLevel||e!=null&&e.log_level)&&(this.logLevel=e.logLevel||e.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),e!=null&&e.heartbeatIntervalMs&&(this.heartbeatIntervalMs=e.heartbeatIntervalMs);const n=(t=e==null?void 0:e.params)===null||t===void 0?void 0:t.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=e!=null&&e.reconnectAfterMs?e.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=e!=null&&e.encode?e.encode:(i,a)=>a(JSON.stringify(i)),this.decode=e!=null&&e.decode?e.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new ro(()=>K(this,null,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(e==null?void 0:e.fetch),e!=null&&e.worker){if(typeof window!="undefined"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(e==null?void 0:e.worker)||!1,this.workerUrl=e==null?void 0:e.workerUrl}this.accessToken=(e==null?void 0:e.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=Xo),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:rs}))}disconnect(r,e){this.conn&&(this.conn.onclose=function(){},r?this.conn.close(r,e!=null?e:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(t=>t.teardown()))}getChannels(){return this.channels}removeChannel(r){return K(this,null,function*(){const e=yield r.unsubscribe();return this.channels.length===0&&this.disconnect(),e})}removeAllChannels(){return K(this,null,function*(){const r=yield Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),r})}log(r,e,t){this.logger(r,e,t)}connectionState(){switch(this.conn&&this.conn.readyState){case nt.connecting:return Ce.Connecting;case nt.open:return Ce.Open;case nt.closing:return Ce.Closing;default:return Ce.Closed}}isConnected(){return this.connectionState()===Ce.Open}channel(r,e={config:{}}){const t=`realtime:${r}`,n=this.getChannels().find(i=>i.topic===t);if(n)return n;{const i=new Pn(`realtime:${r}`,e,this);return this.channels.push(i),i}}push(r){const{topic:e,event:t,payload:n,ref:i}=r,a=()=>{this.encode(r,c=>{var f;(f=this.conn)===null||f===void 0||f.send(c)})};this.log("push",`${e} ${t} (${i})`,n),this.isConnected()?a():this.sendBuffer.push(a)}setAuth(r=null){return K(this,null,function*(){let e=r||this.accessToken&&(yield this.accessToken())||this.accessTokenValue;this.accessTokenValue!=e&&(this.accessTokenValue=e,this.channels.forEach(t=>{const n={access_token:e,version:ts};e&&t.updateJoinPayload(n),t.joinedOnce&&t._isJoined()&&t._push(be.access_token,{access_token:e})}))})}sendHeartbeat(){return K(this,null,function*(){var r;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(r=this.conn)===null||r===void 0||r.close(ns,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),yield this.setAuth()})}onHeartbeat(r){this.heartbeatCallback=r}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(r=>r()),this.sendBuffer=[])}_makeRef(){let r=this.ref+1;return r===this.ref?this.ref=0:this.ref=r,this.ref.toString()}_leaveOpenTopic(r){let e=this.channels.find(t=>t.topic===r&&(t._isJoined()||t._isJoining()));e&&(this.log("transport",`leaving duplicate topic "${r}"`),e.unsubscribe())}_remove(r){this.channels=this.channels.filter(e=>e.topic!==r.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=r=>this._onConnError(r),this.conn.onmessage=r=>this._onConnMessage(r),this.conn.onclose=r=>this._onConnClose(r))}_onConnMessage(r){this.decode(r.data,e=>{let{topic:t,event:n,payload:i,ref:a}=e;t==="phoenix"&&n==="phx_reply"&&this.heartbeatCallback(e.payload.status=="ok"?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${n} ${a&&"("+a+")"||""}`,i),Array.from(this.channels).filter(c=>c._isMember(t)).forEach(c=>c._trigger(n,i,a)),this.stateChangeCallbacks.message.forEach(c=>c(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(r=>r())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const r=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(r),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{e.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(r){this.log("transport","close",r),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(e=>e(r))}_onConnError(r){this.log("transport",`${r}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(e=>e(r))}_triggerChanError(){this.channels.forEach(r=>r._trigger(be.error))}_appendParams(r,e){if(Object.keys(e).length===0)return r;const t=r.match(/\?/)?"&":"?",n=new URLSearchParams(e);return`${r}${t}${n}`}_workerObjectUrl(r){let e;if(r)e=r;else{const t=new Blob([fs],{type:"application/javascript"});e=URL.createObjectURL(t)}return e}}class xn extends Error{constructor(r){super(r),this.__isStorageError=!0,this.name="StorageError"}}function ve(l){return typeof l=="object"&&l!==null&&"__isStorageError"in l}class hs extends xn{constructor(r,e,t){super(r),this.name="StorageApiError",this.status=e,this.statusCode=t}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}}class mn extends xn{constructor(r,e){super(r),this.name="StorageUnknownError",this.originalError=e}}var ds=function(l,r,e,t){function n(i){return i instanceof e?i:new e(function(a){a(i)})}return new(e||(e=Promise))(function(i,a){function c(d){try{y(t.next(d))}catch(u){a(u)}}function f(d){try{y(t.throw(d))}catch(u){a(u)}}function y(d){d.done?i(d.value):n(d.value).then(c,f)}y((t=t.apply(l,r||[])).next())})};const ao=l=>{let r;return l?r=l:typeof fetch=="undefined"?r=(...e)=>ot(()=>K(null,null,function*(){const{default:t}=yield Promise.resolve().then(()=>Ze);return{default:t}}),void 0).then(({default:t})=>t(...e)):r=fetch,(...e)=>r(...e)},vs=()=>ds(void 0,void 0,void 0,function*(){return typeof Response=="undefined"?(yield ot(()=>Promise.resolve().then(()=>Ze),void 0)).Response:Response}),_n=l=>{if(Array.isArray(l))return l.map(e=>_n(e));if(typeof l=="function"||l!==Object(l))return l;const r={};return Object.entries(l).forEach(([e,t])=>{const n=e.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));r[n]=_n(t)}),r},ps=l=>{if(typeof l!="object"||l===null)return!1;const r=Object.getPrototypeOf(l);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in l)&&!(Symbol.iterator in l)};var De=function(l,r,e,t){function n(i){return i instanceof e?i:new e(function(a){a(i)})}return new(e||(e=Promise))(function(i,a){function c(d){try{y(t.next(d))}catch(u){a(u)}}function f(d){try{y(t.throw(d))}catch(u){a(u)}}function y(d){d.done?i(d.value):n(d.value).then(c,f)}y((t=t.apply(l,r||[])).next())})};const Tt=l=>l.msg||l.message||l.error_description||l.error||JSON.stringify(l),ys=(l,r,e)=>De(void 0,void 0,void 0,function*(){const t=yield vs();l instanceof t&&!(e!=null&&e.noResolveJson)?l.json().then(n=>{const i=l.status||500,a=(n==null?void 0:n.statusCode)||i+"";r(new hs(Tt(n),i,a))}).catch(n=>{r(new mn(Tt(n),n))}):r(new mn(Tt(l),l))}),gs=(l,r,e,t)=>{const n={method:l,headers:(r==null?void 0:r.headers)||{}};return l==="GET"||!t?n:(ps(t)?(n.headers=Object.assign({"Content-Type":"application/json"},r==null?void 0:r.headers),n.body=JSON.stringify(t)):n.body=t,Object.assign(Object.assign({},n),e))};function st(l,r,e,t,n,i){return De(this,void 0,void 0,function*(){return new Promise((a,c)=>{l(e,gs(r,t,n,i)).then(f=>{if(!f.ok)throw f;return t!=null&&t.noResolveJson?f:f.json()}).then(f=>a(f)).catch(f=>ys(f,c,t))})})}function pt(l,r,e,t){return De(this,void 0,void 0,function*(){return st(l,"GET",r,e,t)})}function Ae(l,r,e,t,n){return De(this,void 0,void 0,function*(){return st(l,"POST",r,t,n,e)})}function bn(l,r,e,t,n){return De(this,void 0,void 0,function*(){return st(l,"PUT",r,t,n,e)})}function ms(l,r,e,t){return De(this,void 0,void 0,function*(){return st(l,"HEAD",r,Object.assign(Object.assign({},e),{noResolveJson:!0}),t)})}function oo(l,r,e,t,n){return De(this,void 0,void 0,function*(){return st(l,"DELETE",r,t,n,e)})}var ge=function(l,r,e,t){function n(i){return i instanceof e?i:new e(function(a){a(i)})}return new(e||(e=Promise))(function(i,a){function c(d){try{y(t.next(d))}catch(u){a(u)}}function f(d){try{y(t.throw(d))}catch(u){a(u)}}function y(d){d.done?i(d.value):n(d.value).then(c,f)}y((t=t.apply(l,r||[])).next())})};const _s={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ei={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class bs{constructor(r,e={},t,n){this.url=r,this.headers=e,this.bucketId=t,this.fetch=ao(n)}uploadOrUpdate(r,e,t,n){return ge(this,void 0,void 0,function*(){try{let i;const a=Object.assign(Object.assign({},ei),n);let c=Object.assign(Object.assign({},this.headers),r==="POST"&&{"x-upsert":String(a.upsert)});const f=a.metadata;typeof Blob!="undefined"&&t instanceof Blob?(i=new FormData,i.append("cacheControl",a.cacheControl),f&&i.append("metadata",this.encodeMetadata(f)),i.append("",t)):typeof FormData!="undefined"&&t instanceof FormData?(i=t,i.append("cacheControl",a.cacheControl),f&&i.append("metadata",this.encodeMetadata(f))):(i=t,c["cache-control"]=`max-age=${a.cacheControl}`,c["content-type"]=a.contentType,f&&(c["x-metadata"]=this.toBase64(this.encodeMetadata(f)))),n!=null&&n.headers&&(c=Object.assign(Object.assign({},c),n.headers));const y=this._removeEmptyFolders(e),d=this._getFinalPath(y),u=yield(r=="PUT"?bn:Ae)(this.fetch,`${this.url}/object/${d}`,i,Object.assign({headers:c},a!=null&&a.duplex?{duplex:a.duplex}:{}));return{data:{path:y,id:u.Id,fullPath:u.Key},error:null}}catch(i){if(ve(i))return{data:null,error:i};throw i}})}upload(r,e,t){return ge(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",r,e,t)})}uploadToSignedUrl(r,e,t,n){return ge(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(r),a=this._getFinalPath(i),c=new URL(this.url+`/object/upload/sign/${a}`);c.searchParams.set("token",e);try{let f;const y=Object.assign({upsert:ei.upsert},n),d=Object.assign(Object.assign({},this.headers),{"x-upsert":String(y.upsert)});typeof Blob!="undefined"&&t instanceof Blob?(f=new FormData,f.append("cacheControl",y.cacheControl),f.append("",t)):typeof FormData!="undefined"&&t instanceof FormData?(f=t,f.append("cacheControl",y.cacheControl)):(f=t,d["cache-control"]=`max-age=${y.cacheControl}`,d["content-type"]=y.contentType);const u=yield bn(this.fetch,c.toString(),f,{headers:d});return{data:{path:i,fullPath:u.Key},error:null}}catch(f){if(ve(f))return{data:null,error:f};throw f}})}createSignedUploadUrl(r,e){return ge(this,void 0,void 0,function*(){try{let t=this._getFinalPath(r);const n=Object.assign({},this.headers);e!=null&&e.upsert&&(n["x-upsert"]="true");const i=yield Ae(this.fetch,`${this.url}/object/upload/sign/${t}`,{},{headers:n}),a=new URL(this.url+i.url),c=a.searchParams.get("token");if(!c)throw new xn("No token returned by API");return{data:{signedUrl:a.toString(),path:r,token:c},error:null}}catch(t){if(ve(t))return{data:null,error:t};throw t}})}update(r,e,t){return ge(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",r,e,t)})}move(r,e,t){return ge(this,void 0,void 0,function*(){try{return{data:yield Ae(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:r,destinationKey:e,destinationBucket:t==null?void 0:t.destinationBucket},{headers:this.headers}),error:null}}catch(n){if(ve(n))return{data:null,error:n};throw n}})}copy(r,e,t){return ge(this,void 0,void 0,function*(){try{return{data:{path:(yield Ae(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:r,destinationKey:e,destinationBucket:t==null?void 0:t.destinationBucket},{headers:this.headers})).Key},error:null}}catch(n){if(ve(n))return{data:null,error:n};throw n}})}createSignedUrl(r,e,t){return ge(this,void 0,void 0,function*(){try{let n=this._getFinalPath(r),i=yield Ae(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:e},t!=null&&t.transform?{transform:t.transform}:{}),{headers:this.headers});const a=t!=null&&t.download?`&download=${t.download===!0?"":t.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${a}`)},{data:i,error:null}}catch(n){if(ve(n))return{data:null,error:n};throw n}})}createSignedUrls(r,e,t){return ge(this,void 0,void 0,function*(){try{const n=yield Ae(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:e,paths:r},{headers:this.headers}),i=t!=null&&t.download?`&download=${t.download===!0?"":t.download}`:"";return{data:n.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${i}`):null})),error:null}}catch(n){if(ve(n))return{data:null,error:n};throw n}})}download(r,e){return ge(this,void 0,void 0,function*(){const n=typeof(e==null?void 0:e.transform)!="undefined"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((e==null?void 0:e.transform)||{}),a=i?`?${i}`:"";try{const c=this._getFinalPath(r);return{data:yield(yield pt(this.fetch,`${this.url}/${n}/${c}${a}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(c){if(ve(c))return{data:null,error:c};throw c}})}info(r){return ge(this,void 0,void 0,function*(){const e=this._getFinalPath(r);try{const t=yield pt(this.fetch,`${this.url}/object/info/${e}`,{headers:this.headers});return{data:_n(t),error:null}}catch(t){if(ve(t))return{data:null,error:t};throw t}})}exists(r){return ge(this,void 0,void 0,function*(){const e=this._getFinalPath(r);try{return yield ms(this.fetch,`${this.url}/object/${e}`,{headers:this.headers}),{data:!0,error:null}}catch(t){if(ve(t)&&t instanceof mn){const n=t.originalError;if([400,404].includes(n==null?void 0:n.status))return{data:!1,error:t}}throw t}})}getPublicUrl(r,e){const t=this._getFinalPath(r),n=[],i=e!=null&&e.download?`download=${e.download===!0?"":e.download}`:"";i!==""&&n.push(i);const c=typeof(e==null?void 0:e.transform)!="undefined"?"render/image":"object",f=this.transformOptsToQueryString((e==null?void 0:e.transform)||{});f!==""&&n.push(f);let y=n.join("&");return y!==""&&(y=`?${y}`),{data:{publicUrl:encodeURI(`${this.url}/${c}/public/${t}${y}`)}}}remove(r){return ge(this,void 0,void 0,function*(){try{return{data:yield oo(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:r},{headers:this.headers}),error:null}}catch(e){if(ve(e))return{data:null,error:e};throw e}})}list(r,e,t){return ge(this,void 0,void 0,function*(){try{const n=Object.assign(Object.assign(Object.assign({},_s),e),{prefix:r||""});return{data:yield Ae(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},t),error:null}}catch(n){if(ve(n))return{data:null,error:n};throw n}})}encodeMetadata(r){return JSON.stringify(r)}toBase64(r){return typeof Buffer!="undefined"?Buffer.from(r).toString("base64"):btoa(r)}_getFinalPath(r){return`${this.bucketId}/${r.replace(/^\/+/,"")}`}_removeEmptyFolders(r){return r.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(r){const e=[];return r.width&&e.push(`width=${r.width}`),r.height&&e.push(`height=${r.height}`),r.resize&&e.push(`resize=${r.resize}`),r.format&&e.push(`format=${r.format}`),r.quality&&e.push(`quality=${r.quality}`),e.join("&")}}const ws="2.10.4",Os={"X-Client-Info":`storage-js/${ws}`};var He=function(l,r,e,t){function n(i){return i instanceof e?i:new e(function(a){a(i)})}return new(e||(e=Promise))(function(i,a){function c(d){try{y(t.next(d))}catch(u){a(u)}}function f(d){try{y(t.throw(d))}catch(u){a(u)}}function y(d){d.done?i(d.value):n(d.value).then(c,f)}y((t=t.apply(l,r||[])).next())})};class Es{constructor(r,e={},t,n){const i=new URL(r);n!=null&&n.useNewHostname&&/supabase\.(co|in|red)$/.test(i.hostname)&&!i.hostname.includes("storage.supabase.")&&(i.hostname=i.hostname.replace("supabase.","storage.supabase.")),this.url=i.href,this.headers=Object.assign(Object.assign({},Os),e),this.fetch=ao(t)}listBuckets(){return He(this,void 0,void 0,function*(){try{return{data:yield pt(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(r){if(ve(r))return{data:null,error:r};throw r}})}getBucket(r){return He(this,void 0,void 0,function*(){try{return{data:yield pt(this.fetch,`${this.url}/bucket/${r}`,{headers:this.headers}),error:null}}catch(e){if(ve(e))return{data:null,error:e};throw e}})}createBucket(r,e={public:!1}){return He(this,void 0,void 0,function*(){try{return{data:yield Ae(this.fetch,`${this.url}/bucket`,{id:r,name:r,type:e.type,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers}),error:null}}catch(t){if(ve(t))return{data:null,error:t};throw t}})}updateBucket(r,e){return He(this,void 0,void 0,function*(){try{return{data:yield bn(this.fetch,`${this.url}/bucket/${r}`,{id:r,name:r,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers}),error:null}}catch(t){if(ve(t))return{data:null,error:t};throw t}})}emptyBucket(r){return He(this,void 0,void 0,function*(){try{return{data:yield Ae(this.fetch,`${this.url}/bucket/${r}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(ve(e))return{data:null,error:e};throw e}})}deleteBucket(r){return He(this,void 0,void 0,function*(){try{return{data:yield oo(this.fetch,`${this.url}/bucket/${r}`,{},{headers:this.headers}),error:null}}catch(e){if(ve(e))return{data:null,error:e};throw e}})}}class Gu extends Es{constructor(r,e={},t,n){super(r,e,t,n)}from(r){return new bs(this.url,this.headers,r,this.fetch)}}const so="2.71.1",Ve=30*1e3,wn=3,Pt=wn*Ve,ks="http://localhost:9999",As="supabase.auth.token",Ss={"X-Client-Info":`gotrue-js/${so}`},On="X-Supabase-Api-Version",lo={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Ts=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,Ps=600*1e3;class Rn extends Error{constructor(r,e,t){super(r),this.__isAuthError=!0,this.name="AuthError",this.status=e,this.code=t}}function re(l){return typeof l=="object"&&l!==null&&"__isAuthError"in l}class xs extends Rn{constructor(r,e,t){super(r,e,t),this.name="AuthApiError",this.status=e,this.code=t}}function Rs(l){return re(l)&&l.name==="AuthApiError"}class uo extends Rn{constructor(r,e){super(r),this.name="AuthUnknownError",this.originalError=e}}class je extends Rn{constructor(r,e,t,n){super(r,t,n),this.name=e,this.status=t}}class xe extends je{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function qs(l){return re(l)&&l.name==="AuthSessionMissingError"}class ct extends je{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class ft extends je{constructor(r){super(r,"AuthInvalidCredentialsError",400,void 0)}}class ht extends je{constructor(r,e=null){super(r,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=e}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function js(l){return re(l)&&l.name==="AuthImplicitGrantRedirectError"}class ti extends je{constructor(r,e=null){super(r,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=e}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class En extends je{constructor(r,e){super(r,"AuthRetryableFetchError",e,void 0)}}function xt(l){return re(l)&&l.name==="AuthRetryableFetchError"}class ri extends je{constructor(r,e,t){super(r,"AuthWeakPasswordError",e,"weak_password"),this.reasons=t}}class kn extends je{constructor(r){super(r,"AuthInvalidJwtError",400,"invalid_jwt")}}const yt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),ni=` 	
\r=`.split(""),Ns=(()=>{const l=new Array(128);for(let r=0;r<l.length;r+=1)l[r]=-1;for(let r=0;r<ni.length;r+=1)l[ni[r].charCodeAt(0)]=-2;for(let r=0;r<yt.length;r+=1)l[yt[r].charCodeAt(0)]=r;return l})();function ii(l,r,e){if(l!==null)for(r.queue=r.queue<<8|l,r.queuedBits+=8;r.queuedBits>=6;){const t=r.queue>>r.queuedBits-6&63;e(yt[t]),r.queuedBits-=6}else if(r.queuedBits>0)for(r.queue=r.queue<<6-r.queuedBits,r.queuedBits=6;r.queuedBits>=6;){const t=r.queue>>r.queuedBits-6&63;e(yt[t]),r.queuedBits-=6}}function co(l,r,e){const t=Ns[l];if(t>-1)for(r.queue=r.queue<<6|t,r.queuedBits+=6;r.queuedBits>=8;)e(r.queue>>r.queuedBits-8&255),r.queuedBits-=8;else{if(t===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(l)}"`)}}function ai(l){const r=[],e=a=>{r.push(String.fromCodePoint(a))},t={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=a=>{Cs(a,t,e)};for(let a=0;a<l.length;a+=1)co(l.charCodeAt(a),n,i);return r.join("")}function Ls(l,r){if(l<=127){r(l);return}else if(l<=2047){r(192|l>>6),r(128|l&63);return}else if(l<=65535){r(224|l>>12),r(128|l>>6&63),r(128|l&63);return}else if(l<=1114111){r(240|l>>18),r(128|l>>12&63),r(128|l>>6&63),r(128|l&63);return}throw new Error(`Unrecognized Unicode codepoint: ${l.toString(16)}`)}function Is(l,r){for(let e=0;e<l.length;e+=1){let t=l.charCodeAt(e);if(t>55295&&t<=56319){const n=(t-55296)*1024&65535;t=(l.charCodeAt(e+1)-56320&65535|n)+65536,e+=1}Ls(t,r)}}function Cs(l,r,e){if(r.utf8seq===0){if(l<=127){e(l);return}for(let t=1;t<6;t+=1)if((l>>7-t&1)===0){r.utf8seq=t;break}if(r.utf8seq===2)r.codepoint=l&31;else if(r.utf8seq===3)r.codepoint=l&15;else if(r.utf8seq===4)r.codepoint=l&7;else throw new Error("Invalid UTF-8 sequence");r.utf8seq-=1}else if(r.utf8seq>0){if(l<=127)throw new Error("Invalid UTF-8 sequence");r.codepoint=r.codepoint<<6|l&63,r.utf8seq-=1,r.utf8seq===0&&e(r.codepoint)}}function Ds(l){const r=[],e={queue:0,queuedBits:0},t=n=>{r.push(n)};for(let n=0;n<l.length;n+=1)co(l.charCodeAt(n),e,t);return new Uint8Array(r)}function Ms(l){const r=[];return Is(l,e=>r.push(e)),new Uint8Array(r)}function Bs(l){const r=[],e={queue:0,queuedBits:0},t=n=>{r.push(n)};return l.forEach(n=>ii(n,e,t)),ii(null,e,t),r.join("")}function Us(l){return Math.round(Date.now()/1e3)+l}function $s(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(l){const r=Math.random()*16|0;return(l=="x"?r:r&3|8).toString(16)})}const _e=()=>typeof window!="undefined"&&typeof document!="undefined",Ne={tested:!1,writable:!1},fo=()=>{if(!_e())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch(r){return!1}if(Ne.tested)return Ne.writable;const l=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(l,l),globalThis.localStorage.removeItem(l),Ne.tested=!0,Ne.writable=!0}catch(r){Ne.tested=!0,Ne.writable=!1}return Ne.writable};function Fs(l){const r={},e=new URL(l);if(e.hash&&e.hash[0]==="#")try{new URLSearchParams(e.hash.substring(1)).forEach((n,i)=>{r[i]=n})}catch(t){}return e.searchParams.forEach((t,n)=>{r[n]=t}),r}const ho=l=>{let r;return l?r=l:typeof fetch=="undefined"?r=(...e)=>ot(()=>K(null,null,function*(){const{default:t}=yield Promise.resolve().then(()=>Ze);return{default:t}}),void 0).then(({default:t})=>t(...e)):r=fetch,(...e)=>r(...e)},Hs=l=>typeof l=="object"&&l!==null&&"status"in l&&"ok"in l&&"json"in l&&typeof l.json=="function",Je=(l,r,e)=>K(null,null,function*(){yield l.setItem(r,JSON.stringify(e))}),Le=(l,r)=>K(null,null,function*(){const e=yield l.getItem(r);if(!e)return null;try{return JSON.parse(e)}catch(t){return e}}),Pe=(l,r)=>K(null,null,function*(){yield l.removeItem(r)});class gt{constructor(){this.promise=new gt.promiseConstructor((r,e)=>{this.resolve=r,this.reject=e})}}gt.promiseConstructor=Promise;function Rt(l){const r=l.split(".");if(r.length!==3)throw new kn("Invalid JWT structure");for(let t=0;t<r.length;t++)if(!Ts.test(r[t]))throw new kn("JWT not in base64url format");return{header:JSON.parse(ai(r[0])),payload:JSON.parse(ai(r[1])),signature:Ds(r[2]),raw:{header:r[0],payload:r[1]}}}function Ks(l){return K(this,null,function*(){return yield new Promise(r=>{setTimeout(()=>r(null),l)})})}function zs(l,r){return new Promise((t,n)=>{K(null,null,function*(){for(let i=0;i<1/0;i++)try{const a=yield l(i);if(!r(i,null,a)){t(a);return}}catch(a){if(!r(i,a)){n(a);return}}})})}function Gs(l){return("0"+l.toString(16)).substr(-2)}function Ws(){const r=new Uint32Array(56);if(typeof crypto=="undefined"){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let n="";for(let i=0;i<56;i++)n+=e.charAt(Math.floor(Math.random()*t));return n}return crypto.getRandomValues(r),Array.from(r,Gs).join("")}function Vs(l){return K(this,null,function*(){const e=new TextEncoder().encode(l),t=yield crypto.subtle.digest("SHA-256",e),n=new Uint8Array(t);return Array.from(n).map(i=>String.fromCharCode(i)).join("")})}function Js(l){return K(this,null,function*(){if(!(typeof crypto!="undefined"&&typeof crypto.subtle!="undefined"&&typeof TextEncoder!="undefined"))return l;const e=yield Vs(l);return btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")})}function Ke(l,r,e=!1){return K(this,null,function*(){const t=Ws();let n=t;e&&(n+="/PASSWORD_RECOVERY"),yield Je(l,`${r}-code-verifier`,n);const i=yield Js(t);return[i,t===i?"plain":"s256"]})}const Ys=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Zs(l){const r=l.headers.get(On);if(!r||!r.match(Ys))return null;try{return new Date(`${r}T00:00:00.0Z`)}catch(e){return null}}function Qs(l){if(!l)throw new Error("Missing exp claim");const r=Math.floor(Date.now()/1e3);if(l<=r)throw new Error("JWT has expired")}function Xs(l){switch(l){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const el=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function ze(l){if(!el.test(l))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function qt(){const l={};return new Proxy(l,{get:(r,e)=>{if(e==="__isUserNotAvailableProxy")return!0;if(typeof e=="symbol"){const t=e.toString();if(t==="Symbol(Symbol.toPrimitive)"||t==="Symbol(Symbol.toStringTag)"||t==="Symbol(util.inspect.custom)")return}throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${e}" property of the session object is not supported. Please use getUser() instead.`)},set:(r,e)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${e}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(r,e)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${e}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}function oi(l){return JSON.parse(JSON.stringify(l))}var tl=function(l,r){var e={};for(var t in l)Object.prototype.hasOwnProperty.call(l,t)&&r.indexOf(t)<0&&(e[t]=l[t]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(l);n<t.length;n++)r.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(l,t[n])&&(e[t[n]]=l[t[n]]);return e};const Ie=l=>l.msg||l.message||l.error_description||l.error||JSON.stringify(l),rl=[502,503,504];function si(l){return K(this,null,function*(){var r;if(!Hs(l))throw new En(Ie(l),0);if(rl.includes(l.status))throw new En(Ie(l),l.status);let e;try{e=yield l.json()}catch(i){throw new uo(Ie(i),i)}let t;const n=Zs(l);if(n&&n.getTime()>=lo["2024-01-01"].timestamp&&typeof e=="object"&&e&&typeof e.code=="string"?t=e.code:typeof e=="object"&&e&&typeof e.error_code=="string"&&(t=e.error_code),t){if(t==="weak_password")throw new ri(Ie(e),l.status,((r=e.weak_password)===null||r===void 0?void 0:r.reasons)||[]);if(t==="session_not_found")throw new xe}else if(typeof e=="object"&&e&&typeof e.weak_password=="object"&&e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.reasons.reduce((i,a)=>i&&typeof a=="string",!0))throw new ri(Ie(e),l.status,e.weak_password.reasons);throw new xs(Ie(e),l.status||500,t)})}const nl=(l,r,e,t)=>{const n={method:l,headers:(r==null?void 0:r.headers)||{}};return l==="GET"?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},r==null?void 0:r.headers),n.body=JSON.stringify(t),Object.assign(Object.assign({},n),e))};function oe(l,r,e,t){return K(this,null,function*(){var n;const i=Object.assign({},t==null?void 0:t.headers);i[On]||(i[On]=lo["2024-01-01"].name),t!=null&&t.jwt&&(i.Authorization=`Bearer ${t.jwt}`);const a=(n=t==null?void 0:t.query)!==null&&n!==void 0?n:{};t!=null&&t.redirectTo&&(a.redirect_to=t.redirectTo);const c=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",f=yield il(l,r,e+c,{headers:i,noResolveJson:t==null?void 0:t.noResolveJson},{},t==null?void 0:t.body);return t!=null&&t.xform?t==null?void 0:t.xform(f):{data:Object.assign({},f),error:null}})}function il(l,r,e,t,n,i){return K(this,null,function*(){const a=nl(r,t,n,i);let c;try{c=yield l(e,Object.assign({},a))}catch(f){throw new En(Ie(f),0)}if(c.ok||(yield si(c)),t!=null&&t.noResolveJson)return c;try{return yield c.json()}catch(f){yield si(f)}})}function Ee(l){var r;let e=null;ll(l)&&(e=Object.assign({},l),l.expires_at||(e.expires_at=Us(l.expires_in)));const t=(r=l.user)!==null&&r!==void 0?r:l;return{data:{session:e,user:t},error:null}}function li(l){const r=Ee(l);return!r.error&&l.weak_password&&typeof l.weak_password=="object"&&Array.isArray(l.weak_password.reasons)&&l.weak_password.reasons.length&&l.weak_password.message&&typeof l.weak_password.message=="string"&&l.weak_password.reasons.reduce((e,t)=>e&&typeof t=="string",!0)&&(r.data.weak_password=l.weak_password),r}function qe(l){var r;return{data:{user:(r=l.user)!==null&&r!==void 0?r:l},error:null}}function al(l){return{data:l,error:null}}function ol(l){const{action_link:r,email_otp:e,hashed_token:t,redirect_to:n,verification_type:i}=l,a=tl(l,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),c={action_link:r,email_otp:e,hashed_token:t,redirect_to:n,verification_type:i},f=Object.assign({},a);return{data:{properties:c,user:f},error:null}}function sl(l){return l}function ll(l){return l.access_token&&l.refresh_token&&l.expires_in}const jt=["global","local","others"];var ul=function(l,r){var e={};for(var t in l)Object.prototype.hasOwnProperty.call(l,t)&&r.indexOf(t)<0&&(e[t]=l[t]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,t=Object.getOwnPropertySymbols(l);n<t.length;n++)r.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(l,t[n])&&(e[t[n]]=l[t[n]]);return e};class cl{constructor({url:r="",headers:e={},fetch:t}){this.url=r,this.headers=e,this.fetch=ho(t),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(t){return K(this,arguments,function*(r,e=jt[0]){if(jt.indexOf(e)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${jt.join(", ")}`);try{return yield oe(this.fetch,"POST",`${this.url}/logout?scope=${e}`,{headers:this.headers,jwt:r,noResolveJson:!0}),{data:null,error:null}}catch(n){if(re(n))return{data:null,error:n};throw n}})}inviteUserByEmail(t){return K(this,arguments,function*(r,e={}){try{return yield oe(this.fetch,"POST",`${this.url}/invite`,{body:{email:r,data:e.data},headers:this.headers,redirectTo:e.redirectTo,xform:qe})}catch(n){if(re(n))return{data:{user:null},error:n};throw n}})}generateLink(r){return K(this,null,function*(){try{const{options:e}=r,t=ul(r,["options"]),n=Object.assign(Object.assign({},t),e);return"newEmail"in t&&(n.new_email=t==null?void 0:t.newEmail,delete n.newEmail),yield oe(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:ol,redirectTo:e==null?void 0:e.redirectTo})}catch(e){if(re(e))return{data:{properties:null,user:null},error:e};throw e}})}createUser(r){return K(this,null,function*(){try{return yield oe(this.fetch,"POST",`${this.url}/admin/users`,{body:r,headers:this.headers,xform:qe})}catch(e){if(re(e))return{data:{user:null},error:e};throw e}})}listUsers(r){return K(this,null,function*(){var e,t,n,i,a,c,f;try{const y={nextPage:null,lastPage:0,total:0},d=yield oe(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(t=(e=r==null?void 0:r.page)===null||e===void 0?void 0:e.toString())!==null&&t!==void 0?t:"",per_page:(i=(n=r==null?void 0:r.perPage)===null||n===void 0?void 0:n.toString())!==null&&i!==void 0?i:""},xform:sl});if(d.error)throw d.error;const u=yield d.json(),o=(a=d.headers.get("x-total-count"))!==null&&a!==void 0?a:0,s=(f=(c=d.headers.get("link"))===null||c===void 0?void 0:c.split(","))!==null&&f!==void 0?f:[];return s.length>0&&(s.forEach(_=>{const m=parseInt(_.split(";")[0].split("=")[1].substring(0,1)),g=JSON.parse(_.split(";")[1].split("=")[1]);y[`${g}Page`]=m}),y.total=parseInt(o)),{data:Object.assign(Object.assign({},u),y),error:null}}catch(y){if(re(y))return{data:{users:[]},error:y};throw y}})}getUserById(r){return K(this,null,function*(){ze(r);try{return yield oe(this.fetch,"GET",`${this.url}/admin/users/${r}`,{headers:this.headers,xform:qe})}catch(e){if(re(e))return{data:{user:null},error:e};throw e}})}updateUserById(r,e){return K(this,null,function*(){ze(r);try{return yield oe(this.fetch,"PUT",`${this.url}/admin/users/${r}`,{body:e,headers:this.headers,xform:qe})}catch(t){if(re(t))return{data:{user:null},error:t};throw t}})}deleteUser(r,e=!1){return K(this,null,function*(){ze(r);try{return yield oe(this.fetch,"DELETE",`${this.url}/admin/users/${r}`,{headers:this.headers,body:{should_soft_delete:e},xform:qe})}catch(t){if(re(t))return{data:{user:null},error:t};throw t}})}_listFactors(r){return K(this,null,function*(){ze(r.userId);try{const{data:e,error:t}=yield oe(this.fetch,"GET",`${this.url}/admin/users/${r.userId}/factors`,{headers:this.headers,xform:n=>({data:{factors:n},error:null})});return{data:e,error:t}}catch(e){if(re(e))return{data:null,error:e};throw e}})}_deleteFactor(r){return K(this,null,function*(){ze(r.userId),ze(r.id);try{return{data:yield oe(this.fetch,"DELETE",`${this.url}/admin/users/${r.userId}/factors/${r.id}`,{headers:this.headers}),error:null}}catch(e){if(re(e))return{data:null,error:e};throw e}})}}function ui(l={}){return{getItem:r=>l[r]||null,setItem:(r,e)=>{l[r]=e},removeItem:r=>{delete l[r]}}}function fl(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(l){typeof self!="undefined"&&(self.globalThis=self)}}const Ge={debug:!!(globalThis&&fo()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class vo extends Error{constructor(r){super(r),this.isAcquireTimeout=!0}}class hl extends vo{}function dl(l,r,e){return K(this,null,function*(){Ge.debug;const t=new globalThis.AbortController;return r>0&&setTimeout(()=>{t.abort(),Ge.debug},r),yield Promise.resolve().then(()=>globalThis.navigator.locks.request(l,r===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:t.signal},n=>K(null,null,function*(){if(n){Ge.debug;try{return yield e()}finally{Ge.debug}}else{if(r===0)throw Ge.debug,new hl(`Acquiring an exclusive Navigator LockManager lock "${l}" immediately failed`);if(Ge.debug)try{const i=yield globalThis.navigator.locks.query()}catch(i){}return yield e()}})))})}fl();const vl={url:ks,storageKey:As,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Ss,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function ci(l,r,e){return K(this,null,function*(){return yield e()})}const We={};class at{constructor(r){var e,t;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=at.nextInstanceID,at.nextInstanceID+=1,this.instanceID>0&&_e();const n=Object.assign(Object.assign({},vl),r);if(this.logDebugMessages=!!n.debug,typeof n.debug=="function"&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new cl({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=ho(n.fetch),this.lock=n.lock||ci,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:_e()&&(!((e=globalThis==null?void 0:globalThis.navigator)===null||e===void 0)&&e.locks)?this.lock=dl:this.lock=ci,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(n.storage?this.storage=n.storage:fo()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=ui(this.memoryStorage)),n.userStorage&&(this.userStorage=n.userStorage)):(this.memoryStorage={},this.storage=ui(this.memoryStorage)),_e()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){}(t=this.broadcastChannel)===null||t===void 0||t.addEventListener("message",i=>K(this,null,function*(){this._debug("received broadcast notification from other tab or client",i),yield this._notifyAllSubscribers(i.data.event,i.data.session,!1)}))}this.initialize()}get jwks(){var r,e;return(e=(r=We[this.storageKey])===null||r===void 0?void 0:r.jwks)!==null&&e!==void 0?e:{keys:[]}}set jwks(r){We[this.storageKey]=Object.assign(Object.assign({},We[this.storageKey]),{jwks:r})}get jwks_cached_at(){var r,e;return(e=(r=We[this.storageKey])===null||r===void 0?void 0:r.cachedAt)!==null&&e!==void 0?e:Number.MIN_SAFE_INTEGER}set jwks_cached_at(r){We[this.storageKey]=Object.assign(Object.assign({},We[this.storageKey]),{cachedAt:r})}_debug(...r){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${so}) ${new Date().toISOString()}`,...r),this}initialize(){return K(this,null,function*(){return this.initializePromise?yield this.initializePromise:(this.initializePromise=K(this,null,function*(){return yield this._acquireLock(-1,()=>K(this,null,function*(){return yield this._initialize()}))}),yield this.initializePromise)})}_initialize(){return K(this,null,function*(){var r;try{const e=Fs(window.location.href);let t="none";if(this._isImplicitGrantCallback(e)?t="implicit":(yield this._isPKCECallback(e))&&(t="pkce"),_e()&&this.detectSessionInUrl&&t!=="none"){const{data:n,error:i}=yield this._getSessionFromURL(e,t);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),js(i)){const f=(r=i.details)===null||r===void 0?void 0:r.code;if(f==="identity_already_exists"||f==="identity_not_found"||f==="single_identity_not_deletable")return{error:i}}return yield this._removeSession(),{error:i}}const{session:a,redirectType:c}=n;return this._debug("#_initialize()","detected session in URL",a,"redirect type",c),yield this._saveSession(a),setTimeout(()=>K(this,null,function*(){c==="recovery"?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",a):yield this._notifyAllSubscribers("SIGNED_IN",a)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(e){return re(e)?{error:e}:{error:new uo("Unexpected error during initialization",e)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signInAnonymously(r){return K(this,null,function*(){var e,t,n;try{const i=yield oe(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(t=(e=r==null?void 0:r.options)===null||e===void 0?void 0:e.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:(n=r==null?void 0:r.options)===null||n===void 0?void 0:n.captchaToken}},xform:Ee}),{data:a,error:c}=i;if(c||!a)return{data:{user:null,session:null},error:c};const f=a.session,y=a.user;return a.session&&(yield this._saveSession(a.session),yield this._notifyAllSubscribers("SIGNED_IN",f)),{data:{user:y,session:f},error:null}}catch(i){if(re(i))return{data:{user:null,session:null},error:i};throw i}})}signUp(r){return K(this,null,function*(){var e,t,n;try{let i;if("email"in r){const{email:d,password:u,options:o}=r;let s=null,_=null;this.flowType==="pkce"&&([s,_]=yield Ke(this.storage,this.storageKey)),i=yield oe(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:o==null?void 0:o.emailRedirectTo,body:{email:d,password:u,data:(e=o==null?void 0:o.data)!==null&&e!==void 0?e:{},gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken},code_challenge:s,code_challenge_method:_},xform:Ee})}else if("phone"in r){const{phone:d,password:u,options:o}=r;i=yield oe(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:d,password:u,data:(t=o==null?void 0:o.data)!==null&&t!==void 0?t:{},channel:(n=o==null?void 0:o.channel)!==null&&n!==void 0?n:"sms",gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},xform:Ee})}else throw new ft("You must provide either an email or phone number and a password");const{data:a,error:c}=i;if(c||!a)return{data:{user:null,session:null},error:c};const f=a.session,y=a.user;return a.session&&(yield this._saveSession(a.session),yield this._notifyAllSubscribers("SIGNED_IN",f)),{data:{user:y,session:f},error:null}}catch(i){if(re(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithPassword(r){return K(this,null,function*(){try{let e;if("email"in r){const{email:i,password:a,options:c}=r;e=yield oe(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:a,gotrue_meta_security:{captcha_token:c==null?void 0:c.captchaToken}},xform:li})}else if("phone"in r){const{phone:i,password:a,options:c}=r;e=yield oe(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:a,gotrue_meta_security:{captcha_token:c==null?void 0:c.captchaToken}},xform:li})}else throw new ft("You must provide either an email or phone number and a password");const{data:t,error:n}=e;return n?{data:{user:null,session:null},error:n}:!t||!t.session||!t.user?{data:{user:null,session:null},error:new ct}:(t.session&&(yield this._saveSession(t.session),yield this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({user:t.user,session:t.session},t.weak_password?{weakPassword:t.weak_password}:null),error:n})}catch(e){if(re(e))return{data:{user:null,session:null},error:e};throw e}})}signInWithOAuth(r){return K(this,null,function*(){var e,t,n,i;return yield this._handleProviderSignIn(r.provider,{redirectTo:(e=r.options)===null||e===void 0?void 0:e.redirectTo,scopes:(t=r.options)===null||t===void 0?void 0:t.scopes,queryParams:(n=r.options)===null||n===void 0?void 0:n.queryParams,skipBrowserRedirect:(i=r.options)===null||i===void 0?void 0:i.skipBrowserRedirect})})}exchangeCodeForSession(r){return K(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>K(this,null,function*(){return this._exchangeCodeForSession(r)}))})}signInWithWeb3(r){return K(this,null,function*(){const{chain:e}=r;if(e==="solana")return yield this.signInWithSolana(r);throw new Error(`@supabase/auth-js: Unsupported chain "${e}"`)})}signInWithSolana(r){return K(this,null,function*(){var e,t,n,i,a,c,f,y,d,u,o,s;let _,m;if("message"in r)_=r.message,m=r.signature;else{const{chain:g,wallet:v,statement:p,options:b}=r;let h;if(_e())if(typeof v=="object")h=v;else{const S=window;if("solana"in S&&typeof S.solana=="object"&&("signIn"in S.solana&&typeof S.solana.signIn=="function"||"signMessage"in S.solana&&typeof S.solana.signMessage=="function"))h=S.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof v!="object"||!(b!=null&&b.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");h=v}const w=new URL((e=b==null?void 0:b.url)!==null&&e!==void 0?e:window.location.href);if("signIn"in h&&h.signIn){const S=yield h.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},b==null?void 0:b.signInWithSolana),{version:"1",domain:w.host,uri:w.href}),p?{statement:p}:null));let E;if(Array.isArray(S)&&S[0]&&typeof S[0]=="object")E=S[0];else if(S&&typeof S=="object"&&"signedMessage"in S&&"signature"in S)E=S;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in E&&"signature"in E&&(typeof E.signedMessage=="string"||E.signedMessage instanceof Uint8Array)&&E.signature instanceof Uint8Array)_=typeof E.signedMessage=="string"?E.signedMessage:new TextDecoder().decode(E.signedMessage),m=E.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in h)||typeof h.signMessage!="function"||!("publicKey"in h)||typeof h!="object"||!h.publicKey||!("toBase58"in h.publicKey)||typeof h.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");_=[`${w.host} wants you to sign in with your Solana account:`,h.publicKey.toBase58(),...p?["",p,""]:[""],"Version: 1",`URI: ${w.href}`,`Issued At: ${(n=(t=b==null?void 0:b.signInWithSolana)===null||t===void 0?void 0:t.issuedAt)!==null&&n!==void 0?n:new Date().toISOString()}`,...!((i=b==null?void 0:b.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${b.signInWithSolana.notBefore}`]:[],...!((a=b==null?void 0:b.signInWithSolana)===null||a===void 0)&&a.expirationTime?[`Expiration Time: ${b.signInWithSolana.expirationTime}`]:[],...!((c=b==null?void 0:b.signInWithSolana)===null||c===void 0)&&c.chainId?[`Chain ID: ${b.signInWithSolana.chainId}`]:[],...!((f=b==null?void 0:b.signInWithSolana)===null||f===void 0)&&f.nonce?[`Nonce: ${b.signInWithSolana.nonce}`]:[],...!((y=b==null?void 0:b.signInWithSolana)===null||y===void 0)&&y.requestId?[`Request ID: ${b.signInWithSolana.requestId}`]:[],...!((u=(d=b==null?void 0:b.signInWithSolana)===null||d===void 0?void 0:d.resources)===null||u===void 0)&&u.length?["Resources",...b.signInWithSolana.resources.map(E=>`- ${E}`)]:[]].join(`
`);const S=yield h.signMessage(new TextEncoder().encode(_),"utf8");if(!S||!(S instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");m=S}}try{const{data:g,error:v}=yield oe(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:_,signature:Bs(m)},!((o=r.options)===null||o===void 0)&&o.captchaToken?{gotrue_meta_security:{captcha_token:(s=r.options)===null||s===void 0?void 0:s.captchaToken}}:null),xform:Ee});if(v)throw v;return!g||!g.session||!g.user?{data:{user:null,session:null},error:new ct}:(g.session&&(yield this._saveSession(g.session),yield this._notifyAllSubscribers("SIGNED_IN",g.session)),{data:Object.assign({},g),error:v})}catch(g){if(re(g))return{data:{user:null,session:null},error:g};throw g}})}_exchangeCodeForSession(r){return K(this,null,function*(){const e=yield Le(this.storage,`${this.storageKey}-code-verifier`),[t,n]=(e!=null?e:"").split("/");try{const{data:i,error:a}=yield oe(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:r,code_verifier:t},xform:Ee});if(yield Pe(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new ct}:(i.session&&(yield this._saveSession(i.session),yield this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:n!=null?n:null}),error:a})}catch(i){if(re(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}})}signInWithIdToken(r){return K(this,null,function*(){try{const{options:e,provider:t,token:n,access_token:i,nonce:a}=r,c=yield oe(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:t,id_token:n,access_token:i,nonce:a,gotrue_meta_security:{captcha_token:e==null?void 0:e.captchaToken}},xform:Ee}),{data:f,error:y}=c;return y?{data:{user:null,session:null},error:y}:!f||!f.session||!f.user?{data:{user:null,session:null},error:new ct}:(f.session&&(yield this._saveSession(f.session),yield this._notifyAllSubscribers("SIGNED_IN",f.session)),{data:f,error:y})}catch(e){if(re(e))return{data:{user:null,session:null},error:e};throw e}})}signInWithOtp(r){return K(this,null,function*(){var e,t,n,i,a;try{if("email"in r){const{email:c,options:f}=r;let y=null,d=null;this.flowType==="pkce"&&([y,d]=yield Ke(this.storage,this.storageKey));const{error:u}=yield oe(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:c,data:(e=f==null?void 0:f.data)!==null&&e!==void 0?e:{},create_user:(t=f==null?void 0:f.shouldCreateUser)!==null&&t!==void 0?t:!0,gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:y,code_challenge_method:d},redirectTo:f==null?void 0:f.emailRedirectTo});return{data:{user:null,session:null},error:u}}if("phone"in r){const{phone:c,options:f}=r,{data:y,error:d}=yield oe(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:c,data:(n=f==null?void 0:f.data)!==null&&n!==void 0?n:{},create_user:(i=f==null?void 0:f.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},channel:(a=f==null?void 0:f.channel)!==null&&a!==void 0?a:"sms"}});return{data:{user:null,session:null,messageId:y==null?void 0:y.message_id},error:d}}throw new ft("You must provide either an email or phone number.")}catch(c){if(re(c))return{data:{user:null,session:null},error:c};throw c}})}verifyOtp(r){return K(this,null,function*(){var e,t;try{let n,i;"options"in r&&(n=(e=r.options)===null||e===void 0?void 0:e.redirectTo,i=(t=r.options)===null||t===void 0?void 0:t.captchaToken);const{data:a,error:c}=yield oe(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},r),{gotrue_meta_security:{captcha_token:i}}),redirectTo:n,xform:Ee});if(c)throw c;if(!a)throw new Error("An error occurred on token verification.");const f=a.session,y=a.user;return f!=null&&f.access_token&&(yield this._saveSession(f),yield this._notifyAllSubscribers(r.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",f)),{data:{user:y,session:f},error:null}}catch(n){if(re(n))return{data:{user:null,session:null},error:n};throw n}})}signInWithSSO(r){return K(this,null,function*(){var e,t,n;try{let i=null,a=null;return this.flowType==="pkce"&&([i,a]=yield Ke(this.storage,this.storageKey)),yield oe(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in r?{provider_id:r.providerId}:null),"domain"in r?{domain:r.domain}:null),{redirect_to:(t=(e=r.options)===null||e===void 0?void 0:e.redirectTo)!==null&&t!==void 0?t:void 0}),!((n=r==null?void 0:r.options)===null||n===void 0)&&n.captchaToken?{gotrue_meta_security:{captcha_token:r.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:a}),headers:this.headers,xform:al})}catch(i){if(re(i))return{data:null,error:i};throw i}})}reauthenticate(){return K(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return K(this,null,function*(){try{return yield this._useSession(r=>K(this,null,function*(){const{data:{session:e},error:t}=r;if(t)throw t;if(!e)throw new xe;const{error:n}=yield oe(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:e.access_token});return{data:{user:null,session:null},error:n}}))}catch(r){if(re(r))return{data:{user:null,session:null},error:r};throw r}})}resend(r){return K(this,null,function*(){try{const e=`${this.url}/resend`;if("email"in r){const{email:t,type:n,options:i}=r,{error:a}=yield oe(this.fetch,"POST",e,{headers:this.headers,body:{email:t,type:n,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}else if("phone"in r){const{phone:t,type:n,options:i}=r,{data:a,error:c}=yield oe(this.fetch,"POST",e,{headers:this.headers,body:{phone:t,type:n,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:a==null?void 0:a.message_id},error:c}}throw new ft("You must provide either an email or phone number and a type")}catch(e){if(re(e))return{data:{user:null,session:null},error:e};throw e}})}getSession(){return K(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){return this._useSession(e=>K(this,null,function*(){return e}))}))})}_acquireLock(r,e){return K(this,null,function*(){this._debug("#_acquireLock","begin",r);try{if(this.lockAcquired){const t=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),n=K(this,null,function*(){return yield t,yield e()});return this.pendingInLock.push(K(this,null,function*(){try{yield n}catch(i){}})),n}return yield this.lock(`lock:${this.storageKey}`,r,()=>K(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const t=e();for(this.pendingInLock.push(K(this,null,function*(){try{yield t}catch(n){}})),yield t;this.pendingInLock.length;){const n=[...this.pendingInLock];yield Promise.all(n),this.pendingInLock.splice(0,n.length)}return yield t}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(r){return K(this,null,function*(){this._debug("#_useSession","begin");try{const e=yield this.__loadSession();return yield r(e)}finally{this._debug("#_useSession","end")}})}__loadSession(){return K(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let r=null;const e=yield Le(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",e),e!==null&&(this._isValidSession(e)?r=e:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!r)return{data:{session:null},error:null};const t=r.expires_at?r.expires_at*1e3-Date.now()<Pt:!1;if(this._debug("#__loadSession()",`session has${t?"":" not"} expired`,"expires_at",r.expires_at),!t){if(this.userStorage){const a=yield Le(this.userStorage,this.storageKey+"-user");a!=null&&a.user?r.user=a.user:r.user=qt()}if(this.storage.isServer&&r.user){let a=this.suppressGetSessionWarning;r=new Proxy(r,{get:(f,y,d)=>(!a&&y==="user"&&(a=!0,this.suppressGetSessionWarning=!0),Reflect.get(f,y,d))})}return{data:{session:r},error:null}}const{session:n,error:i}=yield this._callRefreshToken(r.refresh_token);return i?{data:{session:null},error:i}:{data:{session:n},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(r){return K(this,null,function*(){return r?yield this._getUser(r):(yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){return yield this._getUser()})))})}_getUser(r){return K(this,null,function*(){try{return r?yield oe(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:r,xform:qe}):yield this._useSession(e=>K(this,null,function*(){var t,n,i;const{data:a,error:c}=e;if(c)throw c;return!(!((t=a.session)===null||t===void 0)&&t.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new xe}:yield oe(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(n=a.session)===null||n===void 0?void 0:n.access_token)!==null&&i!==void 0?i:void 0,xform:qe})}))}catch(e){if(re(e))return qs(e)&&(yield this._removeSession(),yield Pe(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}})}updateUser(t){return K(this,arguments,function*(r,e={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){return yield this._updateUser(r,e)}))})}_updateUser(t){return K(this,arguments,function*(r,e={}){try{return yield this._useSession(n=>K(this,null,function*(){const{data:i,error:a}=n;if(a)throw a;if(!i.session)throw new xe;const c=i.session;let f=null,y=null;this.flowType==="pkce"&&r.email!=null&&([f,y]=yield Ke(this.storage,this.storageKey));const{data:d,error:u}=yield oe(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:e==null?void 0:e.emailRedirectTo,body:Object.assign(Object.assign({},r),{code_challenge:f,code_challenge_method:y}),jwt:c.access_token,xform:qe});if(u)throw u;return c.user=d.user,yield this._saveSession(c),yield this._notifyAllSubscribers("USER_UPDATED",c),{data:{user:c.user},error:null}}))}catch(n){if(re(n))return{data:{user:null},error:n};throw n}})}setSession(r){return K(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){return yield this._setSession(r)}))})}_setSession(r){return K(this,null,function*(){try{if(!r.access_token||!r.refresh_token)throw new xe;const e=Date.now()/1e3;let t=e,n=!0,i=null;const{payload:a}=Rt(r.access_token);if(a.exp&&(t=a.exp,n=t<=e),n){const{session:c,error:f}=yield this._callRefreshToken(r.refresh_token);if(f)return{data:{user:null,session:null},error:f};if(!c)return{data:{user:null,session:null},error:null};i=c}else{const{data:c,error:f}=yield this._getUser(r.access_token);if(f)throw f;i={access_token:r.access_token,refresh_token:r.refresh_token,user:c.user,token_type:"bearer",expires_in:t-e,expires_at:t},yield this._saveSession(i),yield this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if(re(e))return{data:{session:null,user:null},error:e};throw e}})}refreshSession(r){return K(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){return yield this._refreshSession(r)}))})}_refreshSession(r){return K(this,null,function*(){try{return yield this._useSession(e=>K(this,null,function*(){var t;if(!r){const{data:a,error:c}=e;if(c)throw c;r=(t=a.session)!==null&&t!==void 0?t:void 0}if(!(r!=null&&r.refresh_token))throw new xe;const{session:n,error:i}=yield this._callRefreshToken(r.refresh_token);return i?{data:{user:null,session:null},error:i}:n?{data:{user:n.user,session:n},error:null}:{data:{user:null,session:null},error:null}}))}catch(e){if(re(e))return{data:{user:null,session:null},error:e};throw e}})}_getSessionFromURL(r,e){return K(this,null,function*(){try{if(!_e())throw new ht("No browser detected.");if(r.error||r.error_description||r.error_code)throw new ht(r.error_description||"Error in URL with unspecified error_description",{error:r.error||"unspecified_error",code:r.error_code||"unspecified_code"});switch(e){case"implicit":if(this.flowType==="pkce")throw new ti("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new ht("Not a valid implicit grant flow url.");break;default:}if(e==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!r.code)throw new ti("No code detected.");const{data:p,error:b}=yield this._exchangeCodeForSession(r.code);if(b)throw b;const h=new URL(window.location.href);return h.searchParams.delete("code"),window.history.replaceState(window.history.state,"",h.toString()),{data:{session:p.session,redirectType:null},error:null}}const{provider_token:t,provider_refresh_token:n,access_token:i,refresh_token:a,expires_in:c,expires_at:f,token_type:y}=r;if(!i||!c||!a||!y)throw new ht("No session defined in URL");const d=Math.round(Date.now()/1e3),u=parseInt(c);let o=d+u;f&&(o=parseInt(f)),(o-d)*1e3<=Ve;const _=o-u;d-_>=120||d-_<0;const{data:m,error:g}=yield this._getUser(i);if(g)throw g;const v={provider_token:t,provider_refresh_token:n,access_token:i,expires_in:u,expires_at:o,refresh_token:a,token_type:y,user:m.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:r.type},error:null}}catch(t){if(re(t))return{data:{session:null,redirectType:null},error:t};throw t}})}_isImplicitGrantCallback(r){return!!(r.access_token||r.error_description)}_isPKCECallback(r){return K(this,null,function*(){const e=yield Le(this.storage,`${this.storageKey}-code-verifier`);return!!(r.code&&e)})}signOut(){return K(this,arguments,function*(r={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){return yield this._signOut(r)}))})}_signOut(){return K(this,arguments,function*({scope:r}={scope:"global"}){return yield this._useSession(e=>K(this,null,function*(){var t;const{data:n,error:i}=e;if(i)return{error:i};const a=(t=n.session)===null||t===void 0?void 0:t.access_token;if(a){const{error:c}=yield this.admin.signOut(a,r);if(c&&!(Rs(c)&&(c.status===404||c.status===401||c.status===403)))return{error:c}}return r!=="others"&&(yield this._removeSession(),yield Pe(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))})}onAuthStateChange(r){const e=$s(),t={id:e,callback:r,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",e),this.stateChangeEmitters.delete(e)}};return this._debug("#onAuthStateChange()","registered callback with id",e),this.stateChangeEmitters.set(e,t),K(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){this._emitInitialSession(e)}))}),{data:{subscription:t}}}_emitInitialSession(r){return K(this,null,function*(){return yield this._useSession(e=>K(this,null,function*(){var t,n;try{const{data:{session:i},error:a}=e;if(a)throw a;yield(t=this.stateChangeEmitters.get(r))===null||t===void 0?void 0:t.callback("INITIAL_SESSION",i),this._debug("INITIAL_SESSION","callback id",r,"session",i)}catch(i){yield(n=this.stateChangeEmitters.get(r))===null||n===void 0?void 0:n.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",r,"error",i)}}))})}resetPasswordForEmail(t){return K(this,arguments,function*(r,e={}){let n=null,i=null;this.flowType==="pkce"&&([n,i]=yield Ke(this.storage,this.storageKey,!0));try{return yield oe(this.fetch,"POST",`${this.url}/recover`,{body:{email:r,code_challenge:n,code_challenge_method:i,gotrue_meta_security:{captcha_token:e.captchaToken}},headers:this.headers,redirectTo:e.redirectTo})}catch(a){if(re(a))return{data:null,error:a};throw a}})}getUserIdentities(){return K(this,null,function*(){var r;try{const{data:e,error:t}=yield this.getUser();if(t)throw t;return{data:{identities:(r=e.user.identities)!==null&&r!==void 0?r:[]},error:null}}catch(e){if(re(e))return{data:null,error:e};throw e}})}linkIdentity(r){return K(this,null,function*(){var e;try{const{data:t,error:n}=yield this._useSession(i=>K(this,null,function*(){var a,c,f,y,d;const{data:u,error:o}=i;if(o)throw o;const s=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,r.provider,{redirectTo:(a=r.options)===null||a===void 0?void 0:a.redirectTo,scopes:(c=r.options)===null||c===void 0?void 0:c.scopes,queryParams:(f=r.options)===null||f===void 0?void 0:f.queryParams,skipBrowserRedirect:!0});return yield oe(this.fetch,"GET",s,{headers:this.headers,jwt:(d=(y=u.session)===null||y===void 0?void 0:y.access_token)!==null&&d!==void 0?d:void 0})}));if(n)throw n;return _e()&&!(!((e=r.options)===null||e===void 0)&&e.skipBrowserRedirect)&&window.location.assign(t==null?void 0:t.url),{data:{provider:r.provider,url:t==null?void 0:t.url},error:null}}catch(t){if(re(t))return{data:{provider:r.provider,url:null},error:t};throw t}})}unlinkIdentity(r){return K(this,null,function*(){try{return yield this._useSession(e=>K(this,null,function*(){var t,n;const{data:i,error:a}=e;if(a)throw a;return yield oe(this.fetch,"DELETE",`${this.url}/user/identities/${r.identity_id}`,{headers:this.headers,jwt:(n=(t=i.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:void 0})}))}catch(e){if(re(e))return{data:null,error:e};throw e}})}_refreshAccessToken(r){return K(this,null,function*(){const e=`#_refreshAccessToken(${r.substring(0,5)}...)`;this._debug(e,"begin");try{const t=Date.now();return yield zs(n=>K(this,null,function*(){return n>0&&(yield Ks(200*Math.pow(2,n-1))),this._debug(e,"refreshing attempt",n),yield oe(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:r},headers:this.headers,xform:Ee})}),(n,i)=>{const a=200*Math.pow(2,n);return i&&xt(i)&&Date.now()+a-t<Ve})}catch(t){if(this._debug(e,"error",t),re(t))return{data:{session:null,user:null},error:t};throw t}finally{this._debug(e,"end")}})}_isValidSession(r){return typeof r=="object"&&r!==null&&"access_token"in r&&"refresh_token"in r&&"expires_at"in r}_handleProviderSignIn(r,e){return K(this,null,function*(){const t=yield this._getUrlForProvider(`${this.url}/authorize`,r,{redirectTo:e.redirectTo,scopes:e.scopes,queryParams:e.queryParams});return this._debug("#_handleProviderSignIn()","provider",r,"options",e,"url",t),_e()&&!e.skipBrowserRedirect&&window.location.assign(t),{data:{provider:r,url:t},error:null}})}_recoverAndRefresh(){return K(this,null,function*(){var r,e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const n=yield Le(this.storage,this.storageKey);if(n&&this.userStorage){let a=yield Le(this.userStorage,this.storageKey+"-user");!this.storage.isServer&&Object.is(this.storage,this.userStorage)&&!a&&(a={user:n.user},yield Je(this.userStorage,this.storageKey+"-user",a)),n.user=(r=a==null?void 0:a.user)!==null&&r!==void 0?r:qt()}else if(n&&!n.user&&!n.user){const a=yield Le(this.storage,this.storageKey+"-user");a&&(a!=null&&a.user)?(n.user=a.user,yield Pe(this.storage,this.storageKey+"-user"),yield Je(this.storage,this.storageKey,n)):n.user=qt()}if(this._debug(t,"session from storage",n),!this._isValidSession(n)){this._debug(t,"session is not valid"),n!==null&&(yield this._removeSession());return}const i=((e=n.expires_at)!==null&&e!==void 0?e:1/0)*1e3-Date.now()<Pt;if(this._debug(t,`session has${i?"":" not"} expired with margin of ${Pt}s`),i){if(this.autoRefreshToken&&n.refresh_token){const{error:a}=yield this._callRefreshToken(n.refresh_token);a&&(xt(a)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",a),yield this._removeSession()))}}else if(n.user&&n.user.__isUserNotAvailableProxy===!0)try{const{data:a,error:c}=yield this._getUser(n.access_token);!c&&(a!=null&&a.user)?(n.user=a.user,yield this._saveSession(n),yield this._notifyAllSubscribers("SIGNED_IN",n)):this._debug(t,"could not get user data, skipping SIGNED_IN notification")}catch(a){this._debug(t,"error getting user data, skipping SIGNED_IN notification",a)}else yield this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){this._debug(t,"error",n);return}finally{this._debug(t,"end")}})}_callRefreshToken(r){return K(this,null,function*(){var e,t;if(!r)throw new xe;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const n=`#_callRefreshToken(${r.substring(0,5)}...)`;this._debug(n,"begin");try{this.refreshingDeferred=new gt;const{data:i,error:a}=yield this._refreshAccessToken(r);if(a)throw a;if(!i.session)throw new xe;yield this._saveSession(i.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const c={session:i.session,error:null};return this.refreshingDeferred.resolve(c),c}catch(i){if(this._debug(n,"error",i),re(i)){const a={session:null,error:i};return xt(i)||(yield this._removeSession()),(e=this.refreshingDeferred)===null||e===void 0||e.resolve(a),a}throw(t=this.refreshingDeferred)===null||t===void 0||t.reject(i),i}finally{this.refreshingDeferred=null,this._debug(n,"end")}})}_notifyAllSubscribers(r,e,t=!0){return K(this,null,function*(){const n=`#_notifyAllSubscribers(${r})`;this._debug(n,"begin",e,`broadcast = ${t}`);try{this.broadcastChannel&&t&&this.broadcastChannel.postMessage({event:r,session:e});const i=[],a=Array.from(this.stateChangeEmitters.values()).map(c=>K(this,null,function*(){try{yield c.callback(r,e)}catch(f){i.push(f)}}));if(yield Promise.all(a),i.length>0){for(let c=0;c<i.length;c+=1);throw i[0]}}finally{this._debug(n,"end")}})}_saveSession(r){return K(this,null,function*(){this._debug("#_saveSession()",r),this.suppressGetSessionWarning=!0;const e=Object.assign({},r),t=e.user&&e.user.__isUserNotAvailableProxy===!0;if(this.userStorage){!t&&e.user&&(yield Je(this.userStorage,this.storageKey+"-user",{user:e.user}));const n=Object.assign({},e);delete n.user;const i=oi(n);yield Je(this.storage,this.storageKey,i)}else{const n=oi(e);yield Je(this.storage,this.storageKey,n)}})}_removeSession(){return K(this,null,function*(){this._debug("#_removeSession()"),yield Pe(this.storage,this.storageKey),yield Pe(this.storage,this.storageKey+"-code-verifier"),yield Pe(this.storage,this.storageKey+"-user"),this.userStorage&&(yield Pe(this.userStorage,this.storageKey+"-user")),yield this._notifyAllSubscribers("SIGNED_OUT",null)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const r=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{r&&_e()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",r)}catch(e){}}_startAutoRefresh(){return K(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const r=setInterval(()=>this._autoRefreshTokenTick(),Ve);this.autoRefreshTicker=r,r&&typeof r=="object"&&typeof r.unref=="function"?r.unref():typeof Deno!="undefined"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(r),setTimeout(()=>K(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return K(this,null,function*(){this._debug("#_stopAutoRefresh()");const r=this.autoRefreshTicker;this.autoRefreshTicker=null,r&&clearInterval(r)})}startAutoRefresh(){return K(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return K(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return K(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>K(this,null,function*(){try{const r=Date.now();try{return yield this._useSession(e=>K(this,null,function*(){const{data:{session:t}}=e;if(!t||!t.refresh_token||!t.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const n=Math.floor((t.expires_at*1e3-r)/Ve);this._debug("#_autoRefreshTokenTick()",`access token expires in ${n} ticks, a tick lasts ${Ve}ms, refresh threshold is ${wn} ticks`),n<=wn&&(yield this._callRefreshToken(t.refresh_token))}))}catch(e){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(r){if(r.isAcquireTimeout||r instanceof vo)this._debug("auto refresh token tick lock not available");else throw r}})}_handleVisibilityChange(){return K(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!_e()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>K(this,null,function*(){return yield this._onVisibilityChanged(!1)}),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(r){}})}_onVisibilityChanged(r){return K(this,null,function*(){const e=`#_onVisibilityChanged(${r})`;this._debug(e,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),r||(yield this.initializePromise,yield this._acquireLock(-1,()=>K(this,null,function*(){if(document.visibilityState!=="visible"){this._debug(e,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}yield this._recoverAndRefresh()})))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(r,e,t){return K(this,null,function*(){const n=[`provider=${encodeURIComponent(e)}`];if(t!=null&&t.redirectTo&&n.push(`redirect_to=${encodeURIComponent(t.redirectTo)}`),t!=null&&t.scopes&&n.push(`scopes=${encodeURIComponent(t.scopes)}`),this.flowType==="pkce"){const[i,a]=yield Ke(this.storage,this.storageKey),c=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(a)}`});n.push(c.toString())}if(t!=null&&t.queryParams){const i=new URLSearchParams(t.queryParams);n.push(i.toString())}return t!=null&&t.skipBrowserRedirect&&n.push(`skip_http_redirect=${t.skipBrowserRedirect}`),`${r}?${n.join("&")}`})}_unenroll(r){return K(this,null,function*(){try{return yield this._useSession(e=>K(this,null,function*(){var t;const{data:n,error:i}=e;return i?{data:null,error:i}:yield oe(this.fetch,"DELETE",`${this.url}/factors/${r.factorId}`,{headers:this.headers,jwt:(t=n==null?void 0:n.session)===null||t===void 0?void 0:t.access_token})}))}catch(e){if(re(e))return{data:null,error:e};throw e}})}_enroll(r){return K(this,null,function*(){try{return yield this._useSession(e=>K(this,null,function*(){var t,n;const{data:i,error:a}=e;if(a)return{data:null,error:a};const c=Object.assign({friendly_name:r.friendlyName,factor_type:r.factorType},r.factorType==="phone"?{phone:r.phone}:{issuer:r.issuer}),{data:f,error:y}=yield oe(this.fetch,"POST",`${this.url}/factors`,{body:c,headers:this.headers,jwt:(t=i==null?void 0:i.session)===null||t===void 0?void 0:t.access_token});return y?{data:null,error:y}:(r.factorType==="totp"&&(!((n=f==null?void 0:f.totp)===null||n===void 0)&&n.qr_code)&&(f.totp.qr_code=`data:image/svg+xml;utf-8,${f.totp.qr_code}`),{data:f,error:null})}))}catch(e){if(re(e))return{data:null,error:e};throw e}})}_verify(r){return K(this,null,function*(){return this._acquireLock(-1,()=>K(this,null,function*(){try{return yield this._useSession(e=>K(this,null,function*(){var t;const{data:n,error:i}=e;if(i)return{data:null,error:i};const{data:a,error:c}=yield oe(this.fetch,"POST",`${this.url}/factors/${r.factorId}/verify`,{body:{code:r.code,challenge_id:r.challengeId},headers:this.headers,jwt:(t=n==null?void 0:n.session)===null||t===void 0?void 0:t.access_token});return c?{data:null,error:c}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:c})}))}catch(e){if(re(e))return{data:null,error:e};throw e}}))})}_challenge(r){return K(this,null,function*(){return this._acquireLock(-1,()=>K(this,null,function*(){try{return yield this._useSession(e=>K(this,null,function*(){var t;const{data:n,error:i}=e;return i?{data:null,error:i}:yield oe(this.fetch,"POST",`${this.url}/factors/${r.factorId}/challenge`,{body:{channel:r.channel},headers:this.headers,jwt:(t=n==null?void 0:n.session)===null||t===void 0?void 0:t.access_token})}))}catch(e){if(re(e))return{data:null,error:e};throw e}}))})}_challengeAndVerify(r){return K(this,null,function*(){const{data:e,error:t}=yield this._challenge({factorId:r.factorId});return t?{data:null,error:t}:yield this._verify({factorId:r.factorId,challengeId:e.id,code:r.code})})}_listFactors(){return K(this,null,function*(){const{data:{user:r},error:e}=yield this.getUser();if(e)return{data:null,error:e};const t=(r==null?void 0:r.factors)||[],n=t.filter(a=>a.factor_type==="totp"&&a.status==="verified"),i=t.filter(a=>a.factor_type==="phone"&&a.status==="verified");return{data:{all:t,totp:n,phone:i},error:null}})}_getAuthenticatorAssuranceLevel(){return K(this,null,function*(){return this._acquireLock(-1,()=>K(this,null,function*(){return yield this._useSession(r=>K(this,null,function*(){var e,t;const{data:{session:n},error:i}=r;if(i)return{data:null,error:i};if(!n)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:a}=Rt(n.access_token);let c=null;a.aal&&(c=a.aal);let f=c;((t=(e=n.user.factors)===null||e===void 0?void 0:e.filter(u=>u.status==="verified"))!==null&&t!==void 0?t:[]).length>0&&(f="aal2");const d=a.amr||[];return{data:{currentLevel:c,nextLevel:f,currentAuthenticationMethods:d},error:null}}))}))})}fetchJwk(t){return K(this,arguments,function*(r,e={keys:[]}){let n=e.keys.find(f=>f.kid===r);if(n)return n;const i=Date.now();if(n=this.jwks.keys.find(f=>f.kid===r),n&&this.jwks_cached_at+Ps>i)return n;const{data:a,error:c}=yield oe(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(c)throw c;return!a.keys||a.keys.length===0||(this.jwks=a,this.jwks_cached_at=i,n=a.keys.find(f=>f.kid===r),!n)?null:n})}getClaims(t){return K(this,arguments,function*(r,e={}){try{let n=r;if(!n){const{data:_,error:m}=yield this.getSession();if(m||!_.session)return{data:null,error:m};n=_.session.access_token}const{header:i,payload:a,signature:c,raw:{header:f,payload:y}}=Rt(n);e!=null&&e.allowExpired||Qs(a.exp);const d=!i.alg||i.alg.startsWith("HS")||!i.kid||!("crypto"in globalThis&&"subtle"in globalThis.crypto)?null:yield this.fetchJwk(i.kid,e!=null&&e.keys?{keys:e.keys}:e==null?void 0:e.jwks);if(!d){const{error:_}=yield this.getUser(n);if(_)throw _;return{data:{claims:a,header:i,signature:c},error:null}}const u=Xs(i.alg),o=yield crypto.subtle.importKey("jwk",d,u,!0,["verify"]);if(!(yield crypto.subtle.verify(u,o,c,Ms(`${f}.${y}`))))throw new kn("Invalid JWT signature");return{data:{claims:a,header:i,signature:c},error:null}}catch(n){if(re(n))return{data:null,error:n};throw n}})}}at.nextInstanceID=0;const Wu=at;var Nt,fi;function pl(){if(fi)return Nt;fi=1;function l(){this.__data__=[],this.size=0}return Nt=l,Nt}var Lt,hi;function po(){if(hi)return Lt;hi=1;function l(r,e){return r===e||r!==r&&e!==e}return Lt=l,Lt}var It,di;function mt(){if(di)return It;di=1;var l=po();function r(e,t){for(var n=e.length;n--;)if(l(e[n][0],t))return n;return-1}return It=r,It}var Ct,vi;function yl(){if(vi)return Ct;vi=1;var l=mt(),r=Array.prototype,e=r.splice;function t(n){var i=this.__data__,a=l(i,n);if(a<0)return!1;var c=i.length-1;return a==c?i.pop():e.call(i,a,1),--this.size,!0}return Ct=t,Ct}var Dt,pi;function gl(){if(pi)return Dt;pi=1;var l=mt();function r(e){var t=this.__data__,n=l(t,e);return n<0?void 0:t[n][1]}return Dt=r,Dt}var Mt,yi;function ml(){if(yi)return Mt;yi=1;var l=mt();function r(e){return l(this.__data__,e)>-1}return Mt=r,Mt}var Bt,gi;function _l(){if(gi)return Bt;gi=1;var l=mt();function r(e,t){var n=this.__data__,i=l(n,e);return i<0?(++this.size,n.push([e,t])):n[i][1]=t,this}return Bt=r,Bt}var Ut,mi;function _t(){if(mi)return Ut;mi=1;var l=pl(),r=yl(),e=gl(),t=ml(),n=_l();function i(a){var c=-1,f=a==null?0:a.length;for(this.clear();++c<f;){var y=a[c];this.set(y[0],y[1])}}return i.prototype.clear=l,i.prototype.delete=r,i.prototype.get=e,i.prototype.has=t,i.prototype.set=n,Ut=i,Ut}var $t,_i;function bl(){if(_i)return $t;_i=1;var l=_t();function r(){this.__data__=new l,this.size=0}return $t=r,$t}var Ft,bi;function wl(){if(bi)return Ft;bi=1;function l(r){var e=this.__data__,t=e.delete(r);return this.size=e.size,t}return Ft=l,Ft}var Ht,wi;function Ol(){if(wi)return Ht;wi=1;function l(r){return this.__data__.get(r)}return Ht=l,Ht}var Kt,Oi;function El(){if(Oi)return Kt;Oi=1;function l(r){return this.__data__.has(r)}return Kt=l,Kt}var zt,Ei;function yo(){if(Ei)return zt;Ei=1;var l=typeof lt=="object"&&lt&&lt.Object===Object&&lt;return zt=l,zt}var Gt,ki;function Se(){if(ki)return Gt;ki=1;var l=yo(),r=typeof self=="object"&&self&&self.Object===Object&&self,e=l||r||Function("return this")();return Gt=e,Gt}var Wt,Ai;function qn(){if(Ai)return Wt;Ai=1;var l=Se(),r=l.Symbol;return Wt=r,Wt}var Vt,Si;function kl(){if(Si)return Vt;Si=1;var l=qn(),r=Object.prototype,e=r.hasOwnProperty,t=r.toString,n=l?l.toStringTag:void 0;function i(a){var c=e.call(a,n),f=a[n];try{a[n]=void 0;var y=!0}catch(u){}var d=t.call(a);return y&&(c?a[n]=f:delete a[n]),d}return Vt=i,Vt}var Jt,Ti;function Al(){if(Ti)return Jt;Ti=1;var l=Object.prototype,r=l.toString;function e(t){return r.call(t)}return Jt=e,Jt}var Yt,Pi;function bt(){if(Pi)return Yt;Pi=1;var l=qn(),r=kl(),e=Al(),t="[object Null]",n="[object Undefined]",i=l?l.toStringTag:void 0;function a(c){return c==null?c===void 0?n:t:i&&i in Object(c)?r(c):e(c)}return Yt=a,Yt}var Zt,xi;function go(){if(xi)return Zt;xi=1;function l(r){var e=typeof r;return r!=null&&(e=="object"||e=="function")}return Zt=l,Zt}var Qt,Ri;function mo(){if(Ri)return Qt;Ri=1;var l=bt(),r=go(),e="[object AsyncFunction]",t="[object Function]",n="[object GeneratorFunction]",i="[object Proxy]";function a(c){if(!r(c))return!1;var f=l(c);return f==t||f==n||f==e||f==i}return Qt=a,Qt}var Xt,qi;function Sl(){if(qi)return Xt;qi=1;var l=Se(),r=l["__core-js_shared__"];return Xt=r,Xt}var er,ji;function Tl(){if(ji)return er;ji=1;var l=Sl(),r=function(){var t=/[^.]+$/.exec(l&&l.keys&&l.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function e(t){return!!r&&r in t}return er=e,er}var tr,Ni;function _o(){if(Ni)return tr;Ni=1;var l=Function.prototype,r=l.toString;function e(t){if(t!=null){try{return r.call(t)}catch(n){}try{return t+""}catch(n){}}return""}return tr=e,tr}var rr,Li;function Pl(){if(Li)return rr;Li=1;var l=mo(),r=Tl(),e=go(),t=_o(),n=/[\\^$.*+?()[\]{}|]/g,i=/^\[object .+?Constructor\]$/,a=Function.prototype,c=Object.prototype,f=a.toString,y=c.hasOwnProperty,d=RegExp("^"+f.call(y).replace(n,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function u(o){if(!e(o)||r(o))return!1;var s=l(o)?d:i;return s.test(t(o))}return rr=u,rr}var nr,Ii;function xl(){if(Ii)return nr;Ii=1;function l(r,e){return r==null?void 0:r[e]}return nr=l,nr}var ir,Ci;function Qe(){if(Ci)return ir;Ci=1;var l=Pl(),r=xl();function e(t,n){var i=r(t,n);return l(i)?i:void 0}return ir=e,ir}var ar,Di;function jn(){if(Di)return ar;Di=1;var l=Qe(),r=Se(),e=l(r,"Map");return ar=e,ar}var or,Mi;function wt(){if(Mi)return or;Mi=1;var l=Qe(),r=l(Object,"create");return or=r,or}var sr,Bi;function Rl(){if(Bi)return sr;Bi=1;var l=wt();function r(){this.__data__=l?l(null):{},this.size=0}return sr=r,sr}var lr,Ui;function ql(){if(Ui)return lr;Ui=1;function l(r){var e=this.has(r)&&delete this.__data__[r];return this.size-=e?1:0,e}return lr=l,lr}var ur,$i;function jl(){if($i)return ur;$i=1;var l=wt(),r="__lodash_hash_undefined__",e=Object.prototype,t=e.hasOwnProperty;function n(i){var a=this.__data__;if(l){var c=a[i];return c===r?void 0:c}return t.call(a,i)?a[i]:void 0}return ur=n,ur}var cr,Fi;function Nl(){if(Fi)return cr;Fi=1;var l=wt(),r=Object.prototype,e=r.hasOwnProperty;function t(n){var i=this.__data__;return l?i[n]!==void 0:e.call(i,n)}return cr=t,cr}var fr,Hi;function Ll(){if(Hi)return fr;Hi=1;var l=wt(),r="__lodash_hash_undefined__";function e(t,n){var i=this.__data__;return this.size+=this.has(t)?0:1,i[t]=l&&n===void 0?r:n,this}return fr=e,fr}var hr,Ki;function Il(){if(Ki)return hr;Ki=1;var l=Rl(),r=ql(),e=jl(),t=Nl(),n=Ll();function i(a){var c=-1,f=a==null?0:a.length;for(this.clear();++c<f;){var y=a[c];this.set(y[0],y[1])}}return i.prototype.clear=l,i.prototype.delete=r,i.prototype.get=e,i.prototype.has=t,i.prototype.set=n,hr=i,hr}var dr,zi;function Cl(){if(zi)return dr;zi=1;var l=Il(),r=_t(),e=jn();function t(){this.size=0,this.__data__={hash:new l,map:new(e||r),string:new l}}return dr=t,dr}var vr,Gi;function Dl(){if(Gi)return vr;Gi=1;function l(r){var e=typeof r;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?r!=="__proto__":r===null}return vr=l,vr}var pr,Wi;function Ot(){if(Wi)return pr;Wi=1;var l=Dl();function r(e,t){var n=e.__data__;return l(t)?n[typeof t=="string"?"string":"hash"]:n.map}return pr=r,pr}var yr,Vi;function Ml(){if(Vi)return yr;Vi=1;var l=Ot();function r(e){var t=l(this,e).delete(e);return this.size-=t?1:0,t}return yr=r,yr}var gr,Ji;function Bl(){if(Ji)return gr;Ji=1;var l=Ot();function r(e){return l(this,e).get(e)}return gr=r,gr}var mr,Yi;function Ul(){if(Yi)return mr;Yi=1;var l=Ot();function r(e){return l(this,e).has(e)}return mr=r,mr}var _r,Zi;function $l(){if(Zi)return _r;Zi=1;var l=Ot();function r(e,t){var n=l(this,e),i=n.size;return n.set(e,t),this.size+=n.size==i?0:1,this}return _r=r,_r}var br,Qi;function bo(){if(Qi)return br;Qi=1;var l=Cl(),r=Ml(),e=Bl(),t=Ul(),n=$l();function i(a){var c=-1,f=a==null?0:a.length;for(this.clear();++c<f;){var y=a[c];this.set(y[0],y[1])}}return i.prototype.clear=l,i.prototype.delete=r,i.prototype.get=e,i.prototype.has=t,i.prototype.set=n,br=i,br}var wr,Xi;function Fl(){if(Xi)return wr;Xi=1;var l=_t(),r=jn(),e=bo(),t=200;function n(i,a){var c=this.__data__;if(c instanceof l){var f=c.__data__;if(!r||f.length<t-1)return f.push([i,a]),this.size=++c.size,this;c=this.__data__=new e(f)}return c.set(i,a),this.size=c.size,this}return wr=n,wr}var Or,ea;function Hl(){if(ea)return Or;ea=1;var l=_t(),r=bl(),e=wl(),t=Ol(),n=El(),i=Fl();function a(c){var f=this.__data__=new l(c);this.size=f.size}return a.prototype.clear=r,a.prototype.delete=e,a.prototype.get=t,a.prototype.has=n,a.prototype.set=i,Or=a,Or}var Er,ta;function Kl(){if(ta)return Er;ta=1;var l="__lodash_hash_undefined__";function r(e){return this.__data__.set(e,l),this}return Er=r,Er}var kr,ra;function zl(){if(ra)return kr;ra=1;function l(r){return this.__data__.has(r)}return kr=l,kr}var Ar,na;function Gl(){if(na)return Ar;na=1;var l=bo(),r=Kl(),e=zl();function t(n){var i=-1,a=n==null?0:n.length;for(this.__data__=new l;++i<a;)this.add(n[i])}return t.prototype.add=t.prototype.push=r,t.prototype.has=e,Ar=t,Ar}var Sr,ia;function Wl(){if(ia)return Sr;ia=1;function l(r,e){for(var t=-1,n=r==null?0:r.length;++t<n;)if(e(r[t],t,r))return!0;return!1}return Sr=l,Sr}var Tr,aa;function Vl(){if(aa)return Tr;aa=1;function l(r,e){return r.has(e)}return Tr=l,Tr}var Pr,oa;function wo(){if(oa)return Pr;oa=1;var l=Gl(),r=Wl(),e=Vl(),t=1,n=2;function i(a,c,f,y,d,u){var o=f&t,s=a.length,_=c.length;if(s!=_&&!(o&&_>s))return!1;var m=u.get(a),g=u.get(c);if(m&&g)return m==c&&g==a;var v=-1,p=!0,b=f&n?new l:void 0;for(u.set(a,c),u.set(c,a);++v<s;){var h=a[v],w=c[v];if(y)var S=o?y(w,h,v,c,a,u):y(h,w,v,a,c,u);if(S!==void 0){if(S)continue;p=!1;break}if(b){if(!r(c,function(E,T){if(!e(b,T)&&(h===E||d(h,E,f,y,u)))return b.push(T)})){p=!1;break}}else if(!(h===w||d(h,w,f,y,u))){p=!1;break}}return u.delete(a),u.delete(c),p}return Pr=i,Pr}var xr,sa;function Jl(){if(sa)return xr;sa=1;var l=Se(),r=l.Uint8Array;return xr=r,xr}var Rr,la;function Yl(){if(la)return Rr;la=1;function l(r){var e=-1,t=Array(r.size);return r.forEach(function(n,i){t[++e]=[i,n]}),t}return Rr=l,Rr}var qr,ua;function Zl(){if(ua)return qr;ua=1;function l(r){var e=-1,t=Array(r.size);return r.forEach(function(n){t[++e]=n}),t}return qr=l,qr}var jr,ca;function Ql(){if(ca)return jr;ca=1;var l=qn(),r=Jl(),e=po(),t=wo(),n=Yl(),i=Zl(),a=1,c=2,f="[object Boolean]",y="[object Date]",d="[object Error]",u="[object Map]",o="[object Number]",s="[object RegExp]",_="[object Set]",m="[object String]",g="[object Symbol]",v="[object ArrayBuffer]",p="[object DataView]",b=l?l.prototype:void 0,h=b?b.valueOf:void 0;function w(S,E,T,R,A,O,k){switch(T){case p:if(S.byteLength!=E.byteLength||S.byteOffset!=E.byteOffset)return!1;S=S.buffer,E=E.buffer;case v:return!(S.byteLength!=E.byteLength||!O(new r(S),new r(E)));case f:case y:case o:return e(+S,+E);case d:return S.name==E.name&&S.message==E.message;case s:case m:return S==E+"";case u:var x=n;case _:var q=R&a;if(x||(x=i),S.size!=E.size&&!q)return!1;var I=k.get(S);if(I)return I==E;R|=c,k.set(S,E);var U=t(x(S),x(E),R,A,O,k);return k.delete(S),U;case g:if(h)return h.call(S)==h.call(E)}return!1}return jr=w,jr}var Nr,fa;function Xl(){if(fa)return Nr;fa=1;function l(r,e){for(var t=-1,n=e.length,i=r.length;++t<n;)r[i+t]=e[t];return r}return Nr=l,Nr}var Lr,ha;function Nn(){if(ha)return Lr;ha=1;var l=Array.isArray;return Lr=l,Lr}var Ir,da;function eu(){if(da)return Ir;da=1;var l=Xl(),r=Nn();function e(t,n,i){var a=n(t);return r(t)?a:l(a,i(t))}return Ir=e,Ir}var Cr,va;function tu(){if(va)return Cr;va=1;function l(r,e){for(var t=-1,n=r==null?0:r.length,i=0,a=[];++t<n;){var c=r[t];e(c,t,r)&&(a[i++]=c)}return a}return Cr=l,Cr}var Dr,pa;function ru(){if(pa)return Dr;pa=1;function l(){return[]}return Dr=l,Dr}var Mr,ya;function nu(){if(ya)return Mr;ya=1;var l=tu(),r=ru(),e=Object.prototype,t=e.propertyIsEnumerable,n=Object.getOwnPropertySymbols,i=n?function(a){return a==null?[]:(a=Object(a),l(n(a),function(c){return t.call(a,c)}))}:r;return Mr=i,Mr}var Br,ga;function iu(){if(ga)return Br;ga=1;function l(r,e){for(var t=-1,n=Array(r);++t<r;)n[t]=e(t);return n}return Br=l,Br}var Ur,ma;function Et(){if(ma)return Ur;ma=1;function l(r){return r!=null&&typeof r=="object"}return Ur=l,Ur}var $r,_a;function au(){if(_a)return $r;_a=1;var l=bt(),r=Et(),e="[object Arguments]";function t(n){return r(n)&&l(n)==e}return $r=t,$r}var Fr,ba;function ou(){if(ba)return Fr;ba=1;var l=au(),r=Et(),e=Object.prototype,t=e.hasOwnProperty,n=e.propertyIsEnumerable,i=l(function(){return arguments}())?l:function(a){return r(a)&&t.call(a,"callee")&&!n.call(a,"callee")};return Fr=i,Fr}var tt={exports:{}},Hr,wa;function su(){if(wa)return Hr;wa=1;function l(){return!1}return Hr=l,Hr}tt.exports;var Oa;function Oo(){return Oa||(Oa=1,function(l,r){var e=Se(),t=su(),n=r&&!r.nodeType&&r,i=n&&!0&&l&&!l.nodeType&&l,a=i&&i.exports===n,c=a?e.Buffer:void 0,f=c?c.isBuffer:void 0,y=f||t;l.exports=y}(tt,tt.exports)),tt.exports}var Kr,Ea;function lu(){if(Ea)return Kr;Ea=1;var l=9007199254740991,r=/^(?:0|[1-9]\d*)$/;function e(t,n){var i=typeof t;return n=n==null?l:n,!!n&&(i=="number"||i!="symbol"&&r.test(t))&&t>-1&&t%1==0&&t<n}return Kr=e,Kr}var zr,ka;function Eo(){if(ka)return zr;ka=1;var l=9007199254740991;function r(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=l}return zr=r,zr}var Gr,Aa;function uu(){if(Aa)return Gr;Aa=1;var l=bt(),r=Eo(),e=Et(),t="[object Arguments]",n="[object Array]",i="[object Boolean]",a="[object Date]",c="[object Error]",f="[object Function]",y="[object Map]",d="[object Number]",u="[object Object]",o="[object RegExp]",s="[object Set]",_="[object String]",m="[object WeakMap]",g="[object ArrayBuffer]",v="[object DataView]",p="[object Float32Array]",b="[object Float64Array]",h="[object Int8Array]",w="[object Int16Array]",S="[object Int32Array]",E="[object Uint8Array]",T="[object Uint8ClampedArray]",R="[object Uint16Array]",A="[object Uint32Array]",O={};O[p]=O[b]=O[h]=O[w]=O[S]=O[E]=O[T]=O[R]=O[A]=!0,O[t]=O[n]=O[g]=O[i]=O[v]=O[a]=O[c]=O[f]=O[y]=O[d]=O[u]=O[o]=O[s]=O[_]=O[m]=!1;function k(x){return e(x)&&r(x.length)&&!!O[l(x)]}return Gr=k,Gr}var Wr,Sa;function cu(){if(Sa)return Wr;Sa=1;function l(r){return function(e){return r(e)}}return Wr=l,Wr}var rt={exports:{}};rt.exports;var Ta;function fu(){return Ta||(Ta=1,function(l,r){var e=yo(),t=r&&!r.nodeType&&r,n=t&&!0&&l&&!l.nodeType&&l,i=n&&n.exports===t,a=i&&e.process,c=function(){try{var f=n&&n.require&&n.require("util").types;return f||a&&a.binding&&a.binding("util")}catch(y){}}();l.exports=c}(rt,rt.exports)),rt.exports}var Vr,Pa;function ko(){if(Pa)return Vr;Pa=1;var l=uu(),r=cu(),e=fu(),t=e&&e.isTypedArray,n=t?r(t):l;return Vr=n,Vr}var Jr,xa;function hu(){if(xa)return Jr;xa=1;var l=iu(),r=ou(),e=Nn(),t=Oo(),n=lu(),i=ko(),a=Object.prototype,c=a.hasOwnProperty;function f(y,d){var u=e(y),o=!u&&r(y),s=!u&&!o&&t(y),_=!u&&!o&&!s&&i(y),m=u||o||s||_,g=m?l(y.length,String):[],v=g.length;for(var p in y)(d||c.call(y,p))&&!(m&&(p=="length"||s&&(p=="offset"||p=="parent")||_&&(p=="buffer"||p=="byteLength"||p=="byteOffset")||n(p,v)))&&g.push(p);return g}return Jr=f,Jr}var Yr,Ra;function du(){if(Ra)return Yr;Ra=1;var l=Object.prototype;function r(e){var t=e&&e.constructor,n=typeof t=="function"&&t.prototype||l;return e===n}return Yr=r,Yr}var Zr,qa;function vu(){if(qa)return Zr;qa=1;function l(r,e){return function(t){return r(e(t))}}return Zr=l,Zr}var Qr,ja;function pu(){if(ja)return Qr;ja=1;var l=vu(),r=l(Object.keys,Object);return Qr=r,Qr}var Xr,Na;function yu(){if(Na)return Xr;Na=1;var l=du(),r=pu(),e=Object.prototype,t=e.hasOwnProperty;function n(i){if(!l(i))return r(i);var a=[];for(var c in Object(i))t.call(i,c)&&c!="constructor"&&a.push(c);return a}return Xr=n,Xr}var en,La;function gu(){if(La)return en;La=1;var l=mo(),r=Eo();function e(t){return t!=null&&r(t.length)&&!l(t)}return en=e,en}var tn,Ia;function mu(){if(Ia)return tn;Ia=1;var l=hu(),r=yu(),e=gu();function t(n){return e(n)?l(n):r(n)}return tn=t,tn}var rn,Ca;function _u(){if(Ca)return rn;Ca=1;var l=eu(),r=nu(),e=mu();function t(n){return l(n,e,r)}return rn=t,rn}var nn,Da;function bu(){if(Da)return nn;Da=1;var l=_u(),r=1,e=Object.prototype,t=e.hasOwnProperty;function n(i,a,c,f,y,d){var u=c&r,o=l(i),s=o.length,_=l(a),m=_.length;if(s!=m&&!u)return!1;for(var g=s;g--;){var v=o[g];if(!(u?v in a:t.call(a,v)))return!1}var p=d.get(i),b=d.get(a);if(p&&b)return p==a&&b==i;var h=!0;d.set(i,a),d.set(a,i);for(var w=u;++g<s;){v=o[g];var S=i[v],E=a[v];if(f)var T=u?f(E,S,v,a,i,d):f(S,E,v,i,a,d);if(!(T===void 0?S===E||y(S,E,c,f,d):T)){h=!1;break}w||(w=v=="constructor")}if(h&&!w){var R=i.constructor,A=a.constructor;R!=A&&"constructor"in i&&"constructor"in a&&!(typeof R=="function"&&R instanceof R&&typeof A=="function"&&A instanceof A)&&(h=!1)}return d.delete(i),d.delete(a),h}return nn=n,nn}var an,Ma;function wu(){if(Ma)return an;Ma=1;var l=Qe(),r=Se(),e=l(r,"DataView");return an=e,an}var on,Ba;function Ou(){if(Ba)return on;Ba=1;var l=Qe(),r=Se(),e=l(r,"Promise");return on=e,on}var sn,Ua;function Eu(){if(Ua)return sn;Ua=1;var l=Qe(),r=Se(),e=l(r,"Set");return sn=e,sn}var ln,$a;function ku(){if($a)return ln;$a=1;var l=Qe(),r=Se(),e=l(r,"WeakMap");return ln=e,ln}var un,Fa;function Au(){if(Fa)return un;Fa=1;var l=wu(),r=jn(),e=Ou(),t=Eu(),n=ku(),i=bt(),a=_o(),c="[object Map]",f="[object Object]",y="[object Promise]",d="[object Set]",u="[object WeakMap]",o="[object DataView]",s=a(l),_=a(r),m=a(e),g=a(t),v=a(n),p=i;return(l&&p(new l(new ArrayBuffer(1)))!=o||r&&p(new r)!=c||e&&p(e.resolve())!=y||t&&p(new t)!=d||n&&p(new n)!=u)&&(p=function(b){var h=i(b),w=h==f?b.constructor:void 0,S=w?a(w):"";if(S)switch(S){case s:return o;case _:return c;case m:return y;case g:return d;case v:return u}return h}),un=p,un}var cn,Ha;function Su(){if(Ha)return cn;Ha=1;var l=Hl(),r=wo(),e=Ql(),t=bu(),n=Au(),i=Nn(),a=Oo(),c=ko(),f=1,y="[object Arguments]",d="[object Array]",u="[object Object]",o=Object.prototype,s=o.hasOwnProperty;function _(m,g,v,p,b,h){var w=i(m),S=i(g),E=w?d:n(m),T=S?d:n(g);E=E==y?u:E,T=T==y?u:T;var R=E==u,A=T==u,O=E==T;if(O&&a(m)){if(!a(g))return!1;w=!0,R=!1}if(O&&!R)return h||(h=new l),w||c(m)?r(m,g,v,p,b,h):e(m,g,E,v,p,b,h);if(!(v&f)){var k=R&&s.call(m,"__wrapped__"),x=A&&s.call(g,"__wrapped__");if(k||x){var q=k?m.value():m,I=x?g.value():g;return h||(h=new l),b(q,I,v,p,h)}}return O?(h||(h=new l),t(m,g,v,p,b,h)):!1}return cn=_,cn}var fn,Ka;function Tu(){if(Ka)return fn;Ka=1;var l=Su(),r=Et();function e(t,n,i,a,c){return t===n?!0:t==null||n==null||!r(t)&&!r(n)?t!==t&&n!==n:l(t,n,i,a,e,c)}return fn=e,fn}var hn,za;function Vu(){if(za)return hn;za=1;var l=Tu();function r(e,t){return l(e,t)}return hn=r,hn}var dt={exports:{}};/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, Jason Chen
 * Copyright (c) 2013, salesforce.com
 */var Pu=dt.exports,Ga;function Ju(){return Ga||(Ga=1,function(l,r){(function(t,n){l.exports=n()})(typeof self!="undefined"?self:Pu,function(){return function(e){var t={};function n(i){if(t[i])return t[i].exports;var a=t[i]={i,l:!1,exports:{}};return e[i].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(i,a,c){n.o(i,a)||Object.defineProperty(i,a,{configurable:!1,enumerable:!0,get:c})},n.n=function(i){var a=i&&i.__esModule?function(){return i.default}:function(){return i};return n.d(a,"a",a),a},n.o=function(i,a){return Object.prototype.hasOwnProperty.call(i,a)},n.p="",n(n.s=109)}([function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(17),a=n(18),c=n(19),f=n(45),y=n(46),d=n(47),u=n(48),o=n(49),s=n(12),_=n(32),m=n(33),g=n(31),v=n(1),p={Scope:v.Scope,create:v.create,find:v.find,query:v.query,register:v.register,Container:i.default,Format:a.default,Leaf:c.default,Embed:u.default,Scroll:f.default,Block:d.default,Inline:y.default,Text:o.default,Attributor:{Attribute:s.default,Class:_.default,Style:m.default,Store:g.default}};t.default=p},function(e,t,n){var i=this&&this.__extends||function(){var g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(v,p){v.__proto__=p}||function(v,p){for(var b in p)p.hasOwnProperty(b)&&(v[b]=p[b])};return function(v,p){g(v,p);function b(){this.constructor=v}v.prototype=p===null?Object.create(p):(b.prototype=p.prototype,new b)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=function(g){i(v,g);function v(p){var b=this;return p="[Parchment] "+p,b=g.call(this,p)||this,b.message=p,b.name=b.constructor.name,b}return v}(Error);t.ParchmentError=a;var c={},f={},y={},d={};t.DATA_KEY="__blot";var u;(function(g){g[g.TYPE=3]="TYPE",g[g.LEVEL=12]="LEVEL",g[g.ATTRIBUTE=13]="ATTRIBUTE",g[g.BLOT=14]="BLOT",g[g.INLINE=7]="INLINE",g[g.BLOCK=11]="BLOCK",g[g.BLOCK_BLOT=10]="BLOCK_BLOT",g[g.INLINE_BLOT=6]="INLINE_BLOT",g[g.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",g[g.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",g[g.ANY=15]="ANY"})(u=t.Scope||(t.Scope={}));function o(g,v){var p=_(g);if(p==null)throw new a("Unable to create "+g+" blot");var b=p,h=g instanceof Node||g.nodeType===Node.TEXT_NODE?g:b.create(v);return new b(h,v)}t.create=o;function s(g,v){return v===void 0&&(v=!1),g==null?null:g[t.DATA_KEY]!=null?g[t.DATA_KEY].blot:v?s(g.parentNode,v):null}t.find=s;function _(g,v){v===void 0&&(v=u.ANY);var p;if(typeof g=="string")p=d[g]||c[g];else if(g instanceof Text||g.nodeType===Node.TEXT_NODE)p=d.text;else if(typeof g=="number")g&u.LEVEL&u.BLOCK?p=d.block:g&u.LEVEL&u.INLINE&&(p=d.inline);else if(g instanceof HTMLElement){var b=(g.getAttribute("class")||"").split(/\s+/);for(var h in b)if(p=f[b[h]],p)break;p=p||y[g.tagName]}return p==null?null:v&u.LEVEL&p.scope&&v&u.TYPE&p.scope?p:null}t.query=_;function m(){for(var g=[],v=0;v<arguments.length;v++)g[v]=arguments[v];if(g.length>1)return g.map(function(h){return m(h)});var p=g[0];if(typeof p.blotName!="string"&&typeof p.attrName!="string")throw new a("Invalid definition");if(p.blotName==="abstract")throw new a("Cannot register abstract class");if(d[p.blotName||p.attrName]=p,typeof p.keyName=="string")c[p.keyName]=p;else if(p.className!=null&&(f[p.className]=p),p.tagName!=null){Array.isArray(p.tagName)?p.tagName=p.tagName.map(function(h){return h.toUpperCase()}):p.tagName=p.tagName.toUpperCase();var b=Array.isArray(p.tagName)?p.tagName:[p.tagName];b.forEach(function(h){(y[h]==null||p.className==null)&&(y[h]=p)})}return p}t.register=m},function(e,t,n){var i=n(51),a=n(11),c=n(3),f=n(20),y="\0",d=function(u){Array.isArray(u)?this.ops=u:u!=null&&Array.isArray(u.ops)?this.ops=u.ops:this.ops=[]};d.prototype.insert=function(u,o){var s={};return u.length===0?this:(s.insert=u,o!=null&&typeof o=="object"&&Object.keys(o).length>0&&(s.attributes=o),this.push(s))},d.prototype.delete=function(u){return u<=0?this:this.push({delete:u})},d.prototype.retain=function(u,o){if(u<=0)return this;var s={retain:u};return o!=null&&typeof o=="object"&&Object.keys(o).length>0&&(s.attributes=o),this.push(s)},d.prototype.push=function(u){var o=this.ops.length,s=this.ops[o-1];if(u=c(!0,{},u),typeof s=="object"){if(typeof u.delete=="number"&&typeof s.delete=="number")return this.ops[o-1]={delete:s.delete+u.delete},this;if(typeof s.delete=="number"&&u.insert!=null&&(o-=1,s=this.ops[o-1],typeof s!="object"))return this.ops.unshift(u),this;if(a(u.attributes,s.attributes)){if(typeof u.insert=="string"&&typeof s.insert=="string")return this.ops[o-1]={insert:s.insert+u.insert},typeof u.attributes=="object"&&(this.ops[o-1].attributes=u.attributes),this;if(typeof u.retain=="number"&&typeof s.retain=="number")return this.ops[o-1]={retain:s.retain+u.retain},typeof u.attributes=="object"&&(this.ops[o-1].attributes=u.attributes),this}}return o===this.ops.length?this.ops.push(u):this.ops.splice(o,0,u),this},d.prototype.chop=function(){var u=this.ops[this.ops.length-1];return u&&u.retain&&!u.attributes&&this.ops.pop(),this},d.prototype.filter=function(u){return this.ops.filter(u)},d.prototype.forEach=function(u){this.ops.forEach(u)},d.prototype.map=function(u){return this.ops.map(u)},d.prototype.partition=function(u){var o=[],s=[];return this.forEach(function(_){var m=u(_)?o:s;m.push(_)}),[o,s]},d.prototype.reduce=function(u,o){return this.ops.reduce(u,o)},d.prototype.changeLength=function(){return this.reduce(function(u,o){return o.insert?u+f.length(o):o.delete?u-o.delete:u},0)},d.prototype.length=function(){return this.reduce(function(u,o){return u+f.length(o)},0)},d.prototype.slice=function(u,o){u=u||0,typeof o!="number"&&(o=1/0);for(var s=[],_=f.iterator(this.ops),m=0;m<o&&_.hasNext();){var g;m<u?g=_.next(u-m):(g=_.next(o-m),s.push(g)),m+=f.length(g)}return new d(s)},d.prototype.compose=function(u){var o=f.iterator(this.ops),s=f.iterator(u.ops),_=[],m=s.peek();if(m!=null&&typeof m.retain=="number"&&m.attributes==null){for(var g=m.retain;o.peekType()==="insert"&&o.peekLength()<=g;)g-=o.peekLength(),_.push(o.next());m.retain-g>0&&s.next(m.retain-g)}for(var v=new d(_);o.hasNext()||s.hasNext();)if(s.peekType()==="insert")v.push(s.next());else if(o.peekType()==="delete")v.push(o.next());else{var p=Math.min(o.peekLength(),s.peekLength()),b=o.next(p),h=s.next(p);if(typeof h.retain=="number"){var w={};typeof b.retain=="number"?w.retain=p:w.insert=b.insert;var S=f.attributes.compose(b.attributes,h.attributes,typeof b.retain=="number");if(S&&(w.attributes=S),v.push(w),!s.hasNext()&&a(v.ops[v.ops.length-1],w)){var E=new d(o.rest());return v.concat(E).chop()}}else typeof h.delete=="number"&&typeof b.retain=="number"&&v.push(h)}return v.chop()},d.prototype.concat=function(u){var o=new d(this.ops.slice());return u.ops.length>0&&(o.push(u.ops[0]),o.ops=o.ops.concat(u.ops.slice(1))),o},d.prototype.diff=function(u,o){if(this.ops===u.ops)return new d;var s=[this,u].map(function(p){return p.map(function(b){if(b.insert!=null)return typeof b.insert=="string"?b.insert:y;var h=p===u?"on":"with";throw new Error("diff() called "+h+" non-document")}).join("")}),_=new d,m=i(s[0],s[1],o),g=f.iterator(this.ops),v=f.iterator(u.ops);return m.forEach(function(p){for(var b=p[1].length;b>0;){var h=0;switch(p[0]){case i.INSERT:h=Math.min(v.peekLength(),b),_.push(v.next(h));break;case i.DELETE:h=Math.min(b,g.peekLength()),g.next(h),_.delete(h);break;case i.EQUAL:h=Math.min(g.peekLength(),v.peekLength(),b);var w=g.next(h),S=v.next(h);a(w.insert,S.insert)?_.retain(h,f.attributes.diff(w.attributes,S.attributes)):_.push(S).delete(h);break}b-=h}}),_.chop()},d.prototype.eachLine=function(u,o){o=o||`
`;for(var s=f.iterator(this.ops),_=new d,m=0;s.hasNext();){if(s.peekType()!=="insert")return;var g=s.peek(),v=f.length(g)-s.peekLength(),p=typeof g.insert=="string"?g.insert.indexOf(o,v)-v:-1;if(p<0)_.push(s.next());else if(p>0)_.push(s.next(p));else{if(u(_,s.next(1).attributes||{},m)===!1)return;m+=1,_=new d}}_.length()>0&&u(_,{},m)},d.prototype.transform=function(u,o){if(o=!!o,typeof u=="number")return this.transformPosition(u,o);for(var s=f.iterator(this.ops),_=f.iterator(u.ops),m=new d;s.hasNext()||_.hasNext();)if(s.peekType()==="insert"&&(o||_.peekType()!=="insert"))m.retain(f.length(s.next()));else if(_.peekType()==="insert")m.push(_.next());else{var g=Math.min(s.peekLength(),_.peekLength()),v=s.next(g),p=_.next(g);if(v.delete)continue;p.delete?m.push(p):m.retain(g,f.attributes.transform(v.attributes,p.attributes,o))}return m.chop()},d.prototype.transformPosition=function(u,o){o=!!o;for(var s=f.iterator(this.ops),_=0;s.hasNext()&&_<=u;){var m=s.peekLength(),g=s.peekType();if(s.next(),g==="delete"){u-=Math.min(m,u-_);continue}else g==="insert"&&(_<u||!o)&&(u+=m);_+=m}return u},e.exports=d},function(e,t){var n=Object.prototype.hasOwnProperty,i=Object.prototype.toString,a=Object.defineProperty,c=Object.getOwnPropertyDescriptor,f=function(s){return typeof Array.isArray=="function"?Array.isArray(s):i.call(s)==="[object Array]"},y=function(s){if(!s||i.call(s)!=="[object Object]")return!1;var _=n.call(s,"constructor"),m=s.constructor&&s.constructor.prototype&&n.call(s.constructor.prototype,"isPrototypeOf");if(s.constructor&&!_&&!m)return!1;var g;for(g in s);return typeof g=="undefined"||n.call(s,g)},d=function(s,_){a&&_.name==="__proto__"?a(s,_.name,{enumerable:!0,configurable:!0,value:_.newValue,writable:!0}):s[_.name]=_.newValue},u=function(s,_){if(_==="__proto__")if(n.call(s,_)){if(c)return c(s,_).value}else return;return s[_]};e.exports=function o(){var s,_,m,g,v,p,b=arguments[0],h=1,w=arguments.length,S=!1;for(typeof b=="boolean"&&(S=b,b=arguments[1]||{},h=2),(b==null||typeof b!="object"&&typeof b!="function")&&(b={});h<w;++h)if(s=arguments[h],s!=null)for(_ in s)m=u(b,_),g=u(s,_),b!==g&&(S&&g&&(y(g)||(v=f(g)))?(v?(v=!1,p=m&&f(m)?m:[]):p=m&&y(m)?m:{},d(b,{name:_,newValue:o(S,p,g)})):typeof g!="undefined"&&d(b,{name:_,newValue:g}));return b}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.BlockEmbed=t.bubbleFormats=void 0;var i=function(){function O(k,x){for(var q=0;q<x.length;q++){var I=x[q];I.enumerable=I.enumerable||!1,I.configurable=!0,"value"in I&&(I.writable=!0),Object.defineProperty(k,I.key,I)}}return function(k,x,q){return x&&O(k.prototype,x),q&&O(k,q),k}}(),a=function O(k,x,q){k===null&&(k=Function.prototype);var I=Object.getOwnPropertyDescriptor(k,x);if(I===void 0){var U=Object.getPrototypeOf(k);return U===null?void 0:O(U,x,q)}else{if("value"in I)return I.value;var F=I.get;return F===void 0?void 0:F.call(q)}},c=n(3),f=b(c),y=n(2),d=b(y),u=n(0),o=b(u),s=n(16),_=b(s),m=n(6),g=b(m),v=n(7),p=b(v);function b(O){return O&&O.__esModule?O:{default:O}}function h(O,k){if(!(O instanceof k))throw new TypeError("Cannot call a class as a function")}function w(O,k){if(!O)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return k&&(typeof k=="object"||typeof k=="function")?k:O}function S(O,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof k);O.prototype=Object.create(k&&k.prototype,{constructor:{value:O,enumerable:!1,writable:!0,configurable:!0}}),k&&(Object.setPrototypeOf?Object.setPrototypeOf(O,k):O.__proto__=k)}var E=1,T=function(O){S(k,O);function k(){return h(this,k),w(this,(k.__proto__||Object.getPrototypeOf(k)).apply(this,arguments))}return i(k,[{key:"attach",value:function(){a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"attach",this).call(this),this.attributes=new o.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new d.default().insert(this.value(),(0,f.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(q,I){var U=o.default.query(q,o.default.Scope.BLOCK_ATTRIBUTE);U!=null&&this.attributes.attribute(U,I)}},{key:"formatAt",value:function(q,I,U,F){this.format(U,F)}},{key:"insertAt",value:function(q,I,U){if(typeof I=="string"&&I.endsWith(`
`)){var F=o.default.create(R.blotName);this.parent.insertBefore(F,q===0?this:this.next),F.insertAt(0,I.slice(0,-1))}else a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"insertAt",this).call(this,q,I,U)}}]),k}(o.default.Embed);T.scope=o.default.Scope.BLOCK_BLOT;var R=function(O){S(k,O);function k(x){h(this,k);var q=w(this,(k.__proto__||Object.getPrototypeOf(k)).call(this,x));return q.cache={},q}return i(k,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(o.default.Leaf).reduce(function(q,I){return I.length()===0?q:q.insert(I.value(),A(I))},new d.default).insert(`
`,A(this))),this.cache.delta}},{key:"deleteAt",value:function(q,I){a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"deleteAt",this).call(this,q,I),this.cache={}}},{key:"formatAt",value:function(q,I,U,F){I<=0||(o.default.query(U,o.default.Scope.BLOCK)?q+I===this.length()&&this.format(U,F):a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"formatAt",this).call(this,q,Math.min(I,this.length()-q-1),U,F),this.cache={})}},{key:"insertAt",value:function(q,I,U){if(U!=null)return a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"insertAt",this).call(this,q,I,U);if(I.length!==0){var F=I.split(`
`),H=F.shift();H.length>0&&(q<this.length()-1||this.children.tail==null?a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"insertAt",this).call(this,Math.min(q,this.length()-1),H):this.children.tail.insertAt(this.children.tail.length(),H),this.cache={});var D=this;F.reduce(function(L,P){return D=D.split(L,!0),D.insertAt(0,P),P.length},q+H.length)}}},{key:"insertBefore",value:function(q,I){var U=this.children.head;a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"insertBefore",this).call(this,q,I),U instanceof _.default&&U.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"length",this).call(this)+E),this.cache.length}},{key:"moveChildren",value:function(q,I){a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"moveChildren",this).call(this,q,I),this.cache={}}},{key:"optimize",value:function(q){a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"optimize",this).call(this,q),this.cache={}}},{key:"path",value:function(q){return a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"path",this).call(this,q,!0)}},{key:"removeChild",value:function(q){a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"removeChild",this).call(this,q),this.cache={}}},{key:"split",value:function(q){var I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(I&&(q===0||q>=this.length()-E)){var U=this.clone();return q===0?(this.parent.insertBefore(U,this),this):(this.parent.insertBefore(U,this.next),U)}else{var F=a(k.prototype.__proto__||Object.getPrototypeOf(k.prototype),"split",this).call(this,q,I);return this.cache={},F}}}]),k}(o.default.Block);R.blotName="block",R.tagName="P",R.defaultChild="break",R.allowedChildren=[g.default,o.default.Embed,p.default];function A(O){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return O==null||(typeof O.formats=="function"&&(k=(0,f.default)(k,O.formats())),O.parent==null||O.parent.blotName=="scroll"||O.parent.statics.scope!==O.statics.scope)?k:A(O.parent,k)}t.bubbleFormats=A,t.BlockEmbed=T,t.default=R},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.overload=t.expandConfig=void 0;var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(D){return typeof D}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},a=function(){function D(L,P){var N=[],C=!0,$=!1,B=void 0;try{for(var j=L[Symbol.iterator](),M;!(C=(M=j.next()).done)&&(N.push(M.value),!(P&&N.length===P));C=!0);}catch(z){$=!0,B=z}finally{try{!C&&j.return&&j.return()}finally{if($)throw B}}return N}return function(L,P){if(Array.isArray(L))return L;if(Symbol.iterator in Object(L))return D(L,P);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),c=function(){function D(L,P){for(var N=0;N<P.length;N++){var C=P[N];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(L,C.key,C)}}return function(L,P,N){return P&&D(L.prototype,P),N&&D(L,N),L}}();n(50);var f=n(2),y=A(f),d=n(14),u=A(d),o=n(8),s=A(o),_=n(9),m=A(_),g=n(0),v=A(g),p=n(15),b=A(p),h=n(3),w=A(h),S=n(10),E=A(S),T=n(34),R=A(T);function A(D){return D&&D.__esModule?D:{default:D}}function O(D,L,P){return L in D?Object.defineProperty(D,L,{value:P,enumerable:!0,configurable:!0,writable:!0}):D[L]=P,D}function k(D,L){if(!(D instanceof L))throw new TypeError("Cannot call a class as a function")}var x=(0,E.default)("quill"),q=function(){c(D,null,[{key:"debug",value:function(P){P===!0&&(P="log"),E.default.level(P)}},{key:"find",value:function(P){return P.__quill||v.default.find(P)}},{key:"import",value:function(P){return this.imports[P]==null&&x.error("Cannot import "+P+". Are you sure it was registered?"),this.imports[P]}},{key:"register",value:function(P,N){var C=this,$=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof P!="string"){var B=P.attrName||P.blotName;typeof B=="string"?this.register("formats/"+B,P,N):Object.keys(P).forEach(function(j){C.register(j,P[j],N)})}else this.imports[P]!=null&&!$&&x.warn("Overwriting "+P+" with",N),this.imports[P]=N,(P.startsWith("blots/")||P.startsWith("formats/"))&&N.blotName!=="abstract"?v.default.register(N):P.startsWith("modules")&&typeof N.register=="function"&&N.register()}}]);function D(L){var P=this,N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(k(this,D),this.options=I(L,N),this.container=this.options.container,this.container==null)return x.error("Invalid Quill container",L);this.options.debug&&D.debug(this.options.debug);var C=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new s.default,this.scroll=v.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new u.default(this.scroll),this.selection=new b.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(s.default.events.EDITOR_CHANGE,function(B){B===s.default.events.TEXT_CHANGE&&P.root.classList.toggle("ql-blank",P.editor.isBlank())}),this.emitter.on(s.default.events.SCROLL_UPDATE,function(B,j){var M=P.selection.lastRange,z=M&&M.length===0?M.index:void 0;U.call(P,function(){return P.editor.update(null,j,z)},B)});var $=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+C+"<p><br></p></div>");this.setContents($),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return c(D,[{key:"addContainer",value:function(P){var N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof P=="string"){var C=P;P=document.createElement("div"),P.classList.add(C)}return this.container.insertBefore(P,N),P}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(P,N,C){var $=this,B=F(P,N,C),j=a(B,4);return P=j[0],N=j[1],C=j[3],U.call(this,function(){return $.editor.deleteText(P,N)},C,P,-1*N)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(P),this.container.classList.toggle("ql-disabled",!P)}},{key:"focus",value:function(){var P=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=P,this.scrollIntoView()}},{key:"format",value:function(P,N){var C=this,$=arguments.length>2&&arguments[2]!==void 0?arguments[2]:s.default.sources.API;return U.call(this,function(){var B=C.getSelection(!0),j=new y.default;if(B==null)return j;if(v.default.query(P,v.default.Scope.BLOCK))j=C.editor.formatLine(B.index,B.length,O({},P,N));else{if(B.length===0)return C.selection.format(P,N),j;j=C.editor.formatText(B.index,B.length,O({},P,N))}return C.setSelection(B,s.default.sources.SILENT),j},$)}},{key:"formatLine",value:function(P,N,C,$,B){var j=this,M=void 0,z=F(P,N,C,$,B),G=a(z,4);return P=G[0],N=G[1],M=G[2],B=G[3],U.call(this,function(){return j.editor.formatLine(P,N,M)},B,P,0)}},{key:"formatText",value:function(P,N,C,$,B){var j=this,M=void 0,z=F(P,N,C,$,B),G=a(z,4);return P=G[0],N=G[1],M=G[2],B=G[3],U.call(this,function(){return j.editor.formatText(P,N,M)},B,P,0)}},{key:"getBounds",value:function(P){var N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,C=void 0;typeof P=="number"?C=this.selection.getBounds(P,N):C=this.selection.getBounds(P.index,P.length);var $=this.container.getBoundingClientRect();return{bottom:C.bottom-$.top,height:C.height,left:C.left-$.left,right:C.right-$.left,top:C.top-$.top,width:C.width}}},{key:"getContents",value:function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-P,C=F(P,N),$=a(C,2);return P=$[0],N=$[1],this.editor.getContents(P,N)}},{key:"getFormat",value:function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof P=="number"?this.editor.getFormat(P,N):this.editor.getFormat(P.index,P.length)}},{key:"getIndex",value:function(P){return P.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(P){return this.scroll.leaf(P)}},{key:"getLine",value:function(P){return this.scroll.line(P)}},{key:"getLines",value:function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof P!="number"?this.scroll.lines(P.index,P.length):this.scroll.lines(P,N)}},{key:"getModule",value:function(P){return this.theme.modules[P]}},{key:"getSelection",value:function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return P&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-P,C=F(P,N),$=a(C,2);return P=$[0],N=$[1],this.editor.getText(P,N)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(P,N,C){var $=this,B=arguments.length>3&&arguments[3]!==void 0?arguments[3]:D.sources.API;return U.call(this,function(){return $.editor.insertEmbed(P,N,C)},B,P)}},{key:"insertText",value:function(P,N,C,$,B){var j=this,M=void 0,z=F(P,0,C,$,B),G=a(z,4);return P=G[0],M=G[2],B=G[3],U.call(this,function(){return j.editor.insertText(P,N,M)},B,P,N.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(P,N,C){this.clipboard.dangerouslyPasteHTML(P,N,C)}},{key:"removeFormat",value:function(P,N,C){var $=this,B=F(P,N,C),j=a(B,4);return P=j[0],N=j[1],C=j[3],U.call(this,function(){return $.editor.removeFormat(P,N)},C,P)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(P){var N=this,C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s.default.sources.API;return U.call(this,function(){P=new y.default(P);var $=N.getLength(),B=N.editor.deleteText(0,$),j=N.editor.applyDelta(P),M=j.ops[j.ops.length-1];M!=null&&typeof M.insert=="string"&&M.insert[M.insert.length-1]===`
`&&(N.editor.deleteText(N.getLength()-1,1),j.delete(1));var z=B.compose(j);return z},C)}},{key:"setSelection",value:function(P,N,C){if(P==null)this.selection.setRange(null,N||D.sources.API);else{var $=F(P,N,C),B=a($,4);P=B[0],N=B[1],C=B[3],this.selection.setRange(new p.Range(P,N),C),C!==s.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(P){var N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s.default.sources.API,C=new y.default().insert(P);return this.setContents(C,N)}},{key:"update",value:function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:s.default.sources.USER,N=this.scroll.update(P);return this.selection.update(P),N}},{key:"updateContents",value:function(P){var N=this,C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s.default.sources.API;return U.call(this,function(){return P=new y.default(P),N.editor.applyDelta(P,C)},C,!0)}}]),D}();q.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},q.events=s.default.events,q.sources=s.default.sources,q.version="1.3.7",q.imports={delta:y.default,parchment:v.default,"core/module":m.default,"core/theme":R.default};function I(D,L){if(L=(0,w.default)(!0,{container:D,modules:{clipboard:!0,keyboard:!0,history:!0}},L),!L.theme||L.theme===q.DEFAULTS.theme)L.theme=R.default;else if(L.theme=q.import("themes/"+L.theme),L.theme==null)throw new Error("Invalid theme "+L.theme+". Did you register it?");var P=(0,w.default)(!0,{},L.theme.DEFAULTS);[P,L].forEach(function($){$.modules=$.modules||{},Object.keys($.modules).forEach(function(B){$.modules[B]===!0&&($.modules[B]={})})});var N=Object.keys(P.modules).concat(Object.keys(L.modules)),C=N.reduce(function($,B){var j=q.import("modules/"+B);return j==null?x.error("Cannot load "+B+" module. Are you sure you registered it?"):$[B]=j.DEFAULTS||{},$},{});return L.modules!=null&&L.modules.toolbar&&L.modules.toolbar.constructor!==Object&&(L.modules.toolbar={container:L.modules.toolbar}),L=(0,w.default)(!0,{},q.DEFAULTS,{modules:C},P,L),["bounds","container","scrollingContainer"].forEach(function($){typeof L[$]=="string"&&(L[$]=document.querySelector(L[$]))}),L.modules=Object.keys(L.modules).reduce(function($,B){return L.modules[B]&&($[B]=L.modules[B]),$},{}),L}function U(D,L,P,N){if(this.options.strict&&!this.isEnabled()&&L===s.default.sources.USER)return new y.default;var C=P==null?null:this.getSelection(),$=this.editor.delta,B=D();if(C!=null&&(P===!0&&(P=C.index),N==null?C=H(C,B,L):N!==0&&(C=H(C,P,N,L)),this.setSelection(C,s.default.sources.SILENT)),B.length()>0){var j,M=[s.default.events.TEXT_CHANGE,B,$,L];if((j=this.emitter).emit.apply(j,[s.default.events.EDITOR_CHANGE].concat(M)),L!==s.default.sources.SILENT){var z;(z=this.emitter).emit.apply(z,M)}}return B}function F(D,L,P,N,C){var $={};return typeof D.index=="number"&&typeof D.length=="number"?typeof L!="number"?(C=N,N=P,P=L,L=D.length,D=D.index):(L=D.length,D=D.index):typeof L!="number"&&(C=N,N=P,P=L,L=0),(typeof P=="undefined"?"undefined":i(P))==="object"?($=P,C=N):typeof P=="string"&&(N!=null?$[P]=N:C=P),C=C||s.default.sources.API,[D,L,$,C]}function H(D,L,P,N){if(D==null)return null;var C=void 0,$=void 0;if(L instanceof y.default){var B=[D.index,D.index+D.length].map(function(G){return L.transformPosition(G,N!==s.default.sources.USER)}),j=a(B,2);C=j[0],$=j[1]}else{var M=[D.index,D.index+D.length].map(function(G){return G<L||G===L&&N===s.default.sources.USER?G:P>=0?G+P:Math.max(L,G+P)}),z=a(M,2);C=z[0],$=z[1]}return new p.Range(C,$-C)}t.expandConfig=I,t.overload=F,t.default=q},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function g(v,p){for(var b=0;b<p.length;b++){var h=p[b];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(v,h.key,h)}}return function(v,p,b){return p&&g(v.prototype,p),b&&g(v,b),v}}(),a=function g(v,p,b){v===null&&(v=Function.prototype);var h=Object.getOwnPropertyDescriptor(v,p);if(h===void 0){var w=Object.getPrototypeOf(v);return w===null?void 0:g(w,p,b)}else{if("value"in h)return h.value;var S=h.get;return S===void 0?void 0:S.call(b)}},c=n(7),f=u(c),y=n(0),d=u(y);function u(g){return g&&g.__esModule?g:{default:g}}function o(g,v){if(!(g instanceof v))throw new TypeError("Cannot call a class as a function")}function s(g,v){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:g}function _(g,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);g.prototype=Object.create(v&&v.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(g,v):g.__proto__=v)}var m=function(g){_(v,g);function v(){return o(this,v),s(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return i(v,[{key:"formatAt",value:function(b,h,w,S){if(v.compare(this.statics.blotName,w)<0&&d.default.query(w,d.default.Scope.BLOT)){var E=this.isolate(b,h);S&&E.wrap(w,S)}else a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"formatAt",this).call(this,b,h,w,S)}},{key:"optimize",value:function(b){if(a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"optimize",this).call(this,b),this.parent instanceof v&&v.compare(this.statics.blotName,this.parent.statics.blotName)>0){var h=this.parent.isolate(this.offset(),this.length());this.moveChildren(h),h.wrap(this)}}}],[{key:"compare",value:function(b,h){var w=v.order.indexOf(b),S=v.order.indexOf(h);return w>=0||S>=0?w-S:b===h?0:b<h?-1:1}}]),v}(d.default.Inline);m.allowedChildren=[m,d.default.Embed,f.default],m.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],t.default=m},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(0),a=c(i);function c(o){return o&&o.__esModule?o:{default:o}}function f(o,s){if(!(o instanceof s))throw new TypeError("Cannot call a class as a function")}function y(o,s){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:o}function d(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);o.prototype=Object.create(s&&s.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(o,s):o.__proto__=s)}var u=function(o){d(s,o);function s(){return f(this,s),y(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return s}(a.default.Text);t.default=u},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function p(b,h){for(var w=0;w<h.length;w++){var S=h[w];S.enumerable=S.enumerable||!1,S.configurable=!0,"value"in S&&(S.writable=!0),Object.defineProperty(b,S.key,S)}}return function(b,h,w){return h&&p(b.prototype,h),w&&p(b,w),b}}(),a=function p(b,h,w){b===null&&(b=Function.prototype);var S=Object.getOwnPropertyDescriptor(b,h);if(S===void 0){var E=Object.getPrototypeOf(b);return E===null?void 0:p(E,h,w)}else{if("value"in S)return S.value;var T=S.get;return T===void 0?void 0:T.call(w)}},c=n(54),f=u(c),y=n(10),d=u(y);function u(p){return p&&p.__esModule?p:{default:p}}function o(p,b){if(!(p instanceof b))throw new TypeError("Cannot call a class as a function")}function s(p,b){if(!p)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return b&&(typeof b=="object"||typeof b=="function")?b:p}function _(p,b){if(typeof b!="function"&&b!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof b);p.prototype=Object.create(b&&b.prototype,{constructor:{value:p,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(p,b):p.__proto__=b)}var m=(0,d.default)("quill:events"),g=["selectionchange","mousedown","mouseup","click"];g.forEach(function(p){document.addEventListener(p,function(){for(var b=arguments.length,h=Array(b),w=0;w<b;w++)h[w]=arguments[w];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(S){if(S.__quill&&S.__quill.emitter){var E;(E=S.__quill.emitter).handleDOM.apply(E,h)}})})});var v=function(p){_(b,p);function b(){o(this,b);var h=s(this,(b.__proto__||Object.getPrototypeOf(b)).call(this));return h.listeners={},h.on("error",m.error),h}return i(b,[{key:"emit",value:function(){m.log.apply(m,arguments),a(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(w){for(var S=arguments.length,E=Array(S>1?S-1:0),T=1;T<S;T++)E[T-1]=arguments[T];(this.listeners[w.type]||[]).forEach(function(R){var A=R.node,O=R.handler;(w.target===A||A.contains(w.target))&&O.apply(void 0,[w].concat(E))})}},{key:"listenDOM",value:function(w,S,E){this.listeners[w]||(this.listeners[w]=[]),this.listeners[w].push({node:S,handler:E})}}]),b}(f.default);v.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},v.sources={API:"api",SILENT:"silent",USER:"user"},t.default=v},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});function i(c,f){if(!(c instanceof f))throw new TypeError("Cannot call a class as a function")}var a=function c(f){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};i(this,c),this.quill=f,this.options=y};a.DEFAULTS={},t.default=a},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=["error","warn","log","info"],a="warn";function c(y){if(i.indexOf(y)<=i.indexOf(a)){for(var d,u=arguments.length,o=Array(u>1?u-1:0),s=1;s<u;s++)o[s-1]=arguments[s];(d=console)[y].apply(d,o)}}function f(y){return i.reduce(function(d,u){return d[u]=c.bind(console,u,y),d},{})}c.level=f.level=function(y){a=y},t.default=f},function(e,t,n){var i=Array.prototype.slice,a=n(52),c=n(53),f=e.exports=function(o,s,_){return _||(_={}),o===s?!0:o instanceof Date&&s instanceof Date?o.getTime()===s.getTime():!o||!s||typeof o!="object"&&typeof s!="object"?_.strict?o===s:o==s:u(o,s,_)};function y(o){return o==null}function d(o){return!(!o||typeof o!="object"||typeof o.length!="number"||typeof o.copy!="function"||typeof o.slice!="function"||o.length>0&&typeof o[0]!="number")}function u(o,s,_){var m,g;if(y(o)||y(s)||o.prototype!==s.prototype)return!1;if(c(o))return c(s)?(o=i.call(o),s=i.call(s),f(o,s,_)):!1;if(d(o)){if(!d(s)||o.length!==s.length)return!1;for(m=0;m<o.length;m++)if(o[m]!==s[m])return!1;return!0}try{var v=a(o),p=a(s)}catch(b){return!1}if(v.length!=p.length)return!1;for(v.sort(),p.sort(),m=v.length-1;m>=0;m--)if(v[m]!=p[m])return!1;for(m=v.length-1;m>=0;m--)if(g=v[m],!f(o[g],s[g],_))return!1;return typeof o==typeof s}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(1),a=function(){function c(f,y,d){d===void 0&&(d={}),this.attrName=f,this.keyName=y;var u=i.Scope.TYPE&i.Scope.ATTRIBUTE;d.scope!=null?this.scope=d.scope&i.Scope.LEVEL|u:this.scope=i.Scope.ATTRIBUTE,d.whitelist!=null&&(this.whitelist=d.whitelist)}return c.keys=function(f){return[].map.call(f.attributes,function(y){return y.name})},c.prototype.add=function(f,y){return this.canAdd(f,y)?(f.setAttribute(this.keyName,y),!0):!1},c.prototype.canAdd=function(f,y){var d=i.query(f,i.Scope.BLOT&(this.scope|i.Scope.TYPE));return d==null?!1:this.whitelist==null?!0:typeof y=="string"?this.whitelist.indexOf(y.replace(/["']/g,""))>-1:this.whitelist.indexOf(y)>-1},c.prototype.remove=function(f){f.removeAttribute(this.keyName)},c.prototype.value=function(f){var y=f.getAttribute(this.keyName);return this.canAdd(f,y)&&y?y:""},c}();t.default=a},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Code=void 0;var i=function(){function T(R,A){var O=[],k=!0,x=!1,q=void 0;try{for(var I=R[Symbol.iterator](),U;!(k=(U=I.next()).done)&&(O.push(U.value),!(A&&O.length===A));k=!0);}catch(F){x=!0,q=F}finally{try{!k&&I.return&&I.return()}finally{if(x)throw q}}return O}return function(R,A){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return T(R,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=function(){function T(R,A){for(var O=0;O<A.length;O++){var k=A[O];k.enumerable=k.enumerable||!1,k.configurable=!0,"value"in k&&(k.writable=!0),Object.defineProperty(R,k.key,k)}}return function(R,A,O){return A&&T(R.prototype,A),O&&T(R,O),R}}(),c=function T(R,A,O){R===null&&(R=Function.prototype);var k=Object.getOwnPropertyDescriptor(R,A);if(k===void 0){var x=Object.getPrototypeOf(R);return x===null?void 0:T(x,A,O)}else{if("value"in k)return k.value;var q=k.get;return q===void 0?void 0:q.call(O)}},f=n(2),y=p(f),d=n(0),u=p(d),o=n(4),s=p(o),_=n(6),m=p(_),g=n(7),v=p(g);function p(T){return T&&T.__esModule?T:{default:T}}function b(T,R){if(!(T instanceof R))throw new TypeError("Cannot call a class as a function")}function h(T,R){if(!T)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return R&&(typeof R=="object"||typeof R=="function")?R:T}function w(T,R){if(typeof R!="function"&&R!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof R);T.prototype=Object.create(R&&R.prototype,{constructor:{value:T,enumerable:!1,writable:!0,configurable:!0}}),R&&(Object.setPrototypeOf?Object.setPrototypeOf(T,R):T.__proto__=R)}var S=function(T){w(R,T);function R(){return b(this,R),h(this,(R.__proto__||Object.getPrototypeOf(R)).apply(this,arguments))}return R}(m.default);S.blotName="code",S.tagName="CODE";var E=function(T){w(R,T);function R(){return b(this,R),h(this,(R.__proto__||Object.getPrototypeOf(R)).apply(this,arguments))}return a(R,[{key:"delta",value:function(){var O=this,k=this.domNode.textContent;return k.endsWith(`
`)&&(k=k.slice(0,-1)),k.split(`
`).reduce(function(x,q){return x.insert(q).insert(`
`,O.formats())},new y.default)}},{key:"format",value:function(O,k){if(!(O===this.statics.blotName&&k)){var x=this.descendant(v.default,this.length()-1),q=i(x,1),I=q[0];I!=null&&I.deleteAt(I.length()-1,1),c(R.prototype.__proto__||Object.getPrototypeOf(R.prototype),"format",this).call(this,O,k)}}},{key:"formatAt",value:function(O,k,x,q){if(k!==0&&!(u.default.query(x,u.default.Scope.BLOCK)==null||x===this.statics.blotName&&q===this.statics.formats(this.domNode))){var I=this.newlineIndex(O);if(!(I<0||I>=O+k)){var U=this.newlineIndex(O,!0)+1,F=I-U+1,H=this.isolate(U,F),D=H.next;H.format(x,q),D instanceof R&&D.formatAt(0,O-U+k-F,x,q)}}}},{key:"insertAt",value:function(O,k,x){if(x==null){var q=this.descendant(v.default,O),I=i(q,2),U=I[0],F=I[1];U.insertAt(F,k)}}},{key:"length",value:function(){var O=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?O:O+1}},{key:"newlineIndex",value:function(O){var k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(k)return this.domNode.textContent.slice(0,O).lastIndexOf(`
`);var x=this.domNode.textContent.slice(O).indexOf(`
`);return x>-1?O+x:-1}},{key:"optimize",value:function(O){this.domNode.textContent.endsWith(`
`)||this.appendChild(u.default.create("text",`
`)),c(R.prototype.__proto__||Object.getPrototypeOf(R.prototype),"optimize",this).call(this,O);var k=this.next;k!=null&&k.prev===this&&k.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===k.statics.formats(k.domNode)&&(k.optimize(O),k.moveChildren(this),k.remove())}},{key:"replace",value:function(O){c(R.prototype.__proto__||Object.getPrototypeOf(R.prototype),"replace",this).call(this,O),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(k){var x=u.default.find(k);x==null?k.parentNode.removeChild(k):x instanceof u.default.Embed?x.remove():x.unwrap()})}}],[{key:"create",value:function(O){var k=c(R.__proto__||Object.getPrototypeOf(R),"create",this).call(this,O);return k.setAttribute("spellcheck",!1),k}},{key:"formats",value:function(){return!0}}]),R}(s.default);E.blotName="code-block",E.tagName="PRE",E.TAB="  ",t.Code=S,t.default=E},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(D){return typeof D}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},a=function(){function D(L,P){var N=[],C=!0,$=!1,B=void 0;try{for(var j=L[Symbol.iterator](),M;!(C=(M=j.next()).done)&&(N.push(M.value),!(P&&N.length===P));C=!0);}catch(z){$=!0,B=z}finally{try{!C&&j.return&&j.return()}finally{if($)throw B}}return N}return function(L,P){if(Array.isArray(L))return L;if(Symbol.iterator in Object(L))return D(L,P);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),c=function(){function D(L,P){for(var N=0;N<P.length;N++){var C=P[N];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(L,C.key,C)}}return function(L,P,N){return P&&D(L.prototype,P),N&&D(L,N),L}}(),f=n(2),y=k(f),d=n(20),u=k(d),o=n(0),s=k(o),_=n(13),m=k(_),g=n(24),v=k(g),p=n(4),b=k(p),h=n(16),w=k(h),S=n(21),E=k(S),T=n(11),R=k(T),A=n(3),O=k(A);function k(D){return D&&D.__esModule?D:{default:D}}function x(D,L,P){return L in D?Object.defineProperty(D,L,{value:P,enumerable:!0,configurable:!0,writable:!0}):D[L]=P,D}function q(D,L){if(!(D instanceof L))throw new TypeError("Cannot call a class as a function")}var I=/^[ -~]*$/,U=function(){function D(L){q(this,D),this.scroll=L,this.delta=this.getDelta()}return c(D,[{key:"applyDelta",value:function(P){var N=this,C=!1;this.scroll.update();var $=this.scroll.length();return this.scroll.batchStart(),P=H(P),P.reduce(function(B,j){var M=j.retain||j.delete||j.insert.length||1,z=j.attributes||{};if(j.insert!=null){if(typeof j.insert=="string"){var G=j.insert;G.endsWith(`
`)&&C&&(C=!1,G=G.slice(0,-1)),B>=$&&!G.endsWith(`
`)&&(C=!0),N.scroll.insertAt(B,G);var J=N.scroll.line(B),X=a(J,2),ee=X[0],te=X[1],se=(0,O.default)({},(0,p.bubbleFormats)(ee));if(ee instanceof b.default){var le=ee.descendant(s.default.Leaf,te),ye=a(le,1),he=ye[0];se=(0,O.default)(se,(0,p.bubbleFormats)(he))}z=u.default.attributes.diff(se,z)||{}}else if(i(j.insert)==="object"){var W=Object.keys(j.insert)[0];if(W==null)return B;N.scroll.insertAt(B,W,j.insert[W])}$+=M}return Object.keys(z).forEach(function(V){N.scroll.formatAt(B,M,V,z[V])}),B+M},0),P.reduce(function(B,j){return typeof j.delete=="number"?(N.scroll.deleteAt(B,j.delete),B):B+(j.retain||j.insert.length||1)},0),this.scroll.batchEnd(),this.update(P)}},{key:"deleteText",value:function(P,N){return this.scroll.deleteAt(P,N),this.update(new y.default().retain(P).delete(N))}},{key:"formatLine",value:function(P,N){var C=this,$=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys($).forEach(function(B){if(!(C.scroll.whitelist!=null&&!C.scroll.whitelist[B])){var j=C.scroll.lines(P,Math.max(N,1)),M=N;j.forEach(function(z){var G=z.length();if(!(z instanceof m.default))z.format(B,$[B]);else{var J=P-z.offset(C.scroll),X=z.newlineIndex(J+M)-J+1;z.formatAt(J,X,B,$[B])}M-=G})}}),this.scroll.optimize(),this.update(new y.default().retain(P).retain(N,(0,E.default)($)))}},{key:"formatText",value:function(P,N){var C=this,$=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys($).forEach(function(B){C.scroll.formatAt(P,N,B,$[B])}),this.update(new y.default().retain(P).retain(N,(0,E.default)($)))}},{key:"getContents",value:function(P,N){return this.delta.slice(P,P+N)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(P,N){return P.concat(N.delta())},new y.default)}},{key:"getFormat",value:function(P){var N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,C=[],$=[];N===0?this.scroll.path(P).forEach(function(j){var M=a(j,1),z=M[0];z instanceof b.default?C.push(z):z instanceof s.default.Leaf&&$.push(z)}):(C=this.scroll.lines(P,N),$=this.scroll.descendants(s.default.Leaf,P,N));var B=[C,$].map(function(j){if(j.length===0)return{};for(var M=(0,p.bubbleFormats)(j.shift());Object.keys(M).length>0;){var z=j.shift();if(z==null)return M;M=F((0,p.bubbleFormats)(z),M)}return M});return O.default.apply(O.default,B)}},{key:"getText",value:function(P,N){return this.getContents(P,N).filter(function(C){return typeof C.insert=="string"}).map(function(C){return C.insert}).join("")}},{key:"insertEmbed",value:function(P,N,C){return this.scroll.insertAt(P,N,C),this.update(new y.default().retain(P).insert(x({},N,C)))}},{key:"insertText",value:function(P,N){var C=this,$=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return N=N.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(P,N),Object.keys($).forEach(function(B){C.scroll.formatAt(P,N.length,B,$[B])}),this.update(new y.default().retain(P).insert(N,(0,E.default)($)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var P=this.scroll.children.head;return P.statics.blotName!==b.default.blotName||P.children.length>1?!1:P.children.head instanceof w.default}},{key:"removeFormat",value:function(P,N){var C=this.getText(P,N),$=this.scroll.line(P+N),B=a($,2),j=B[0],M=B[1],z=0,G=new y.default;j!=null&&(j instanceof m.default?z=j.newlineIndex(M)-M+1:z=j.length()-M,G=j.delta().slice(M,M+z-1).insert(`
`));var J=this.getContents(P,N+z),X=J.diff(new y.default().insert(C).concat(G)),ee=new y.default().retain(P).concat(X);return this.applyDelta(ee)}},{key:"update",value:function(P){var N=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],C=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,$=this.delta;if(N.length===1&&N[0].type==="characterData"&&N[0].target.data.match(I)&&s.default.find(N[0].target)){var B=s.default.find(N[0].target),j=(0,p.bubbleFormats)(B),M=B.offset(this.scroll),z=N[0].oldValue.replace(v.default.CONTENTS,""),G=new y.default().insert(z),J=new y.default().insert(B.value()),X=new y.default().retain(M).concat(G.diff(J,C));P=X.reduce(function(ee,te){return te.insert?ee.insert(te.insert,j):ee.push(te)},new y.default),this.delta=$.compose(P)}else this.delta=this.getDelta(),(!P||!(0,R.default)($.compose(P),this.delta))&&(P=$.diff(this.delta,C));return P}}]),D}();function F(D,L){return Object.keys(L).reduce(function(P,N){return D[N]==null||(L[N]===D[N]?P[N]=L[N]:Array.isArray(L[N])?L[N].indexOf(D[N])<0&&(P[N]=L[N].concat([D[N]])):P[N]=[L[N],D[N]]),P},{})}function H(D){return D.reduce(function(L,P){if(P.insert===1){var N=(0,E.default)(P.attributes);return delete N.image,L.insert({image:P.attributes.image},N)}if(P.attributes!=null&&(P.attributes.list===!0||P.attributes.bullet===!0)&&(P=(0,E.default)(P),P.attributes.list?P.attributes.list="ordered":(P.attributes.list="bullet",delete P.attributes.bullet)),typeof P.insert=="string"){var C=P.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return L.insert(C,P.attributes)}return L.push(P)},new y.default)}t.default=U},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Range=void 0;var i=function(){function T(R,A){var O=[],k=!0,x=!1,q=void 0;try{for(var I=R[Symbol.iterator](),U;!(k=(U=I.next()).done)&&(O.push(U.value),!(A&&O.length===A));k=!0);}catch(F){x=!0,q=F}finally{try{!k&&I.return&&I.return()}finally{if(x)throw q}}return O}return function(R,A){if(Array.isArray(R))return R;if(Symbol.iterator in Object(R))return T(R,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=function(){function T(R,A){for(var O=0;O<A.length;O++){var k=A[O];k.enumerable=k.enumerable||!1,k.configurable=!0,"value"in k&&(k.writable=!0),Object.defineProperty(R,k.key,k)}}return function(R,A,O){return A&&T(R.prototype,A),O&&T(R,O),R}}(),c=n(0),f=v(c),y=n(21),d=v(y),u=n(11),o=v(u),s=n(8),_=v(s),m=n(10),g=v(m);function v(T){return T&&T.__esModule?T:{default:T}}function p(T){if(Array.isArray(T)){for(var R=0,A=Array(T.length);R<T.length;R++)A[R]=T[R];return A}else return Array.from(T)}function b(T,R){if(!(T instanceof R))throw new TypeError("Cannot call a class as a function")}var h=(0,g.default)("quill:selection"),w=function T(R){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;b(this,T),this.index=R,this.length=A},S=function(){function T(R,A){var O=this;b(this,T),this.emitter=A,this.scroll=R,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=f.default.create("cursor",this),this.lastRange=this.savedRange=new w(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){O.mouseDown||setTimeout(O.update.bind(O,_.default.sources.USER),1)}),this.emitter.on(_.default.events.EDITOR_CHANGE,function(k,x){k===_.default.events.TEXT_CHANGE&&x.length()>0&&O.update(_.default.sources.SILENT)}),this.emitter.on(_.default.events.SCROLL_BEFORE_UPDATE,function(){if(O.hasFocus()){var k=O.getNativeRange();k!=null&&k.start.node!==O.cursor.textNode&&O.emitter.once(_.default.events.SCROLL_UPDATE,function(){try{O.setNativeRange(k.start.node,k.start.offset,k.end.node,k.end.offset)}catch(x){}})}}),this.emitter.on(_.default.events.SCROLL_OPTIMIZE,function(k,x){if(x.range){var q=x.range,I=q.startNode,U=q.startOffset,F=q.endNode,H=q.endOffset;O.setNativeRange(I,U,F,H)}}),this.update(_.default.sources.SILENT)}return a(T,[{key:"handleComposition",value:function(){var A=this;this.root.addEventListener("compositionstart",function(){A.composing=!0}),this.root.addEventListener("compositionend",function(){if(A.composing=!1,A.cursor.parent){var O=A.cursor.restore();if(!O)return;setTimeout(function(){A.setNativeRange(O.startNode,O.startOffset,O.endNode,O.endOffset)},1)}})}},{key:"handleDragging",value:function(){var A=this;this.emitter.listenDOM("mousedown",document.body,function(){A.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){A.mouseDown=!1,A.update(_.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(A,O){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[A])){this.scroll.update();var k=this.getNativeRange();if(!(k==null||!k.native.collapsed||f.default.query(A,f.default.Scope.BLOCK))){if(k.start.node!==this.cursor.textNode){var x=f.default.find(k.start.node,!1);if(x==null)return;if(x instanceof f.default.Leaf){var q=x.split(k.start.offset);x.parent.insertBefore(this.cursor,q)}else x.insertBefore(this.cursor,k.start.node);this.cursor.attach()}this.cursor.format(A,O),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(A){var O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,k=this.scroll.length();A=Math.min(A,k-1),O=Math.min(A+O,k-1)-A;var x=void 0,q=this.scroll.leaf(A),I=i(q,2),U=I[0],F=I[1];if(U==null)return null;var H=U.position(F,!0),D=i(H,2);x=D[0],F=D[1];var L=document.createRange();if(O>0){L.setStart(x,F);var P=this.scroll.leaf(A+O),N=i(P,2);if(U=N[0],F=N[1],U==null)return null;var C=U.position(F,!0),$=i(C,2);return x=$[0],F=$[1],L.setEnd(x,F),L.getBoundingClientRect()}else{var B="left",j=void 0;return x instanceof Text?(F<x.data.length?(L.setStart(x,F),L.setEnd(x,F+1)):(L.setStart(x,F-1),L.setEnd(x,F),B="right"),j=L.getBoundingClientRect()):(j=U.domNode.getBoundingClientRect(),F>0&&(B="right")),{bottom:j.top+j.height,height:j.height,left:j[B],right:j[B],top:j.top,width:0}}}},{key:"getNativeRange",value:function(){var A=document.getSelection();if(A==null||A.rangeCount<=0)return null;var O=A.getRangeAt(0);if(O==null)return null;var k=this.normalizeNative(O);return h.info("getNativeRange",k),k}},{key:"getRange",value:function(){var A=this.getNativeRange();if(A==null)return[null,null];var O=this.normalizedToRange(A);return[O,A]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(A){var O=this,k=[[A.start.node,A.start.offset]];A.native.collapsed||k.push([A.end.node,A.end.offset]);var x=k.map(function(U){var F=i(U,2),H=F[0],D=F[1],L=f.default.find(H,!0),P=L.offset(O.scroll);return D===0?P:L instanceof f.default.Container?P+L.length():P+L.index(H,D)}),q=Math.min(Math.max.apply(Math,p(x)),this.scroll.length()-1),I=Math.min.apply(Math,[q].concat(p(x)));return new w(I,q-I)}},{key:"normalizeNative",value:function(A){if(!E(this.root,A.startContainer)||!A.collapsed&&!E(this.root,A.endContainer))return null;var O={start:{node:A.startContainer,offset:A.startOffset},end:{node:A.endContainer,offset:A.endOffset},native:A};return[O.start,O.end].forEach(function(k){for(var x=k.node,q=k.offset;!(x instanceof Text)&&x.childNodes.length>0;)if(x.childNodes.length>q)x=x.childNodes[q],q=0;else if(x.childNodes.length===q)x=x.lastChild,q=x instanceof Text?x.data.length:x.childNodes.length+1;else break;k.node=x,k.offset=q}),O}},{key:"rangeToNative",value:function(A){var O=this,k=A.collapsed?[A.index]:[A.index,A.index+A.length],x=[],q=this.scroll.length();return k.forEach(function(I,U){I=Math.min(q-1,I);var F=void 0,H=O.scroll.leaf(I),D=i(H,2),L=D[0],P=D[1],N=L.position(P,U!==0),C=i(N,2);F=C[0],P=C[1],x.push(F,P)}),x.length<2&&(x=x.concat(x)),x}},{key:"scrollIntoView",value:function(A){var O=this.lastRange;if(O!=null){var k=this.getBounds(O.index,O.length);if(k!=null){var x=this.scroll.length()-1,q=this.scroll.line(Math.min(O.index,x)),I=i(q,1),U=I[0],F=U;if(O.length>0){var H=this.scroll.line(Math.min(O.index+O.length,x)),D=i(H,1);F=D[0]}if(!(U==null||F==null)){var L=A.getBoundingClientRect();k.top<L.top?A.scrollTop-=L.top-k.top:k.bottom>L.bottom&&(A.scrollTop+=k.bottom-L.bottom)}}}}},{key:"setNativeRange",value:function(A,O){var k=arguments.length>2&&arguments[2]!==void 0?arguments[2]:A,x=arguments.length>3&&arguments[3]!==void 0?arguments[3]:O,q=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(h.info("setNativeRange",A,O,k,x),!(A!=null&&(this.root.parentNode==null||A.parentNode==null||k.parentNode==null))){var I=document.getSelection();if(I!=null)if(A!=null){this.hasFocus()||this.root.focus();var U=(this.getNativeRange()||{}).native;if(U==null||q||A!==U.startContainer||O!==U.startOffset||k!==U.endContainer||x!==U.endOffset){A.tagName=="BR"&&(O=[].indexOf.call(A.parentNode.childNodes,A),A=A.parentNode),k.tagName=="BR"&&(x=[].indexOf.call(k.parentNode.childNodes,k),k=k.parentNode);var F=document.createRange();F.setStart(A,O),F.setEnd(k,x),I.removeAllRanges(),I.addRange(F)}}else I.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(A){var O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,k=arguments.length>2&&arguments[2]!==void 0?arguments[2]:_.default.sources.API;if(typeof O=="string"&&(k=O,O=!1),h.info("setRange",A),A!=null){var x=this.rangeToNative(A);this.setNativeRange.apply(this,p(x).concat([O]))}else this.setNativeRange(null);this.update(k)}},{key:"update",value:function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:_.default.sources.USER,O=this.lastRange,k=this.getRange(),x=i(k,2),q=x[0],I=x[1];if(this.lastRange=q,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,o.default)(O,this.lastRange)){var U;!this.composing&&I!=null&&I.native.collapsed&&I.start.node!==this.cursor.textNode&&this.cursor.restore();var F=[_.default.events.SELECTION_CHANGE,(0,d.default)(this.lastRange),(0,d.default)(O),A];if((U=this.emitter).emit.apply(U,[_.default.events.EDITOR_CHANGE].concat(F)),A!==_.default.sources.SILENT){var H;(H=this.emitter).emit.apply(H,F)}}}}]),T}();function E(T,R){try{R.parentNode}catch(A){return!1}return R instanceof Text&&(R=R.parentNode),T.contains(R)}t.Range=w,t.default=S},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function _(m,g){for(var v=0;v<g.length;v++){var p=g[v];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(m,p.key,p)}}return function(m,g,v){return g&&_(m.prototype,g),v&&_(m,v),m}}(),a=function _(m,g,v){m===null&&(m=Function.prototype);var p=Object.getOwnPropertyDescriptor(m,g);if(p===void 0){var b=Object.getPrototypeOf(m);return b===null?void 0:_(b,g,v)}else{if("value"in p)return p.value;var h=p.get;return h===void 0?void 0:h.call(v)}},c=n(0),f=y(c);function y(_){return _&&_.__esModule?_:{default:_}}function d(_,m){if(!(_ instanceof m))throw new TypeError("Cannot call a class as a function")}function u(_,m){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:_}function o(_,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);_.prototype=Object.create(m&&m.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(_,m):_.__proto__=m)}var s=function(_){o(m,_);function m(){return d(this,m),u(this,(m.__proto__||Object.getPrototypeOf(m)).apply(this,arguments))}return i(m,[{key:"insertInto",value:function(v,p){v.children.length===0?a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"insertInto",this).call(this,v,p):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),m}(f.default.Embed);s.blotName="break",s.tagName="BR",t.default=s},function(e,t,n){var i=this&&this.__extends||function(){var u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,s){o.__proto__=s}||function(o,s){for(var _ in s)s.hasOwnProperty(_)&&(o[_]=s[_])};return function(o,s){u(o,s);function _(){this.constructor=o}o.prototype=s===null?Object.create(s):(_.prototype=s.prototype,new _)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(44),c=n(30),f=n(1),y=function(u){i(o,u);function o(s){var _=u.call(this,s)||this;return _.build(),_}return o.prototype.appendChild=function(s){this.insertBefore(s)},o.prototype.attach=function(){u.prototype.attach.call(this),this.children.forEach(function(s){s.attach()})},o.prototype.build=function(){var s=this;this.children=new a.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(_){try{var m=d(_);s.insertBefore(m,s.children.head||void 0)}catch(g){if(g instanceof f.ParchmentError)return;throw g}})},o.prototype.deleteAt=function(s,_){if(s===0&&_===this.length())return this.remove();this.children.forEachAt(s,_,function(m,g,v){m.deleteAt(g,v)})},o.prototype.descendant=function(s,_){var m=this.children.find(_),g=m[0],v=m[1];return s.blotName==null&&s(g)||s.blotName!=null&&g instanceof s?[g,v]:g instanceof o?g.descendant(s,v):[null,-1]},o.prototype.descendants=function(s,_,m){_===void 0&&(_=0),m===void 0&&(m=Number.MAX_VALUE);var g=[],v=m;return this.children.forEachAt(_,m,function(p,b,h){(s.blotName==null&&s(p)||s.blotName!=null&&p instanceof s)&&g.push(p),p instanceof o&&(g=g.concat(p.descendants(s,b,v))),v-=h}),g},o.prototype.detach=function(){this.children.forEach(function(s){s.detach()}),u.prototype.detach.call(this)},o.prototype.formatAt=function(s,_,m,g){this.children.forEachAt(s,_,function(v,p,b){v.formatAt(p,b,m,g)})},o.prototype.insertAt=function(s,_,m){var g=this.children.find(s),v=g[0],p=g[1];if(v)v.insertAt(p,_,m);else{var b=m==null?f.create("text",_):f.create(_,m);this.appendChild(b)}},o.prototype.insertBefore=function(s,_){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(m){return s instanceof m}))throw new f.ParchmentError("Cannot insert "+s.statics.blotName+" into "+this.statics.blotName);s.insertInto(this,_)},o.prototype.length=function(){return this.children.reduce(function(s,_){return s+_.length()},0)},o.prototype.moveChildren=function(s,_){this.children.forEach(function(m){s.insertBefore(m,_)})},o.prototype.optimize=function(s){if(u.prototype.optimize.call(this,s),this.children.length===0)if(this.statics.defaultChild!=null){var _=f.create(this.statics.defaultChild);this.appendChild(_),_.optimize(s)}else this.remove()},o.prototype.path=function(s,_){_===void 0&&(_=!1);var m=this.children.find(s,_),g=m[0],v=m[1],p=[[this,s]];return g instanceof o?p.concat(g.path(v,_)):(g!=null&&p.push([g,v]),p)},o.prototype.removeChild=function(s){this.children.remove(s)},o.prototype.replace=function(s){s instanceof o&&s.moveChildren(this),u.prototype.replace.call(this,s)},o.prototype.split=function(s,_){if(_===void 0&&(_=!1),!_){if(s===0)return this;if(s===this.length())return this.next}var m=this.clone();return this.parent.insertBefore(m,this.next),this.children.forEachAt(s,this.length(),function(g,v,p){g=g.split(v,_),m.appendChild(g)}),m},o.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},o.prototype.update=function(s,_){var m=this,g=[],v=[];s.forEach(function(p){p.target===m.domNode&&p.type==="childList"&&(g.push.apply(g,p.addedNodes),v.push.apply(v,p.removedNodes))}),v.forEach(function(p){if(!(p.parentNode!=null&&p.tagName!=="IFRAME"&&document.body.compareDocumentPosition(p)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var b=f.find(p);b!=null&&(b.domNode.parentNode==null||b.domNode.parentNode===m.domNode)&&b.detach()}}),g.filter(function(p){return p.parentNode==m.domNode}).sort(function(p,b){return p===b?0:p.compareDocumentPosition(b)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(p){var b=null;p.nextSibling!=null&&(b=f.find(p.nextSibling));var h=d(p);(h.next!=b||h.next==null)&&(h.parent!=null&&h.parent.removeChild(m),m.insertBefore(h,b||void 0))})},o}(c.default);function d(u){var o=f.find(u);if(o==null)try{o=f.create(u)}catch(s){o=f.create(f.Scope.INLINE),[].slice.call(u.childNodes).forEach(function(_){o.domNode.appendChild(_)}),u.parentNode&&u.parentNode.replaceChild(o.domNode,u),o.attach()}return o}t.default=y},function(e,t,n){var i=this&&this.__extends||function(){var u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,s){o.__proto__=s}||function(o,s){for(var _ in s)s.hasOwnProperty(_)&&(o[_]=s[_])};return function(o,s){u(o,s);function _(){this.constructor=o}o.prototype=s===null?Object.create(s):(_.prototype=s.prototype,new _)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(12),c=n(31),f=n(17),y=n(1),d=function(u){i(o,u);function o(s){var _=u.call(this,s)||this;return _.attributes=new c.default(_.domNode),_}return o.formats=function(s){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return s.tagName.toLowerCase()},o.prototype.format=function(s,_){var m=y.query(s);m instanceof a.default?this.attributes.attribute(m,_):_&&m!=null&&(s!==this.statics.blotName||this.formats()[s]!==_)&&this.replaceWith(s,_)},o.prototype.formats=function(){var s=this.attributes.values(),_=this.statics.formats(this.domNode);return _!=null&&(s[this.statics.blotName]=_),s},o.prototype.replaceWith=function(s,_){var m=u.prototype.replaceWith.call(this,s,_);return this.attributes.copy(m),m},o.prototype.update=function(s,_){var m=this;u.prototype.update.call(this,s,_),s.some(function(g){return g.target===m.domNode&&g.type==="attributes"})&&this.attributes.build()},o.prototype.wrap=function(s,_){var m=u.prototype.wrap.call(this,s,_);return m instanceof o&&m.statics.scope===this.statics.scope&&this.attributes.move(m),m},o}(f.default);t.default=d},function(e,t,n){var i=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,u){d.__proto__=u}||function(d,u){for(var o in u)u.hasOwnProperty(o)&&(d[o]=u[o])};return function(d,u){y(d,u);function o(){this.constructor=d}d.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(30),c=n(1),f=function(y){i(d,y);function d(){return y!==null&&y.apply(this,arguments)||this}return d.value=function(u){return!0},d.prototype.index=function(u,o){return this.domNode===u||this.domNode.compareDocumentPosition(u)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(o,1):-1},d.prototype.position=function(u,o){var s=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return u>0&&(s+=1),[this.parent.domNode,s]},d.prototype.value=function(){var u;return u={},u[this.statics.blotName]=this.statics.value(this.domNode)||!0,u},d.scope=c.Scope.INLINE_BLOT,d}(a.default);t.default=f},function(e,t,n){var i=n(11),a=n(3),c={attributes:{compose:function(y,d,u){typeof y!="object"&&(y={}),typeof d!="object"&&(d={});var o=a(!0,{},d);u||(o=Object.keys(o).reduce(function(_,m){return o[m]!=null&&(_[m]=o[m]),_},{}));for(var s in y)y[s]!==void 0&&d[s]===void 0&&(o[s]=y[s]);return Object.keys(o).length>0?o:void 0},diff:function(y,d){typeof y!="object"&&(y={}),typeof d!="object"&&(d={});var u=Object.keys(y).concat(Object.keys(d)).reduce(function(o,s){return i(y[s],d[s])||(o[s]=d[s]===void 0?null:d[s]),o},{});return Object.keys(u).length>0?u:void 0},transform:function(y,d,u){if(typeof y!="object")return d;if(typeof d=="object"){if(!u)return d;var o=Object.keys(d).reduce(function(s,_){return y[_]===void 0&&(s[_]=d[_]),s},{});return Object.keys(o).length>0?o:void 0}}},iterator:function(y){return new f(y)},length:function(y){return typeof y.delete=="number"?y.delete:typeof y.retain=="number"?y.retain:typeof y.insert=="string"?y.insert.length:1}};function f(y){this.ops=y,this.index=0,this.offset=0}f.prototype.hasNext=function(){return this.peekLength()<1/0},f.prototype.next=function(y){y||(y=1/0);var d=this.ops[this.index];if(d){var u=this.offset,o=c.length(d);if(y>=o-u?(y=o-u,this.index+=1,this.offset=0):this.offset+=y,typeof d.delete=="number")return{delete:y};var s={};return d.attributes&&(s.attributes=d.attributes),typeof d.retain=="number"?s.retain=y:typeof d.insert=="string"?s.insert=d.insert.substr(u,y):s.insert=d.insert,s}else return{retain:1/0}},f.prototype.peek=function(){return this.ops[this.index]},f.prototype.peekLength=function(){return this.ops[this.index]?c.length(this.ops[this.index])-this.offset:1/0},f.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},f.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var y=this.offset,d=this.index,u=this.next(),o=this.ops.slice(this.index);return this.offset=y,this.index=d,[u].concat(o)}else return[]},e.exports=c},function(e,t){var n=function(){function i(m,g){return g!=null&&m instanceof g}var a;try{a=Map}catch(m){a=function(){}}var c;try{c=Set}catch(m){c=function(){}}var f;try{f=Promise}catch(m){f=function(){}}function y(m,g,v,p,b){typeof g=="object"&&(v=g.depth,p=g.prototype,b=g.includeNonEnumerable,g=g.circular);var h=[],w=[],S=typeof Buffer!="undefined";typeof g=="undefined"&&(g=!0),typeof v=="undefined"&&(v=1/0);function E(T,R){if(T===null)return null;if(R===0)return T;var A,O;if(typeof T!="object")return T;if(i(T,a))A=new a;else if(i(T,c))A=new c;else if(i(T,f))A=new f(function(L,P){T.then(function(N){L(E(N,R-1))},function(N){P(E(N,R-1))})});else if(y.__isArray(T))A=[];else if(y.__isRegExp(T))A=new RegExp(T.source,_(T)),T.lastIndex&&(A.lastIndex=T.lastIndex);else if(y.__isDate(T))A=new Date(T.getTime());else{if(S&&Buffer.isBuffer(T))return Buffer.allocUnsafe?A=Buffer.allocUnsafe(T.length):A=new Buffer(T.length),T.copy(A),A;i(T,Error)?A=Object.create(T):typeof p=="undefined"?(O=Object.getPrototypeOf(T),A=Object.create(O)):(A=Object.create(p),O=p)}if(g){var k=h.indexOf(T);if(k!=-1)return w[k];h.push(T),w.push(A)}i(T,a)&&T.forEach(function(L,P){var N=E(P,R-1),C=E(L,R-1);A.set(N,C)}),i(T,c)&&T.forEach(function(L){var P=E(L,R-1);A.add(P)});for(var x in T){var q;O&&(q=Object.getOwnPropertyDescriptor(O,x)),!(q&&q.set==null)&&(A[x]=E(T[x],R-1))}if(Object.getOwnPropertySymbols)for(var I=Object.getOwnPropertySymbols(T),x=0;x<I.length;x++){var U=I[x],F=Object.getOwnPropertyDescriptor(T,U);F&&!F.enumerable&&!b||(A[U]=E(T[U],R-1),F.enumerable||Object.defineProperty(A,U,{enumerable:!1}))}if(b)for(var H=Object.getOwnPropertyNames(T),x=0;x<H.length;x++){var D=H[x],F=Object.getOwnPropertyDescriptor(T,D);F&&F.enumerable||(A[D]=E(T[D],R-1),Object.defineProperty(A,D,{enumerable:!1}))}return A}return E(m,v)}y.clonePrototype=function(g){if(g===null)return null;var v=function(){};return v.prototype=g,new v};function d(m){return Object.prototype.toString.call(m)}y.__objToStr=d;function u(m){return typeof m=="object"&&d(m)==="[object Date]"}y.__isDate=u;function o(m){return typeof m=="object"&&d(m)==="[object Array]"}y.__isArray=o;function s(m){return typeof m=="object"&&d(m)==="[object RegExp]"}y.__isRegExp=s;function _(m){var g="";return m.global&&(g+="g"),m.ignoreCase&&(g+="i"),m.multiline&&(g+="m"),g}return y.__getRegExpFlags=_,y}();typeof e=="object"&&e.exports&&(e.exports=n)},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function A(O,k){var x=[],q=!0,I=!1,U=void 0;try{for(var F=O[Symbol.iterator](),H;!(q=(H=F.next()).done)&&(x.push(H.value),!(k&&x.length===k));q=!0);}catch(D){I=!0,U=D}finally{try{!q&&F.return&&F.return()}finally{if(I)throw U}}return x}return function(O,k){if(Array.isArray(O))return O;if(Symbol.iterator in Object(O))return A(O,k);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=function(){function A(O,k){for(var x=0;x<k.length;x++){var q=k[x];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(O,q.key,q)}}return function(O,k,x){return k&&A(O.prototype,k),x&&A(O,x),O}}(),c=function A(O,k,x){O===null&&(O=Function.prototype);var q=Object.getOwnPropertyDescriptor(O,k);if(q===void 0){var I=Object.getPrototypeOf(O);return I===null?void 0:A(I,k,x)}else{if("value"in q)return q.value;var U=q.get;return U===void 0?void 0:U.call(x)}},f=n(0),y=h(f),d=n(8),u=h(d),o=n(4),s=h(o),_=n(16),m=h(_),g=n(13),v=h(g),p=n(25),b=h(p);function h(A){return A&&A.__esModule?A:{default:A}}function w(A,O){if(!(A instanceof O))throw new TypeError("Cannot call a class as a function")}function S(A,O){if(!A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O&&(typeof O=="object"||typeof O=="function")?O:A}function E(A,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof O);A.prototype=Object.create(O&&O.prototype,{constructor:{value:A,enumerable:!1,writable:!0,configurable:!0}}),O&&(Object.setPrototypeOf?Object.setPrototypeOf(A,O):A.__proto__=O)}function T(A){return A instanceof s.default||A instanceof o.BlockEmbed}var R=function(A){E(O,A);function O(k,x){w(this,O);var q=S(this,(O.__proto__||Object.getPrototypeOf(O)).call(this,k));return q.emitter=x.emitter,Array.isArray(x.whitelist)&&(q.whitelist=x.whitelist.reduce(function(I,U){return I[U]=!0,I},{})),q.domNode.addEventListener("DOMNodeInserted",function(){}),q.optimize(),q.enable(),q}return a(O,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(x,q){var I=this.line(x),U=i(I,2),F=U[0],H=U[1],D=this.line(x+q),L=i(D,1),P=L[0];if(c(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"deleteAt",this).call(this,x,q),P!=null&&F!==P&&H>0){if(F instanceof o.BlockEmbed||P instanceof o.BlockEmbed){this.optimize();return}if(F instanceof v.default){var N=F.newlineIndex(F.length(),!0);if(N>-1&&(F=F.split(N+1),F===P)){this.optimize();return}}else if(P instanceof v.default){var C=P.newlineIndex(0);C>-1&&P.split(C+1)}var $=P.children.head instanceof m.default?null:P.children.head;F.moveChildren(P,$),F.remove()}this.optimize()}},{key:"enable",value:function(){var x=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",x)}},{key:"formatAt",value:function(x,q,I,U){this.whitelist!=null&&!this.whitelist[I]||(c(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"formatAt",this).call(this,x,q,I,U),this.optimize())}},{key:"insertAt",value:function(x,q,I){if(!(I!=null&&this.whitelist!=null&&!this.whitelist[q])){if(x>=this.length())if(I==null||y.default.query(q,y.default.Scope.BLOCK)==null){var U=y.default.create(this.statics.defaultChild);this.appendChild(U),I==null&&q.endsWith(`
`)&&(q=q.slice(0,-1)),U.insertAt(0,q,I)}else{var F=y.default.create(q,I);this.appendChild(F)}else c(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"insertAt",this).call(this,x,q,I);this.optimize()}}},{key:"insertBefore",value:function(x,q){if(x.statics.scope===y.default.Scope.INLINE_BLOT){var I=y.default.create(this.statics.defaultChild);I.appendChild(x),x=I}c(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"insertBefore",this).call(this,x,q)}},{key:"leaf",value:function(x){return this.path(x).pop()||[null,-1]}},{key:"line",value:function(x){return x===this.length()?this.line(x-1):this.descendant(T,x)}},{key:"lines",value:function(){var x=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,I=function U(F,H,D){var L=[],P=D;return F.children.forEachAt(H,D,function(N,C,$){T(N)?L.push(N):N instanceof y.default.Container&&(L=L.concat(U(N,C,P))),P-=$}),L};return I(this,x,q)}},{key:"optimize",value:function(){var x=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(c(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"optimize",this).call(this,x,q),x.length>0&&this.emitter.emit(u.default.events.SCROLL_OPTIMIZE,x,q))}},{key:"path",value:function(x){return c(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"path",this).call(this,x).slice(1)}},{key:"update",value:function(x){if(this.batch!==!0){var q=u.default.sources.USER;typeof x=="string"&&(q=x),Array.isArray(x)||(x=this.observer.takeRecords()),x.length>0&&this.emitter.emit(u.default.events.SCROLL_BEFORE_UPDATE,q,x),c(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"update",this).call(this,x.concat([])),x.length>0&&this.emitter.emit(u.default.events.SCROLL_UPDATE,q,x)}}}]),O}(y.default.Scroll);R.blotName="scroll",R.className="ql-editor",R.tagName="DIV",R.defaultChild="block",R.allowedChildren=[s.default,o.BlockEmbed,b.default],t.default=R},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SHORTKEY=t.default=void 0;var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(j){return typeof j}:function(j){return j&&typeof Symbol=="function"&&j.constructor===Symbol&&j!==Symbol.prototype?"symbol":typeof j},a=function(){function j(M,z){var G=[],J=!0,X=!1,ee=void 0;try{for(var te=M[Symbol.iterator](),se;!(J=(se=te.next()).done)&&(G.push(se.value),!(z&&G.length===z));J=!0);}catch(le){X=!0,ee=le}finally{try{!J&&te.return&&te.return()}finally{if(X)throw ee}}return G}return function(M,z){if(Array.isArray(M))return M;if(Symbol.iterator in Object(M))return j(M,z);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),c=function(){function j(M,z){for(var G=0;G<z.length;G++){var J=z[G];J.enumerable=J.enumerable||!1,J.configurable=!0,"value"in J&&(J.writable=!0),Object.defineProperty(M,J.key,J)}}return function(M,z,G){return z&&j(M.prototype,z),G&&j(M,G),M}}(),f=n(21),y=A(f),d=n(11),u=A(d),o=n(3),s=A(o),_=n(2),m=A(_),g=n(20),v=A(g),p=n(0),b=A(p),h=n(5),w=A(h),S=n(10),E=A(S),T=n(9),R=A(T);function A(j){return j&&j.__esModule?j:{default:j}}function O(j,M,z){return M in j?Object.defineProperty(j,M,{value:z,enumerable:!0,configurable:!0,writable:!0}):j[M]=z,j}function k(j,M){if(!(j instanceof M))throw new TypeError("Cannot call a class as a function")}function x(j,M){if(!j)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return M&&(typeof M=="object"||typeof M=="function")?M:j}function q(j,M){if(typeof M!="function"&&M!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof M);j.prototype=Object.create(M&&M.prototype,{constructor:{value:j,enumerable:!1,writable:!0,configurable:!0}}),M&&(Object.setPrototypeOf?Object.setPrototypeOf(j,M):j.__proto__=M)}var I=(0,E.default)("quill:keyboard"),U=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",F=function(j){q(M,j),c(M,null,[{key:"match",value:function(G,J){return J=B(J),["altKey","ctrlKey","metaKey","shiftKey"].some(function(X){return!!J[X]!==G[X]&&J[X]!==null})?!1:J.key===(G.which||G.keyCode)}}]);function M(z,G){k(this,M);var J=x(this,(M.__proto__||Object.getPrototypeOf(M)).call(this,z,G));return J.bindings={},Object.keys(J.options.bindings).forEach(function(X){X==="list autofill"&&z.scroll.whitelist!=null&&!z.scroll.whitelist.list||J.options.bindings[X]&&J.addBinding(J.options.bindings[X])}),J.addBinding({key:M.keys.ENTER,shiftKey:null},N),J.addBinding({key:M.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(J.addBinding({key:M.keys.BACKSPACE},{collapsed:!0},D),J.addBinding({key:M.keys.DELETE},{collapsed:!0},L)):(J.addBinding({key:M.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},D),J.addBinding({key:M.keys.DELETE},{collapsed:!0,suffix:/^.?$/},L)),J.addBinding({key:M.keys.BACKSPACE},{collapsed:!1},P),J.addBinding({key:M.keys.DELETE},{collapsed:!1},P),J.addBinding({key:M.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},D),J.listen(),J}return c(M,[{key:"addBinding",value:function(G){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},ee=B(G);if(ee==null||ee.key==null)return I.warn("Attempted to add invalid keyboard binding",ee);typeof J=="function"&&(J={handler:J}),typeof X=="function"&&(X={handler:X}),ee=(0,s.default)(ee,J,X),this.bindings[ee.key]=this.bindings[ee.key]||[],this.bindings[ee.key].push(ee)}},{key:"listen",value:function(){var G=this;this.quill.root.addEventListener("keydown",function(J){if(!J.defaultPrevented){var X=J.which||J.keyCode,ee=(G.bindings[X]||[]).filter(function(fe){return M.match(J,fe)});if(ee.length!==0){var te=G.quill.getSelection();if(!(te==null||!G.quill.hasFocus())){var se=G.quill.getLine(te.index),le=a(se,2),ye=le[0],he=le[1],W=G.quill.getLeaf(te.index),V=a(W,2),Z=V[0],Q=V[1],Y=te.length===0?[Z,Q]:G.quill.getLeaf(te.index+te.length),ne=a(Y,2),ie=ne[0],ae=ne[1],me=Z instanceof b.default.Text?Z.value().slice(0,Q):"",we=ie instanceof b.default.Text?ie.value().slice(ae):"",ce={collapsed:te.length===0,empty:te.length===0&&ye.length()<=1,format:G.quill.getFormat(te),offset:he,prefix:me,suffix:we},Ao=ee.some(function(fe){if(fe.collapsed!=null&&fe.collapsed!==ce.collapsed||fe.empty!=null&&fe.empty!==ce.empty||fe.offset!=null&&fe.offset!==ce.offset)return!1;if(Array.isArray(fe.format)){if(fe.format.every(function(Te){return ce.format[Te]==null}))return!1}else if(i(fe.format)==="object"&&!Object.keys(fe.format).every(function(Te){return fe.format[Te]===!0?ce.format[Te]!=null:fe.format[Te]===!1?ce.format[Te]==null:(0,u.default)(fe.format[Te],ce.format[Te])}))return!1;return fe.prefix!=null&&!fe.prefix.test(ce.prefix)||fe.suffix!=null&&!fe.suffix.test(ce.suffix)?!1:fe.handler.call(G,te,ce)!==!0});Ao&&J.preventDefault()}}}})}}]),M}(R.default);F.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},F.DEFAULTS={bindings:{bold:$("bold"),italic:$("italic"),underline:$("underline"),indent:{key:F.keys.TAB,format:["blockquote","indent","list"],handler:function(M,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","+1",w.default.sources.USER)}},outdent:{key:F.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(M,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","-1",w.default.sources.USER)}},"outdent backspace":{key:F.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(M,z){z.format.indent!=null?this.quill.format("indent","-1",w.default.sources.USER):z.format.list!=null&&this.quill.format("list",!1,w.default.sources.USER)}},"indent code-block":C(!0),"outdent code-block":C(!1),"remove tab":{key:F.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(M){this.quill.deleteText(M.index-1,1,w.default.sources.USER)}},tab:{key:F.keys.TAB,handler:function(M){this.quill.history.cutoff();var z=new m.default().retain(M.index).delete(M.length).insert("	");this.quill.updateContents(z,w.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(M.index+1,w.default.sources.SILENT)}},"list empty enter":{key:F.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(M,z){this.quill.format("list",!1,w.default.sources.USER),z.format.indent&&this.quill.format("indent",!1,w.default.sources.USER)}},"checklist enter":{key:F.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(M){var z=this.quill.getLine(M.index),G=a(z,2),J=G[0],X=G[1],ee=(0,s.default)({},J.formats(),{list:"checked"}),te=new m.default().retain(M.index).insert(`
`,ee).retain(J.length()-X-1).retain(1,{list:"unchecked"});this.quill.updateContents(te,w.default.sources.USER),this.quill.setSelection(M.index+1,w.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:F.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(M,z){var G=this.quill.getLine(M.index),J=a(G,2),X=J[0],ee=J[1],te=new m.default().retain(M.index).insert(`
`,z.format).retain(X.length()-ee-1).retain(1,{header:null});this.quill.updateContents(te,w.default.sources.USER),this.quill.setSelection(M.index+1,w.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(M,z){var G=z.prefix.length,J=this.quill.getLine(M.index),X=a(J,2),ee=X[0],te=X[1];if(te>G)return!0;var se=void 0;switch(z.prefix.trim()){case"[]":case"[ ]":se="unchecked";break;case"[x]":se="checked";break;case"-":case"*":se="bullet";break;default:se="ordered"}this.quill.insertText(M.index," ",w.default.sources.USER),this.quill.history.cutoff();var le=new m.default().retain(M.index-te).delete(G+1).retain(ee.length()-2-te).retain(1,{list:se});this.quill.updateContents(le,w.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(M.index-G,w.default.sources.SILENT)}},"code exit":{key:F.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(M){var z=this.quill.getLine(M.index),G=a(z,2),J=G[0],X=G[1],ee=new m.default().retain(M.index+J.length()-X-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(ee,w.default.sources.USER)}},"embed left":H(F.keys.LEFT,!1),"embed left shift":H(F.keys.LEFT,!0),"embed right":H(F.keys.RIGHT,!1),"embed right shift":H(F.keys.RIGHT,!0)}};function H(j,M){var z,G=j===F.keys.LEFT?"prefix":"suffix";return z={key:j,shiftKey:M,altKey:null},O(z,G,/^$/),O(z,"handler",function(X){var ee=X.index;j===F.keys.RIGHT&&(ee+=X.length+1);var te=this.quill.getLeaf(ee),se=a(te,1),le=se[0];return le instanceof b.default.Embed?(j===F.keys.LEFT?M?this.quill.setSelection(X.index-1,X.length+1,w.default.sources.USER):this.quill.setSelection(X.index-1,w.default.sources.USER):M?this.quill.setSelection(X.index,X.length+1,w.default.sources.USER):this.quill.setSelection(X.index+X.length+1,w.default.sources.USER),!1):!0}),z}function D(j,M){if(!(j.index===0||this.quill.getLength()<=1)){var z=this.quill.getLine(j.index),G=a(z,1),J=G[0],X={};if(M.offset===0){var ee=this.quill.getLine(j.index-1),te=a(ee,1),se=te[0];if(se!=null&&se.length()>1){var le=J.formats(),ye=this.quill.getFormat(j.index-1,1);X=v.default.attributes.diff(le,ye)||{}}}var he=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(M.prefix)?2:1;this.quill.deleteText(j.index-he,he,w.default.sources.USER),Object.keys(X).length>0&&this.quill.formatLine(j.index-he,he,X,w.default.sources.USER),this.quill.focus()}}function L(j,M){var z=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(M.suffix)?2:1;if(!(j.index>=this.quill.getLength()-z)){var G={},J=0,X=this.quill.getLine(j.index),ee=a(X,1),te=ee[0];if(M.offset>=te.length()-1){var se=this.quill.getLine(j.index+1),le=a(se,1),ye=le[0];if(ye){var he=te.formats(),W=this.quill.getFormat(j.index,1);G=v.default.attributes.diff(he,W)||{},J=ye.length()}}this.quill.deleteText(j.index,z,w.default.sources.USER),Object.keys(G).length>0&&this.quill.formatLine(j.index+J-1,z,G,w.default.sources.USER)}}function P(j){var M=this.quill.getLines(j),z={};if(M.length>1){var G=M[0].formats(),J=M[M.length-1].formats();z=v.default.attributes.diff(J,G)||{}}this.quill.deleteText(j,w.default.sources.USER),Object.keys(z).length>0&&this.quill.formatLine(j.index,1,z,w.default.sources.USER),this.quill.setSelection(j.index,w.default.sources.SILENT),this.quill.focus()}function N(j,M){var z=this;j.length>0&&this.quill.scroll.deleteAt(j.index,j.length);var G=Object.keys(M.format).reduce(function(J,X){return b.default.query(X,b.default.Scope.BLOCK)&&!Array.isArray(M.format[X])&&(J[X]=M.format[X]),J},{});this.quill.insertText(j.index,`
`,G,w.default.sources.USER),this.quill.setSelection(j.index+1,w.default.sources.SILENT),this.quill.focus(),Object.keys(M.format).forEach(function(J){G[J]==null&&(Array.isArray(M.format[J])||J!=="link"&&z.quill.format(J,M.format[J],w.default.sources.USER))})}function C(j){return{key:F.keys.TAB,shiftKey:!j,format:{"code-block":!0},handler:function(z){var G=b.default.query("code-block"),J=z.index,X=z.length,ee=this.quill.scroll.descendant(G,J),te=a(ee,2),se=te[0],le=te[1];if(se!=null){var ye=this.quill.getIndex(se),he=se.newlineIndex(le,!0)+1,W=se.newlineIndex(ye+le+X),V=se.domNode.textContent.slice(he,W).split(`
`);le=0,V.forEach(function(Z,Q){j?(se.insertAt(he+le,G.TAB),le+=G.TAB.length,Q===0?J+=G.TAB.length:X+=G.TAB.length):Z.startsWith(G.TAB)&&(se.deleteAt(he+le,G.TAB.length),le-=G.TAB.length,Q===0?J-=G.TAB.length:X-=G.TAB.length),le+=Z.length+1}),this.quill.update(w.default.sources.USER),this.quill.setSelection(J,X,w.default.sources.SILENT)}}}}function $(j){return{key:j[0].toUpperCase(),shortKey:!0,handler:function(z,G){this.quill.format(j,!G.format[j],w.default.sources.USER)}}}function B(j){if(typeof j=="string"||typeof j=="number")return B({key:j});if((typeof j=="undefined"?"undefined":i(j))==="object"&&(j=(0,y.default)(j,!1)),typeof j.key=="string")if(F.keys[j.key.toUpperCase()]!=null)j.key=F.keys[j.key.toUpperCase()];else if(j.key.length===1)j.key=j.key.toUpperCase().charCodeAt(0);else return null;return j.shortKey&&(j[U]=j.shortKey,delete j.shortKey),j}t.default=F,t.SHORTKEY=U},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function v(p,b){var h=[],w=!0,S=!1,E=void 0;try{for(var T=p[Symbol.iterator](),R;!(w=(R=T.next()).done)&&(h.push(R.value),!(b&&h.length===b));w=!0);}catch(A){S=!0,E=A}finally{try{!w&&T.return&&T.return()}finally{if(S)throw E}}return h}return function(p,b){if(Array.isArray(p))return p;if(Symbol.iterator in Object(p))return v(p,b);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=function v(p,b,h){p===null&&(p=Function.prototype);var w=Object.getOwnPropertyDescriptor(p,b);if(w===void 0){var S=Object.getPrototypeOf(p);return S===null?void 0:v(S,b,h)}else{if("value"in w)return w.value;var E=w.get;return E===void 0?void 0:E.call(h)}},c=function(){function v(p,b){for(var h=0;h<b.length;h++){var w=b[h];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(p,w.key,w)}}return function(p,b,h){return b&&v(p.prototype,b),h&&v(p,h),p}}(),f=n(0),y=o(f),d=n(7),u=o(d);function o(v){return v&&v.__esModule?v:{default:v}}function s(v,p){if(!(v instanceof p))throw new TypeError("Cannot call a class as a function")}function _(v,p){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:v}function m(v,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);v.prototype=Object.create(p&&p.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(v,p):v.__proto__=p)}var g=function(v){m(p,v),c(p,null,[{key:"value",value:function(){}}]);function p(b,h){s(this,p);var w=_(this,(p.__proto__||Object.getPrototypeOf(p)).call(this,b));return w.selection=h,w.textNode=document.createTextNode(p.CONTENTS),w.domNode.appendChild(w.textNode),w._length=0,w}return c(p,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(h,w){if(this._length!==0)return a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"format",this).call(this,h,w);for(var S=this,E=0;S!=null&&S.statics.scope!==y.default.Scope.BLOCK_BLOT;)E+=S.offset(S.parent),S=S.parent;S!=null&&(this._length=p.CONTENTS.length,S.optimize(),S.formatAt(E,p.CONTENTS.length,h,w),this._length=0)}},{key:"index",value:function(h,w){return h===this.textNode?0:a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"index",this).call(this,h,w)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var h=this.textNode,w=this.selection.getNativeRange(),S=void 0,E=void 0,T=void 0;if(w!=null&&w.start.node===h&&w.end.node===h){var R=[h,w.start.offset,w.end.offset];S=R[0],E=R[1],T=R[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==p.CONTENTS){var A=this.textNode.data.split(p.CONTENTS).join("");this.next instanceof u.default?(S=this.next.domNode,this.next.insertAt(0,A),this.textNode.data=p.CONTENTS):(this.textNode.data=A,this.parent.insertBefore(y.default.create(this.textNode),this),this.textNode=document.createTextNode(p.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),E!=null){var O=[E,T].map(function(x){return Math.max(0,Math.min(S.data.length,x-1))}),k=i(O,2);return E=k[0],T=k[1],{startNode:S,startOffset:E,endNode:S,endOffset:T}}}}},{key:"update",value:function(h,w){var S=this;if(h.some(function(T){return T.type==="characterData"&&T.target===S.textNode})){var E=this.restore();E&&(w.range=E)}}},{key:"value",value:function(){return""}}]),p}(y.default.Embed);g.blotName="cursor",g.className="ql-cursor",g.tagName="span",g.CONTENTS="\uFEFF",t.default=g},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(0),a=y(i),c=n(4),f=y(c);function y(_){return _&&_.__esModule?_:{default:_}}function d(_,m){if(!(_ instanceof m))throw new TypeError("Cannot call a class as a function")}function u(_,m){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:_}function o(_,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);_.prototype=Object.create(m&&m.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(_,m):_.__proto__=m)}var s=function(_){o(m,_);function m(){return d(this,m),u(this,(m.__proto__||Object.getPrototypeOf(m)).apply(this,arguments))}return m}(a.default.Container);s.allowedChildren=[f.default,c.BlockEmbed,s],t.default=s},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.ColorStyle=t.ColorClass=t.ColorAttributor=void 0;var i=function(){function g(v,p){for(var b=0;b<p.length;b++){var h=p[b];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(v,h.key,h)}}return function(v,p,b){return p&&g(v.prototype,p),b&&g(v,b),v}}(),a=function g(v,p,b){v===null&&(v=Function.prototype);var h=Object.getOwnPropertyDescriptor(v,p);if(h===void 0){var w=Object.getPrototypeOf(v);return w===null?void 0:g(w,p,b)}else{if("value"in h)return h.value;var S=h.get;return S===void 0?void 0:S.call(b)}},c=n(0),f=y(c);function y(g){return g&&g.__esModule?g:{default:g}}function d(g,v){if(!(g instanceof v))throw new TypeError("Cannot call a class as a function")}function u(g,v){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:g}function o(g,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);g.prototype=Object.create(v&&v.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(g,v):g.__proto__=v)}var s=function(g){o(v,g);function v(){return d(this,v),u(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return i(v,[{key:"value",value:function(b){var h=a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"value",this).call(this,b);return h.startsWith("rgb(")?(h=h.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+h.split(",").map(function(w){return("00"+parseInt(w).toString(16)).slice(-2)}).join("")):h}}]),v}(f.default.Attributor.Style),_=new f.default.Attributor.Class("color","ql-color",{scope:f.default.Scope.INLINE}),m=new s("color","color",{scope:f.default.Scope.INLINE});t.ColorAttributor=s,t.ColorClass=_,t.ColorStyle=m},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.sanitize=t.default=void 0;var i=function(){function m(g,v){for(var p=0;p<v.length;p++){var b=v[p];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(g,b.key,b)}}return function(g,v,p){return v&&m(g.prototype,v),p&&m(g,p),g}}(),a=function m(g,v,p){g===null&&(g=Function.prototype);var b=Object.getOwnPropertyDescriptor(g,v);if(b===void 0){var h=Object.getPrototypeOf(g);return h===null?void 0:m(h,v,p)}else{if("value"in b)return b.value;var w=b.get;return w===void 0?void 0:w.call(p)}},c=n(6),f=y(c);function y(m){return m&&m.__esModule?m:{default:m}}function d(m,g){if(!(m instanceof g))throw new TypeError("Cannot call a class as a function")}function u(m,g){if(!m)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:m}function o(m,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);m.prototype=Object.create(g&&g.prototype,{constructor:{value:m,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(m,g):m.__proto__=g)}var s=function(m){o(g,m);function g(){return d(this,g),u(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return i(g,[{key:"format",value:function(p,b){if(p!==this.statics.blotName||!b)return a(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"format",this).call(this,p,b);b=this.constructor.sanitize(b),this.domNode.setAttribute("href",b)}}],[{key:"create",value:function(p){var b=a(g.__proto__||Object.getPrototypeOf(g),"create",this).call(this,p);return p=this.sanitize(p),b.setAttribute("href",p),b.setAttribute("rel","noopener noreferrer"),b.setAttribute("target","_blank"),b}},{key:"formats",value:function(p){return p.getAttribute("href")}},{key:"sanitize",value:function(p){return _(p,this.PROTOCOL_WHITELIST)?p:this.SANITIZED_URL}}]),g}(f.default);s.blotName="link",s.tagName="A",s.SANITIZED_URL="about:blank",s.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function _(m,g){var v=document.createElement("a");v.href=m;var p=v.href.slice(0,v.href.indexOf(":"));return g.indexOf(p)>-1}t.default=s,t.sanitize=_},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(g){return typeof g}:function(g){return g&&typeof Symbol=="function"&&g.constructor===Symbol&&g!==Symbol.prototype?"symbol":typeof g},a=function(){function g(v,p){for(var b=0;b<p.length;b++){var h=p[b];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(v,h.key,h)}}return function(v,p,b){return p&&g(v.prototype,p),b&&g(v,b),v}}(),c=n(23),f=u(c),y=n(107),d=u(y);function u(g){return g&&g.__esModule?g:{default:g}}function o(g,v){if(!(g instanceof v))throw new TypeError("Cannot call a class as a function")}var s=0;function _(g,v){g.setAttribute(v,g.getAttribute(v)!=="true")}var m=function(){function g(v){var p=this;o(this,g),this.select=v,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){p.togglePicker()}),this.label.addEventListener("keydown",function(b){switch(b.keyCode){case f.default.keys.ENTER:p.togglePicker();break;case f.default.keys.ESCAPE:p.escape(),b.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}return a(g,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),_(this.label,"aria-expanded"),_(this.options,"aria-hidden")}},{key:"buildItem",value:function(p){var b=this,h=document.createElement("span");return h.tabIndex="0",h.setAttribute("role","button"),h.classList.add("ql-picker-item"),p.hasAttribute("value")&&h.setAttribute("data-value",p.getAttribute("value")),p.textContent&&h.setAttribute("data-label",p.textContent),h.addEventListener("click",function(){b.selectItem(h,!0)}),h.addEventListener("keydown",function(w){switch(w.keyCode){case f.default.keys.ENTER:b.selectItem(h,!0),w.preventDefault();break;case f.default.keys.ESCAPE:b.escape(),w.preventDefault();break}}),h}},{key:"buildLabel",value:function(){var p=document.createElement("span");return p.classList.add("ql-picker-label"),p.innerHTML=d.default,p.tabIndex="0",p.setAttribute("role","button"),p.setAttribute("aria-expanded","false"),this.container.appendChild(p),p}},{key:"buildOptions",value:function(){var p=this,b=document.createElement("span");b.classList.add("ql-picker-options"),b.setAttribute("aria-hidden","true"),b.tabIndex="-1",b.id="ql-picker-options-"+s,s+=1,this.label.setAttribute("aria-controls",b.id),this.options=b,[].slice.call(this.select.options).forEach(function(h){var w=p.buildItem(h);b.appendChild(w),h.selected===!0&&p.selectItem(w)}),this.container.appendChild(b)}},{key:"buildPicker",value:function(){var p=this;[].slice.call(this.select.attributes).forEach(function(b){p.container.setAttribute(b.name,b.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var p=this;this.close(),setTimeout(function(){return p.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(p){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,h=this.container.querySelector(".ql-selected");if(p!==h&&(h!=null&&h.classList.remove("ql-selected"),p!=null&&(p.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(p.parentNode.children,p),p.hasAttribute("data-value")?this.label.setAttribute("data-value",p.getAttribute("data-value")):this.label.removeAttribute("data-value"),p.hasAttribute("data-label")?this.label.setAttribute("data-label",p.getAttribute("data-label")):this.label.removeAttribute("data-label"),b))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event=="undefined"?"undefined":i(Event))==="object"){var w=document.createEvent("Event");w.initEvent("change",!0,!0),this.select.dispatchEvent(w)}this.close()}}},{key:"update",value:function(){var p=void 0;if(this.select.selectedIndex>-1){var b=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];p=this.select.options[this.select.selectedIndex],this.selectItem(b)}else this.selectItem(null);var h=p!=null&&p!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",h)}}]),g}();t.default=m},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(0),a=I(i),c=n(5),f=I(c),y=n(4),d=I(y),u=n(16),o=I(u),s=n(25),_=I(s),m=n(24),g=I(m),v=n(35),p=I(v),b=n(6),h=I(b),w=n(22),S=I(w),E=n(7),T=I(E),R=n(55),A=I(R),O=n(42),k=I(O),x=n(23),q=I(x);function I(U){return U&&U.__esModule?U:{default:U}}f.default.register({"blots/block":d.default,"blots/block/embed":y.BlockEmbed,"blots/break":o.default,"blots/container":_.default,"blots/cursor":g.default,"blots/embed":p.default,"blots/inline":h.default,"blots/scroll":S.default,"blots/text":T.default,"modules/clipboard":A.default,"modules/history":k.default,"modules/keyboard":q.default}),a.default.register(d.default,o.default,g.default,h.default,S.default,T.default),t.default=f.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(1),a=function(){function c(f){this.domNode=f,this.domNode[i.DATA_KEY]={blot:this}}return Object.defineProperty(c.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),c.create=function(f){if(this.tagName==null)throw new i.ParchmentError("Blot definition missing tagName");var y;return Array.isArray(this.tagName)?(typeof f=="string"&&(f=f.toUpperCase(),parseInt(f).toString()===f&&(f=parseInt(f))),typeof f=="number"?y=document.createElement(this.tagName[f-1]):this.tagName.indexOf(f)>-1?y=document.createElement(f):y=document.createElement(this.tagName[0])):y=document.createElement(this.tagName),this.className&&y.classList.add(this.className),y},c.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},c.prototype.clone=function(){var f=this.domNode.cloneNode(!1);return i.create(f)},c.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[i.DATA_KEY]},c.prototype.deleteAt=function(f,y){var d=this.isolate(f,y);d.remove()},c.prototype.formatAt=function(f,y,d,u){var o=this.isolate(f,y);if(i.query(d,i.Scope.BLOT)!=null&&u)o.wrap(d,u);else if(i.query(d,i.Scope.ATTRIBUTE)!=null){var s=i.create(this.statics.scope);o.wrap(s),s.format(d,u)}},c.prototype.insertAt=function(f,y,d){var u=d==null?i.create("text",y):i.create(y,d),o=this.split(f);this.parent.insertBefore(u,o)},c.prototype.insertInto=function(f,y){y===void 0&&(y=null),this.parent!=null&&this.parent.children.remove(this);var d=null;f.children.insertBefore(this,y),y!=null&&(d=y.domNode),(this.domNode.parentNode!=f.domNode||this.domNode.nextSibling!=d)&&f.domNode.insertBefore(this.domNode,d),this.parent=f,this.attach()},c.prototype.isolate=function(f,y){var d=this.split(f);return d.split(y),d},c.prototype.length=function(){return 1},c.prototype.offset=function(f){return f===void 0&&(f=this.parent),this.parent==null||this==f?0:this.parent.children.offset(this)+this.parent.offset(f)},c.prototype.optimize=function(f){this.domNode[i.DATA_KEY]!=null&&delete this.domNode[i.DATA_KEY].mutations},c.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},c.prototype.replace=function(f){f.parent!=null&&(f.parent.insertBefore(this,f.next),f.remove())},c.prototype.replaceWith=function(f,y){var d=typeof f=="string"?i.create(f,y):f;return d.replace(this),d},c.prototype.split=function(f,y){return f===0?this:this.next},c.prototype.update=function(f,y){},c.prototype.wrap=function(f,y){var d=typeof f=="string"?i.create(f,y):f;return this.parent!=null&&this.parent.insertBefore(d,this.next),d.appendChild(this),d},c.blotName="abstract",c}();t.default=a},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(12),a=n(32),c=n(33),f=n(1),y=function(){function d(u){this.attributes={},this.domNode=u,this.build()}return d.prototype.attribute=function(u,o){o?u.add(this.domNode,o)&&(u.value(this.domNode)!=null?this.attributes[u.attrName]=u:delete this.attributes[u.attrName]):(u.remove(this.domNode),delete this.attributes[u.attrName])},d.prototype.build=function(){var u=this;this.attributes={};var o=i.default.keys(this.domNode),s=a.default.keys(this.domNode),_=c.default.keys(this.domNode);o.concat(s).concat(_).forEach(function(m){var g=f.query(m,f.Scope.ATTRIBUTE);g instanceof i.default&&(u.attributes[g.attrName]=g)})},d.prototype.copy=function(u){var o=this;Object.keys(this.attributes).forEach(function(s){var _=o.attributes[s].value(o.domNode);u.format(s,_)})},d.prototype.move=function(u){var o=this;this.copy(u),Object.keys(this.attributes).forEach(function(s){o.attributes[s].remove(o.domNode)}),this.attributes={}},d.prototype.values=function(){var u=this;return Object.keys(this.attributes).reduce(function(o,s){return o[s]=u.attributes[s].value(u.domNode),o},{})},d}();t.default=y},function(e,t,n){var i=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,u){d.__proto__=u}||function(d,u){for(var o in u)u.hasOwnProperty(o)&&(d[o]=u[o])};return function(d,u){y(d,u);function o(){this.constructor=d}d.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(12);function c(y,d){var u=y.getAttribute("class")||"";return u.split(/\s+/).filter(function(o){return o.indexOf(d+"-")===0})}var f=function(y){i(d,y);function d(){return y!==null&&y.apply(this,arguments)||this}return d.keys=function(u){return(u.getAttribute("class")||"").split(/\s+/).map(function(o){return o.split("-").slice(0,-1).join("-")})},d.prototype.add=function(u,o){return this.canAdd(u,o)?(this.remove(u),u.classList.add(this.keyName+"-"+o),!0):!1},d.prototype.remove=function(u){var o=c(u,this.keyName);o.forEach(function(s){u.classList.remove(s)}),u.classList.length===0&&u.removeAttribute("class")},d.prototype.value=function(u){var o=c(u,this.keyName)[0]||"",s=o.slice(this.keyName.length+1);return this.canAdd(u,s)?s:""},d}(a.default);t.default=f},function(e,t,n){var i=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,u){d.__proto__=u}||function(d,u){for(var o in u)u.hasOwnProperty(o)&&(d[o]=u[o])};return function(d,u){y(d,u);function o(){this.constructor=d}d.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(12);function c(y){var d=y.split("-"),u=d.slice(1).map(function(o){return o[0].toUpperCase()+o.slice(1)}).join("");return d[0]+u}var f=function(y){i(d,y);function d(){return y!==null&&y.apply(this,arguments)||this}return d.keys=function(u){return(u.getAttribute("style")||"").split(";").map(function(o){var s=o.split(":");return s[0].trim()})},d.prototype.add=function(u,o){return this.canAdd(u,o)?(u.style[c(this.keyName)]=o,!0):!1},d.prototype.remove=function(u){u.style[c(this.keyName)]="",u.getAttribute("style")||u.removeAttribute("style")},d.prototype.value=function(u){var o=u.style[c(this.keyName)];return this.canAdd(u,o)?o:""},d}(a.default);t.default=f},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function f(y,d){for(var u=0;u<d.length;u++){var o=d[u];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(y,o.key,o)}}return function(y,d,u){return d&&f(y.prototype,d),u&&f(y,u),y}}();function a(f,y){if(!(f instanceof y))throw new TypeError("Cannot call a class as a function")}var c=function(){function f(y,d){a(this,f),this.quill=y,this.options=d,this.modules={}}return i(f,[{key:"init",value:function(){var d=this;Object.keys(this.options.modules).forEach(function(u){d.modules[u]==null&&d.addModule(u)})}},{key:"addModule",value:function(d){var u=this.quill.constructor.import("modules/"+d);return this.modules[d]=new u(this.quill,this.options.modules[d]||{}),this.modules[d]}}]),f}();c.DEFAULTS={modules:{}},c.themes={default:c},t.default=c},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function v(p,b){for(var h=0;h<b.length;h++){var w=b[h];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(p,w.key,w)}}return function(p,b,h){return b&&v(p.prototype,b),h&&v(p,h),p}}(),a=function v(p,b,h){p===null&&(p=Function.prototype);var w=Object.getOwnPropertyDescriptor(p,b);if(w===void 0){var S=Object.getPrototypeOf(p);return S===null?void 0:v(S,b,h)}else{if("value"in w)return w.value;var E=w.get;return E===void 0?void 0:E.call(h)}},c=n(0),f=u(c),y=n(7),d=u(y);function u(v){return v&&v.__esModule?v:{default:v}}function o(v,p){if(!(v instanceof p))throw new TypeError("Cannot call a class as a function")}function s(v,p){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:v}function _(v,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);v.prototype=Object.create(p&&p.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(v,p):v.__proto__=p)}var m="\uFEFF",g=function(v){_(p,v);function p(b){o(this,p);var h=s(this,(p.__proto__||Object.getPrototypeOf(p)).call(this,b));return h.contentNode=document.createElement("span"),h.contentNode.setAttribute("contenteditable",!1),[].slice.call(h.domNode.childNodes).forEach(function(w){h.contentNode.appendChild(w)}),h.leftGuard=document.createTextNode(m),h.rightGuard=document.createTextNode(m),h.domNode.appendChild(h.leftGuard),h.domNode.appendChild(h.contentNode),h.domNode.appendChild(h.rightGuard),h}return i(p,[{key:"index",value:function(h,w){return h===this.leftGuard?0:h===this.rightGuard?1:a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"index",this).call(this,h,w)}},{key:"restore",value:function(h){var w=void 0,S=void 0,E=h.data.split(m).join("");if(h===this.leftGuard)if(this.prev instanceof d.default){var T=this.prev.length();this.prev.insertAt(T,E),w={startNode:this.prev.domNode,startOffset:T+E.length}}else S=document.createTextNode(E),this.parent.insertBefore(f.default.create(S),this),w={startNode:S,startOffset:E.length};else h===this.rightGuard&&(this.next instanceof d.default?(this.next.insertAt(0,E),w={startNode:this.next.domNode,startOffset:E.length}):(S=document.createTextNode(E),this.parent.insertBefore(f.default.create(S),this.next),w={startNode:S,startOffset:E.length}));return h.data=m,w}},{key:"update",value:function(h,w){var S=this;h.forEach(function(E){if(E.type==="characterData"&&(E.target===S.leftGuard||E.target===S.rightGuard)){var T=S.restore(E.target);T&&(w.range=T)}})}}]),p}(f.default.Embed);t.default=g},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.AlignStyle=t.AlignClass=t.AlignAttribute=void 0;var i=n(0),a=c(i);function c(o){return o&&o.__esModule?o:{default:o}}var f={scope:a.default.Scope.BLOCK,whitelist:["right","center","justify"]},y=new a.default.Attributor.Attribute("align","align",f),d=new a.default.Attributor.Class("align","ql-align",f),u=new a.default.Attributor.Style("align","text-align",f);t.AlignAttribute=y,t.AlignClass=d,t.AlignStyle=u},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.BackgroundStyle=t.BackgroundClass=void 0;var i=n(0),a=f(i),c=n(26);function f(u){return u&&u.__esModule?u:{default:u}}var y=new a.default.Attributor.Class("background","ql-bg",{scope:a.default.Scope.INLINE}),d=new c.ColorAttributor("background","background-color",{scope:a.default.Scope.INLINE});t.BackgroundClass=y,t.BackgroundStyle=d},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.DirectionStyle=t.DirectionClass=t.DirectionAttribute=void 0;var i=n(0),a=c(i);function c(o){return o&&o.__esModule?o:{default:o}}var f={scope:a.default.Scope.BLOCK,whitelist:["rtl"]},y=new a.default.Attributor.Attribute("direction","dir",f),d=new a.default.Attributor.Class("direction","ql-direction",f),u=new a.default.Attributor.Style("direction","direction",f);t.DirectionAttribute=y,t.DirectionClass=d,t.DirectionStyle=u},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.FontClass=t.FontStyle=void 0;var i=function(){function v(p,b){for(var h=0;h<b.length;h++){var w=b[h];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(p,w.key,w)}}return function(p,b,h){return b&&v(p.prototype,b),h&&v(p,h),p}}(),a=function v(p,b,h){p===null&&(p=Function.prototype);var w=Object.getOwnPropertyDescriptor(p,b);if(w===void 0){var S=Object.getPrototypeOf(p);return S===null?void 0:v(S,b,h)}else{if("value"in w)return w.value;var E=w.get;return E===void 0?void 0:E.call(h)}},c=n(0),f=y(c);function y(v){return v&&v.__esModule?v:{default:v}}function d(v,p){if(!(v instanceof p))throw new TypeError("Cannot call a class as a function")}function u(v,p){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return p&&(typeof p=="object"||typeof p=="function")?p:v}function o(v,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof p);v.prototype=Object.create(p&&p.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),p&&(Object.setPrototypeOf?Object.setPrototypeOf(v,p):v.__proto__=p)}var s={scope:f.default.Scope.INLINE,whitelist:["serif","monospace"]},_=new f.default.Attributor.Class("font","ql-font",s),m=function(v){o(p,v);function p(){return d(this,p),u(this,(p.__proto__||Object.getPrototypeOf(p)).apply(this,arguments))}return i(p,[{key:"value",value:function(h){return a(p.prototype.__proto__||Object.getPrototypeOf(p.prototype),"value",this).call(this,h).replace(/["']/g,"")}}]),p}(f.default.Attributor.Style),g=new m("font","font-family",s);t.FontStyle=g,t.FontClass=_},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.SizeStyle=t.SizeClass=void 0;var i=n(0),a=c(i);function c(d){return d&&d.__esModule?d:{default:d}}var f=new a.default.Attributor.Class("size","ql-size",{scope:a.default.Scope.INLINE,whitelist:["small","large","huge"]}),y=new a.default.Attributor.Style("size","font-size",{scope:a.default.Scope.INLINE,whitelist:["10px","18px","32px"]});t.SizeClass=f,t.SizeStyle=y},function(e,t,n){e.exports={align:{"":n(76),center:n(77),right:n(78),justify:n(79)},background:n(80),blockquote:n(81),bold:n(82),clean:n(83),code:n(58),"code-block":n(58),color:n(84),direction:{"":n(85),rtl:n(86)},float:{center:n(87),full:n(88),left:n(89),right:n(90)},formula:n(91),header:{1:n(92),2:n(93)},italic:n(94),image:n(95),indent:{"+1":n(96),"-1":n(97)},link:n(98),list:{ordered:n(99),bullet:n(100),check:n(101)},script:{sub:n(102),super:n(103)},strike:n(104),underline:n(105),video:n(106)}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getLastChangeIndex=t.default=void 0;var i=function(){function b(h,w){for(var S=0;S<w.length;S++){var E=w[S];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(h,E.key,E)}}return function(h,w,S){return w&&b(h.prototype,w),S&&b(h,S),h}}(),a=n(0),c=o(a),f=n(5),y=o(f),d=n(9),u=o(d);function o(b){return b&&b.__esModule?b:{default:b}}function s(b,h){if(!(b instanceof h))throw new TypeError("Cannot call a class as a function")}function _(b,h){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:b}function m(b,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);b.prototype=Object.create(h&&h.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(b,h):b.__proto__=h)}var g=function(b){m(h,b);function h(w,S){s(this,h);var E=_(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,w,S));return E.lastRecorded=0,E.ignoreChange=!1,E.clear(),E.quill.on(y.default.events.EDITOR_CHANGE,function(T,R,A,O){T!==y.default.events.TEXT_CHANGE||E.ignoreChange||(!E.options.userOnly||O===y.default.sources.USER?E.record(R,A):E.transform(R))}),E.quill.keyboard.addBinding({key:"Z",shortKey:!0},E.undo.bind(E)),E.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},E.redo.bind(E)),/Win/i.test(navigator.platform)&&E.quill.keyboard.addBinding({key:"Y",shortKey:!0},E.redo.bind(E)),E}return i(h,[{key:"change",value:function(S,E){if(this.stack[S].length!==0){var T=this.stack[S].pop();this.stack[E].push(T),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(T[S],y.default.sources.USER),this.ignoreChange=!1;var R=p(T[S]);this.quill.setSelection(R)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(S,E){if(S.ops.length!==0){this.stack.redo=[];var T=this.quill.getContents().diff(E),R=Date.now();if(this.lastRecorded+this.options.delay>R&&this.stack.undo.length>0){var A=this.stack.undo.pop();T=T.compose(A.undo),S=A.redo.compose(S)}else this.lastRecorded=R;this.stack.undo.push({redo:S,undo:T}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(S){this.stack.undo.forEach(function(E){E.undo=S.transform(E.undo,!0),E.redo=S.transform(E.redo,!0)}),this.stack.redo.forEach(function(E){E.undo=S.transform(E.undo,!0),E.redo=S.transform(E.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),h}(u.default);g.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function v(b){var h=b.ops[b.ops.length-1];return h==null?!1:h.insert!=null?typeof h.insert=="string"&&h.insert.endsWith(`
`):h.attributes!=null?Object.keys(h.attributes).some(function(w){return c.default.query(w,c.default.Scope.BLOCK)!=null}):!1}function p(b){var h=b.reduce(function(S,E){return S+=E.delete||0,S},0),w=b.length()-h;return v(b)&&(w-=1),w}t.default=g,t.getLastChangeIndex=p},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.BaseTooltip=void 0;var i=function(){function N(C,$){for(var B=0;B<$.length;B++){var j=$[B];j.enumerable=j.enumerable||!1,j.configurable=!0,"value"in j&&(j.writable=!0),Object.defineProperty(C,j.key,j)}}return function(C,$,B){return $&&N(C.prototype,$),B&&N(C,B),C}}(),a=function N(C,$,B){C===null&&(C=Function.prototype);var j=Object.getOwnPropertyDescriptor(C,$);if(j===void 0){var M=Object.getPrototypeOf(C);return M===null?void 0:N(M,$,B)}else{if("value"in j)return j.value;var z=j.get;return z===void 0?void 0:z.call(B)}},c=n(3),f=R(c),y=n(2),d=R(y),u=n(8),o=R(u),s=n(23),_=R(s),m=n(34),g=R(m),v=n(59),p=R(v),b=n(60),h=R(b),w=n(28),S=R(w),E=n(61),T=R(E);function R(N){return N&&N.__esModule?N:{default:N}}function A(N,C){if(!(N instanceof C))throw new TypeError("Cannot call a class as a function")}function O(N,C){if(!N)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return C&&(typeof C=="object"||typeof C=="function")?C:N}function k(N,C){if(typeof C!="function"&&C!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof C);N.prototype=Object.create(C&&C.prototype,{constructor:{value:N,enumerable:!1,writable:!0,configurable:!0}}),C&&(Object.setPrototypeOf?Object.setPrototypeOf(N,C):N.__proto__=C)}var x=[!1,"center","right","justify"],q=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],I=[!1,"serif","monospace"],U=["1","2","3",!1],F=["small",!1,"large","huge"],H=function(N){k(C,N);function C($,B){A(this,C);var j=O(this,(C.__proto__||Object.getPrototypeOf(C)).call(this,$,B)),M=function z(G){if(!document.body.contains($.root))return document.body.removeEventListener("click",z);j.tooltip!=null&&!j.tooltip.root.contains(G.target)&&document.activeElement!==j.tooltip.textbox&&!j.quill.hasFocus()&&j.tooltip.hide(),j.pickers!=null&&j.pickers.forEach(function(J){J.container.contains(G.target)||J.close()})};return $.emitter.listenDOM("click",document.body,M),j}return i(C,[{key:"addModule",value:function(B){var j=a(C.prototype.__proto__||Object.getPrototypeOf(C.prototype),"addModule",this).call(this,B);return B==="toolbar"&&this.extendToolbar(j),j}},{key:"buildButtons",value:function(B,j){B.forEach(function(M){var z=M.getAttribute("class")||"";z.split(/\s+/).forEach(function(G){if(G.startsWith("ql-")&&(G=G.slice(3),j[G]!=null))if(G==="direction")M.innerHTML=j[G][""]+j[G].rtl;else if(typeof j[G]=="string")M.innerHTML=j[G];else{var J=M.value||"";J!=null&&j[G][J]&&(M.innerHTML=j[G][J])}})})}},{key:"buildPickers",value:function(B,j){var M=this;this.pickers=B.map(function(G){if(G.classList.contains("ql-align"))return G.querySelector("option")==null&&P(G,x),new h.default(G,j.align);if(G.classList.contains("ql-background")||G.classList.contains("ql-color")){var J=G.classList.contains("ql-background")?"background":"color";return G.querySelector("option")==null&&P(G,q,J==="background"?"#ffffff":"#000000"),new p.default(G,j[J])}else return G.querySelector("option")==null&&(G.classList.contains("ql-font")?P(G,I):G.classList.contains("ql-header")?P(G,U):G.classList.contains("ql-size")&&P(G,F)),new S.default(G)});var z=function(){M.pickers.forEach(function(J){J.update()})};this.quill.on(o.default.events.EDITOR_CHANGE,z)}}]),C}(g.default);H.DEFAULTS=(0,f.default)(!0,{},g.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var C=this,$=this.container.querySelector("input.ql-image[type=file]");$==null&&($=document.createElement("input"),$.setAttribute("type","file"),$.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),$.classList.add("ql-image"),$.addEventListener("change",function(){if($.files!=null&&$.files[0]!=null){var B=new FileReader;B.onload=function(j){var M=C.quill.getSelection(!0);C.quill.updateContents(new d.default().retain(M.index).delete(M.length).insert({image:j.target.result}),o.default.sources.USER),C.quill.setSelection(M.index+1,o.default.sources.SILENT),$.value=""},B.readAsDataURL($.files[0])}}),this.container.appendChild($)),$.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var D=function(N){k(C,N);function C($,B){A(this,C);var j=O(this,(C.__proto__||Object.getPrototypeOf(C)).call(this,$,B));return j.textbox=j.root.querySelector('input[type="text"]'),j.listen(),j}return i(C,[{key:"listen",value:function(){var B=this;this.textbox.addEventListener("keydown",function(j){_.default.match(j,"enter")?(B.save(),j.preventDefault()):_.default.match(j,"escape")&&(B.cancel(),j.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),j!=null?this.textbox.value=j:B!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+B)||""),this.root.setAttribute("data-mode",B)}},{key:"restoreFocus",value:function(){var B=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=B}},{key:"save",value:function(){var B=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var j=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",B,o.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",B,o.default.sources.USER)),this.quill.root.scrollTop=j;break}case"video":B=L(B);case"formula":{if(!B)break;var M=this.quill.getSelection(!0);if(M!=null){var z=M.index+M.length;this.quill.insertEmbed(z,this.root.getAttribute("data-mode"),B,o.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(z+1," ",o.default.sources.USER),this.quill.setSelection(z+2,o.default.sources.USER)}break}}this.textbox.value="",this.hide()}}]),C}(T.default);function L(N){var C=N.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||N.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return C?(C[1]||"https")+"://www.youtube.com/embed/"+C[2]+"?showinfo=0":(C=N.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(C[1]||"https")+"://player.vimeo.com/video/"+C[2]+"/":N}function P(N,C){var $=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;C.forEach(function(B){var j=document.createElement("option");B===$?j.setAttribute("selected","selected"):j.setAttribute("value",B),N.appendChild(j)})}t.BaseTooltip=D,t.default=H},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function a(){this.head=this.tail=null,this.length=0}return a.prototype.append=function(){for(var c=[],f=0;f<arguments.length;f++)c[f]=arguments[f];this.insertBefore(c[0],null),c.length>1&&this.append.apply(this,c.slice(1))},a.prototype.contains=function(c){for(var f,y=this.iterator();f=y();)if(f===c)return!0;return!1},a.prototype.insertBefore=function(c,f){c&&(c.next=f,f!=null?(c.prev=f.prev,f.prev!=null&&(f.prev.next=c),f.prev=c,f===this.head&&(this.head=c)):this.tail!=null?(this.tail.next=c,c.prev=this.tail,this.tail=c):(c.prev=null,this.head=this.tail=c),this.length+=1)},a.prototype.offset=function(c){for(var f=0,y=this.head;y!=null;){if(y===c)return f;f+=y.length(),y=y.next}return-1},a.prototype.remove=function(c){this.contains(c)&&(c.prev!=null&&(c.prev.next=c.next),c.next!=null&&(c.next.prev=c.prev),c===this.head&&(this.head=c.next),c===this.tail&&(this.tail=c.prev),this.length-=1)},a.prototype.iterator=function(c){return c===void 0&&(c=this.head),function(){var f=c;return c!=null&&(c=c.next),f}},a.prototype.find=function(c,f){f===void 0&&(f=!1);for(var y,d=this.iterator();y=d();){var u=y.length();if(c<u||f&&c===u&&(y.next==null||y.next.length()!==0))return[y,c];c-=u}return[null,0]},a.prototype.forEach=function(c){for(var f,y=this.iterator();f=y();)c(f)},a.prototype.forEachAt=function(c,f,y){if(!(f<=0))for(var d=this.find(c),u=d[0],o=d[1],s,_=c-o,m=this.iterator(u);(s=m())&&_<c+f;){var g=s.length();c>_?y(s,c-_,Math.min(f,_+g-c)):y(s,0,Math.min(g,c+f-_)),_+=g}},a.prototype.map=function(c){return this.reduce(function(f,y){return f.push(c(y)),f},[])},a.prototype.reduce=function(c,f){for(var y,d=this.iterator();y=d();)f=c(f,y);return f},a}();t.default=i},function(e,t,n){var i=this&&this.__extends||function(){var u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,s){o.__proto__=s}||function(o,s){for(var _ in s)s.hasOwnProperty(_)&&(o[_]=s[_])};return function(o,s){u(o,s);function _(){this.constructor=o}o.prototype=s===null?Object.create(s):(_.prototype=s.prototype,new _)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(17),c=n(1),f={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},y=100,d=function(u){i(o,u);function o(s){var _=u.call(this,s)||this;return _.scroll=_,_.observer=new MutationObserver(function(m){_.update(m)}),_.observer.observe(_.domNode,f),_.attach(),_}return o.prototype.detach=function(){u.prototype.detach.call(this),this.observer.disconnect()},o.prototype.deleteAt=function(s,_){this.update(),s===0&&_===this.length()?this.children.forEach(function(m){m.remove()}):u.prototype.deleteAt.call(this,s,_)},o.prototype.formatAt=function(s,_,m,g){this.update(),u.prototype.formatAt.call(this,s,_,m,g)},o.prototype.insertAt=function(s,_,m){this.update(),u.prototype.insertAt.call(this,s,_,m)},o.prototype.optimize=function(s,_){var m=this;s===void 0&&(s=[]),_===void 0&&(_={}),u.prototype.optimize.call(this,_);for(var g=[].slice.call(this.observer.takeRecords());g.length>0;)s.push(g.pop());for(var v=function(w,S){S===void 0&&(S=!0),!(w==null||w===m)&&w.domNode.parentNode!=null&&(w.domNode[c.DATA_KEY].mutations==null&&(w.domNode[c.DATA_KEY].mutations=[]),S&&v(w.parent))},p=function(w){w.domNode[c.DATA_KEY]==null||w.domNode[c.DATA_KEY].mutations==null||(w instanceof a.default&&w.children.forEach(p),w.optimize(_))},b=s,h=0;b.length>0;h+=1){if(h>=y)throw new Error("[Parchment] Maximum optimize iterations reached");for(b.forEach(function(w){var S=c.find(w.target,!0);S!=null&&(S.domNode===w.target&&(w.type==="childList"?(v(c.find(w.previousSibling,!1)),[].forEach.call(w.addedNodes,function(E){var T=c.find(E,!1);v(T,!1),T instanceof a.default&&T.children.forEach(function(R){v(R,!1)})})):w.type==="attributes"&&v(S.prev)),v(S))}),this.children.forEach(p),b=[].slice.call(this.observer.takeRecords()),g=b.slice();g.length>0;)s.push(g.pop())}},o.prototype.update=function(s,_){var m=this;_===void 0&&(_={}),s=s||this.observer.takeRecords(),s.map(function(g){var v=c.find(g.target,!0);return v==null?null:v.domNode[c.DATA_KEY].mutations==null?(v.domNode[c.DATA_KEY].mutations=[g],v):(v.domNode[c.DATA_KEY].mutations.push(g),null)}).forEach(function(g){g==null||g===m||g.domNode[c.DATA_KEY]==null||g.update(g.domNode[c.DATA_KEY].mutations||[],_)}),this.domNode[c.DATA_KEY].mutations!=null&&u.prototype.update.call(this,this.domNode[c.DATA_KEY].mutations,_),this.optimize(s,_)},o.blotName="scroll",o.defaultChild="block",o.scope=c.Scope.BLOCK_BLOT,o.tagName="DIV",o}(a.default);t.default=d},function(e,t,n){var i=this&&this.__extends||function(){var d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(u,o){u.__proto__=o}||function(u,o){for(var s in o)o.hasOwnProperty(s)&&(u[s]=o[s])};return function(u,o){d(u,o);function s(){this.constructor=u}u.prototype=o===null?Object.create(o):(s.prototype=o.prototype,new s)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(18),c=n(1);function f(d,u){if(Object.keys(d).length!==Object.keys(u).length)return!1;for(var o in d)if(d[o]!==u[o])return!1;return!0}var y=function(d){i(u,d);function u(){return d!==null&&d.apply(this,arguments)||this}return u.formats=function(o){if(o.tagName!==u.tagName)return d.formats.call(this,o)},u.prototype.format=function(o,s){var _=this;o===this.statics.blotName&&!s?(this.children.forEach(function(m){m instanceof a.default||(m=m.wrap(u.blotName,!0)),_.attributes.copy(m)}),this.unwrap()):d.prototype.format.call(this,o,s)},u.prototype.formatAt=function(o,s,_,m){if(this.formats()[_]!=null||c.query(_,c.Scope.ATTRIBUTE)){var g=this.isolate(o,s);g.format(_,m)}else d.prototype.formatAt.call(this,o,s,_,m)},u.prototype.optimize=function(o){d.prototype.optimize.call(this,o);var s=this.formats();if(Object.keys(s).length===0)return this.unwrap();var _=this.next;_ instanceof u&&_.prev===this&&f(s,_.formats())&&(_.moveChildren(this),_.remove())},u.blotName="inline",u.scope=c.Scope.INLINE_BLOT,u.tagName="SPAN",u}(a.default);t.default=y},function(e,t,n){var i=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,u){d.__proto__=u}||function(d,u){for(var o in u)u.hasOwnProperty(o)&&(d[o]=u[o])};return function(d,u){y(d,u);function o(){this.constructor=d}d.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(18),c=n(1),f=function(y){i(d,y);function d(){return y!==null&&y.apply(this,arguments)||this}return d.formats=function(u){var o=c.query(d.blotName).tagName;if(u.tagName!==o)return y.formats.call(this,u)},d.prototype.format=function(u,o){c.query(u,c.Scope.BLOCK)!=null&&(u===this.statics.blotName&&!o?this.replaceWith(d.blotName):y.prototype.format.call(this,u,o))},d.prototype.formatAt=function(u,o,s,_){c.query(s,c.Scope.BLOCK)!=null?this.format(s,_):y.prototype.formatAt.call(this,u,o,s,_)},d.prototype.insertAt=function(u,o,s){if(s==null||c.query(o,c.Scope.INLINE)!=null)y.prototype.insertAt.call(this,u,o,s);else{var _=this.split(u),m=c.create(o,s);_.parent.insertBefore(m,_)}},d.prototype.update=function(u,o){navigator.userAgent.match(/Trident/)?this.build():y.prototype.update.call(this,u,o)},d.blotName="block",d.scope=c.Scope.BLOCK_BLOT,d.tagName="P",d}(a.default);t.default=f},function(e,t,n){var i=this&&this.__extends||function(){var f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var u in d)d.hasOwnProperty(u)&&(y[u]=d[u])};return function(y,d){f(y,d);function u(){this.constructor=y}y.prototype=d===null?Object.create(d):(u.prototype=d.prototype,new u)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(19),c=function(f){i(y,f);function y(){return f!==null&&f.apply(this,arguments)||this}return y.formats=function(d){},y.prototype.format=function(d,u){f.prototype.formatAt.call(this,0,this.length(),d,u)},y.prototype.formatAt=function(d,u,o,s){d===0&&u===this.length()?this.format(o,s):f.prototype.formatAt.call(this,d,u,o,s)},y.prototype.formats=function(){return this.statics.formats(this.domNode)},y}(a.default);t.default=c},function(e,t,n){var i=this&&this.__extends||function(){var y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,u){d.__proto__=u}||function(d,u){for(var o in u)u.hasOwnProperty(o)&&(d[o]=u[o])};return function(d,u){y(d,u);function o(){this.constructor=d}d.prototype=u===null?Object.create(u):(o.prototype=u.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var a=n(19),c=n(1),f=function(y){i(d,y);function d(u){var o=y.call(this,u)||this;return o.text=o.statics.value(o.domNode),o}return d.create=function(u){return document.createTextNode(u)},d.value=function(u){var o=u.data;return o.normalize&&(o=o.normalize()),o},d.prototype.deleteAt=function(u,o){this.domNode.data=this.text=this.text.slice(0,u)+this.text.slice(u+o)},d.prototype.index=function(u,o){return this.domNode===u?o:-1},d.prototype.insertAt=function(u,o,s){s==null?(this.text=this.text.slice(0,u)+o+this.text.slice(u),this.domNode.data=this.text):y.prototype.insertAt.call(this,u,o,s)},d.prototype.length=function(){return this.text.length},d.prototype.optimize=function(u){y.prototype.optimize.call(this,u),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof d&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},d.prototype.position=function(u,o){return[this.domNode,u]},d.prototype.split=function(u,o){if(o===void 0&&(o=!1),!o){if(u===0)return this;if(u===this.length())return this.next}var s=c.create(this.domNode.splitText(u));return this.parent.insertBefore(s,this.next),this.text=this.statics.value(this.domNode),s},d.prototype.update=function(u,o){var s=this;u.some(function(_){return _.type==="characterData"&&_.target===s.domNode})&&(this.text=this.statics.value(this.domNode))},d.prototype.value=function(){return this.text},d.blotName="text",d.scope=c.Scope.INLINE_BLOT,d}(a.default);t.default=f},function(e,t,n){var i=document.createElement("div");if(i.classList.toggle("test-class",!1),i.classList.contains("test-class")){var a=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(c,f){return arguments.length>1&&!this.contains(c)==!f?f:a.call(this,c)}}String.prototype.startsWith||(String.prototype.startsWith=function(c,f){return f=f||0,this.substr(f,c.length)===c}),String.prototype.endsWith||(String.prototype.endsWith=function(c,f){var y=this.toString();(typeof f!="number"||!isFinite(f)||Math.floor(f)!==f||f>y.length)&&(f=y.length),f-=c.length;var d=y.indexOf(c,f);return d!==-1&&d===f}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(f){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof f!="function")throw new TypeError("predicate must be a function");for(var y=Object(this),d=y.length>>>0,u=arguments[1],o,s=0;s<d;s++)if(o=y[s],f.call(u,o,s,y))return o}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(e,t){var n=-1,i=1,a=0;function c(h,w,S){if(h==w)return h?[[a,h]]:[];(S<0||h.length<S)&&(S=null);var E=u(h,w),T=h.substring(0,E);h=h.substring(E),w=w.substring(E),E=o(h,w);var R=h.substring(h.length-E);h=h.substring(0,h.length-E),w=w.substring(0,w.length-E);var A=f(h,w);return T&&A.unshift([a,T]),R&&A.push([a,R]),_(A),S!=null&&(A=v(A,S)),A=p(A),A}function f(h,w){var S;if(!h)return[[i,w]];if(!w)return[[n,h]];var E=h.length>w.length?h:w,T=h.length>w.length?w:h,R=E.indexOf(T);if(R!=-1)return S=[[i,E.substring(0,R)],[a,T],[i,E.substring(R+T.length)]],h.length>w.length&&(S[0][0]=S[2][0]=n),S;if(T.length==1)return[[n,h],[i,w]];var A=s(h,w);if(A){var O=A[0],k=A[1],x=A[2],q=A[3],I=A[4],U=c(O,x),F=c(k,q);return U.concat([[a,I]],F)}return y(h,w)}function y(h,w){for(var S=h.length,E=w.length,T=Math.ceil((S+E)/2),R=T,A=2*T,O=new Array(A),k=new Array(A),x=0;x<A;x++)O[x]=-1,k[x]=-1;O[R+1]=0,k[R+1]=0;for(var q=S-E,I=q%2!=0,U=0,F=0,H=0,D=0,L=0;L<T;L++){for(var P=-L+U;P<=L-F;P+=2){var N=R+P,C;P==-L||P!=L&&O[N-1]<O[N+1]?C=O[N+1]:C=O[N-1]+1;for(var $=C-P;C<S&&$<E&&h.charAt(C)==w.charAt($);)C++,$++;if(O[N]=C,C>S)F+=2;else if($>E)U+=2;else if(I){var B=R+q-P;if(B>=0&&B<A&&k[B]!=-1){var j=S-k[B];if(C>=j)return d(h,w,C,$)}}}for(var M=-L+H;M<=L-D;M+=2){var B=R+M,j;M==-L||M!=L&&k[B-1]<k[B+1]?j=k[B+1]:j=k[B-1]+1;for(var z=j-M;j<S&&z<E&&h.charAt(S-j-1)==w.charAt(E-z-1);)j++,z++;if(k[B]=j,j>S)D+=2;else if(z>E)H+=2;else if(!I){var N=R+q-M;if(N>=0&&N<A&&O[N]!=-1){var C=O[N],$=R+C-N;if(j=S-j,C>=j)return d(h,w,C,$)}}}}return[[n,h],[i,w]]}function d(h,w,S,E){var T=h.substring(0,S),R=w.substring(0,E),A=h.substring(S),O=w.substring(E),k=c(T,R),x=c(A,O);return k.concat(x)}function u(h,w){if(!h||!w||h.charAt(0)!=w.charAt(0))return 0;for(var S=0,E=Math.min(h.length,w.length),T=E,R=0;S<T;)h.substring(R,T)==w.substring(R,T)?(S=T,R=S):E=T,T=Math.floor((E-S)/2+S);return T}function o(h,w){if(!h||!w||h.charAt(h.length-1)!=w.charAt(w.length-1))return 0;for(var S=0,E=Math.min(h.length,w.length),T=E,R=0;S<T;)h.substring(h.length-T,h.length-R)==w.substring(w.length-T,w.length-R)?(S=T,R=S):E=T,T=Math.floor((E-S)/2+S);return T}function s(h,w){var S=h.length>w.length?h:w,E=h.length>w.length?w:h;if(S.length<4||E.length*2<S.length)return null;function T(F,H,D){for(var L=F.substring(D,D+Math.floor(F.length/4)),P=-1,N="",C,$,B,j;(P=H.indexOf(L,P+1))!=-1;){var M=u(F.substring(D),H.substring(P)),z=o(F.substring(0,D),H.substring(0,P));N.length<z+M&&(N=H.substring(P-z,P)+H.substring(P,P+M),C=F.substring(0,D-z),$=F.substring(D+M),B=H.substring(0,P-z),j=H.substring(P+M))}return N.length*2>=F.length?[C,$,B,j,N]:null}var R=T(S,E,Math.ceil(S.length/4)),A=T(S,E,Math.ceil(S.length/2)),O;if(!R&&!A)return null;A?R?O=R[4].length>A[4].length?R:A:O=A:O=R;var k,x,q,I;h.length>w.length?(k=O[0],x=O[1],q=O[2],I=O[3]):(q=O[0],I=O[1],k=O[2],x=O[3]);var U=O[4];return[k,x,q,I,U]}function _(h){h.push([a,""]);for(var w=0,S=0,E=0,T="",R="",A;w<h.length;)switch(h[w][0]){case i:E++,R+=h[w][1],w++;break;case n:S++,T+=h[w][1],w++;break;case a:S+E>1?(S!==0&&E!==0&&(A=u(R,T),A!==0&&(w-S-E>0&&h[w-S-E-1][0]==a?h[w-S-E-1][1]+=R.substring(0,A):(h.splice(0,0,[a,R.substring(0,A)]),w++),R=R.substring(A),T=T.substring(A)),A=o(R,T),A!==0&&(h[w][1]=R.substring(R.length-A)+h[w][1],R=R.substring(0,R.length-A),T=T.substring(0,T.length-A))),S===0?h.splice(w-E,S+E,[i,R]):E===0?h.splice(w-S,S+E,[n,T]):h.splice(w-S-E,S+E,[n,T],[i,R]),w=w-S-E+(S?1:0)+(E?1:0)+1):w!==0&&h[w-1][0]==a?(h[w-1][1]+=h[w][1],h.splice(w,1)):w++,E=0,S=0,T="",R="";break}h[h.length-1][1]===""&&h.pop();var O=!1;for(w=1;w<h.length-1;)h[w-1][0]==a&&h[w+1][0]==a&&(h[w][1].substring(h[w][1].length-h[w-1][1].length)==h[w-1][1]?(h[w][1]=h[w-1][1]+h[w][1].substring(0,h[w][1].length-h[w-1][1].length),h[w+1][1]=h[w-1][1]+h[w+1][1],h.splice(w-1,1),O=!0):h[w][1].substring(0,h[w+1][1].length)==h[w+1][1]&&(h[w-1][1]+=h[w+1][1],h[w][1]=h[w][1].substring(h[w+1][1].length)+h[w+1][1],h.splice(w+1,1),O=!0)),w++;O&&_(h)}var m=c;m.INSERT=i,m.DELETE=n,m.EQUAL=a,e.exports=m;function g(h,w){if(w===0)return[a,h];for(var S=0,E=0;E<h.length;E++){var T=h[E];if(T[0]===n||T[0]===a){var R=S+T[1].length;if(w===R)return[E+1,h];if(w<R){h=h.slice();var A=w-S,O=[T[0],T[1].slice(0,A)],k=[T[0],T[1].slice(A)];return h.splice(E,1,O,k),[E+1,h]}else S=R}}throw new Error("cursor_pos is out of bounds!")}function v(h,w){var S=g(h,w),E=S[1],T=S[0],R=E[T],A=E[T+1];if(R==null)return h;if(R[0]!==a)return h;if(A!=null&&R[1]+A[1]===A[1]+R[1])return E.splice(T,2,A,R),b(E,T,2);if(A!=null&&A[1].indexOf(R[1])===0){E.splice(T,2,[A[0],R[1]],[0,R[1]]);var O=A[1].slice(R[1].length);return O.length>0&&E.splice(T+2,0,[A[0],O]),b(E,T,3)}else return h}function p(h){for(var w=!1,S=function(A){return A.charCodeAt(0)>=56320&&A.charCodeAt(0)<=57343},E=function(A){return A.charCodeAt(A.length-1)>=55296&&A.charCodeAt(A.length-1)<=56319},T=2;T<h.length;T+=1)h[T-2][0]===a&&E(h[T-2][1])&&h[T-1][0]===n&&S(h[T-1][1])&&h[T][0]===i&&S(h[T][1])&&(w=!0,h[T-1][1]=h[T-2][1].slice(-1)+h[T-1][1],h[T][1]=h[T-2][1].slice(-1)+h[T][1],h[T-2][1]=h[T-2][1].slice(0,-1));if(!w)return h;for(var R=[],T=0;T<h.length;T+=1)h[T][1].length>0&&R.push(h[T]);return R}function b(h,w,S){for(var E=w+S-1;E>=0&&E>=w-1;E--)if(E+1<h.length){var T=h[E],R=h[E+1];T[0]===R[1]&&h.splice(E,2,[T[0],T[1]+R[1]])}return h}},function(e,t){t=e.exports=typeof Object.keys=="function"?Object.keys:n,t.shim=n;function n(i){var a=[];for(var c in i)a.push(c);return a}},function(e,t){var n=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";t=e.exports=n?i:a,t.supported=i;function i(c){return Object.prototype.toString.call(c)=="[object Arguments]"}t.unsupported=a;function a(c){return c&&typeof c=="object"&&typeof c.length=="number"&&Object.prototype.hasOwnProperty.call(c,"callee")&&!Object.prototype.propertyIsEnumerable.call(c,"callee")||!1}},function(e,t){var n=Object.prototype.hasOwnProperty,i="~";function a(){}Object.create&&(a.prototype=Object.create(null),new a().__proto__||(i=!1));function c(y,d,u){this.fn=y,this.context=d,this.once=u||!1}function f(){this._events=new a,this._eventsCount=0}f.prototype.eventNames=function(){var d=[],u,o;if(this._eventsCount===0)return d;for(o in u=this._events)n.call(u,o)&&d.push(i?o.slice(1):o);return Object.getOwnPropertySymbols?d.concat(Object.getOwnPropertySymbols(u)):d},f.prototype.listeners=function(d,u){var o=i?i+d:d,s=this._events[o];if(u)return!!s;if(!s)return[];if(s.fn)return[s.fn];for(var _=0,m=s.length,g=new Array(m);_<m;_++)g[_]=s[_].fn;return g},f.prototype.emit=function(d,u,o,s,_,m){var g=i?i+d:d;if(!this._events[g])return!1;var v=this._events[g],p=arguments.length,b,h;if(v.fn){switch(v.once&&this.removeListener(d,v.fn,void 0,!0),p){case 1:return v.fn.call(v.context),!0;case 2:return v.fn.call(v.context,u),!0;case 3:return v.fn.call(v.context,u,o),!0;case 4:return v.fn.call(v.context,u,o,s),!0;case 5:return v.fn.call(v.context,u,o,s,_),!0;case 6:return v.fn.call(v.context,u,o,s,_,m),!0}for(h=1,b=new Array(p-1);h<p;h++)b[h-1]=arguments[h];v.fn.apply(v.context,b)}else{var w=v.length,S;for(h=0;h<w;h++)switch(v[h].once&&this.removeListener(d,v[h].fn,void 0,!0),p){case 1:v[h].fn.call(v[h].context);break;case 2:v[h].fn.call(v[h].context,u);break;case 3:v[h].fn.call(v[h].context,u,o);break;case 4:v[h].fn.call(v[h].context,u,o,s);break;default:if(!b)for(S=1,b=new Array(p-1);S<p;S++)b[S-1]=arguments[S];v[h].fn.apply(v[h].context,b)}}return!0},f.prototype.on=function(d,u,o){var s=new c(u,o||this),_=i?i+d:d;return this._events[_]?this._events[_].fn?this._events[_]=[this._events[_],s]:this._events[_].push(s):(this._events[_]=s,this._eventsCount++),this},f.prototype.once=function(d,u,o){var s=new c(u,o||this,!0),_=i?i+d:d;return this._events[_]?this._events[_].fn?this._events[_]=[this._events[_],s]:this._events[_].push(s):(this._events[_]=s,this._eventsCount++),this},f.prototype.removeListener=function(d,u,o,s){var _=i?i+d:d;if(!this._events[_])return this;if(!u)return--this._eventsCount===0?this._events=new a:delete this._events[_],this;var m=this._events[_];if(m.fn)m.fn===u&&(!s||m.once)&&(!o||m.context===o)&&(--this._eventsCount===0?this._events=new a:delete this._events[_]);else{for(var g=0,v=[],p=m.length;g<p;g++)(m[g].fn!==u||s&&!m[g].once||o&&m[g].context!==o)&&v.push(m[g]);v.length?this._events[_]=v.length===1?v[0]:v:--this._eventsCount===0?this._events=new a:delete this._events[_]}return this},f.prototype.removeAllListeners=function(d){var u;return d?(u=i?i+d:d,this._events[u]&&(--this._eventsCount===0?this._events=new a:delete this._events[u])):(this._events=new a,this._eventsCount=0),this},f.prototype.off=f.prototype.removeListener,f.prototype.addListener=f.prototype.on,f.prototype.setMaxListeners=function(){return this},f.prefixed=i,f.EventEmitter=f,typeof e!="undefined"&&(e.exports=f)},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.matchText=t.matchSpacing=t.matchNewline=t.matchBlot=t.matchAttributor=t.default=void 0;var i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(W){return typeof W}:function(W){return W&&typeof Symbol=="function"&&W.constructor===Symbol&&W!==Symbol.prototype?"symbol":typeof W},a=function(){function W(V,Z){var Q=[],Y=!0,ne=!1,ie=void 0;try{for(var ae=V[Symbol.iterator](),me;!(Y=(me=ae.next()).done)&&(Q.push(me.value),!(Z&&Q.length===Z));Y=!0);}catch(we){ne=!0,ie=we}finally{try{!Y&&ae.return&&ae.return()}finally{if(ne)throw ie}}return Q}return function(V,Z){if(Array.isArray(V))return V;if(Symbol.iterator in Object(V))return W(V,Z);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),c=function(){function W(V,Z){for(var Q=0;Q<Z.length;Q++){var Y=Z[Q];Y.enumerable=Y.enumerable||!1,Y.configurable=!0,"value"in Y&&(Y.writable=!0),Object.defineProperty(V,Y.key,Y)}}return function(V,Z,Q){return Z&&W(V.prototype,Z),Q&&W(V,Q),V}}(),f=n(3),y=k(f),d=n(2),u=k(d),o=n(0),s=k(o),_=n(5),m=k(_),g=n(10),v=k(g),p=n(9),b=k(p),h=n(36),w=n(37),S=n(13),E=k(S),T=n(26),R=n(38),A=n(39),O=n(40);function k(W){return W&&W.__esModule?W:{default:W}}function x(W,V,Z){return V in W?Object.defineProperty(W,V,{value:Z,enumerable:!0,configurable:!0,writable:!0}):W[V]=Z,W}function q(W,V){if(!(W instanceof V))throw new TypeError("Cannot call a class as a function")}function I(W,V){if(!W)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return V&&(typeof V=="object"||typeof V=="function")?V:W}function U(W,V){if(typeof V!="function"&&V!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof V);W.prototype=Object.create(V&&V.prototype,{constructor:{value:W,enumerable:!1,writable:!0,configurable:!0}}),V&&(Object.setPrototypeOf?Object.setPrototypeOf(W,V):W.__proto__=V)}var F=(0,v.default)("quill:clipboard"),H="__ql-matcher",D=[[Node.TEXT_NODE,he],[Node.TEXT_NODE,se],["br",X],[Node.ELEMENT_NODE,se],[Node.ELEMENT_NODE,J],[Node.ELEMENT_NODE,le],[Node.ELEMENT_NODE,G],[Node.ELEMENT_NODE,ye],["li",te],["b",z.bind(z,"bold")],["i",z.bind(z,"italic")],["style",ee]],L=[h.AlignAttribute,R.DirectionAttribute].reduce(function(W,V){return W[V.keyName]=V,W},{}),P=[h.AlignStyle,w.BackgroundStyle,T.ColorStyle,R.DirectionStyle,A.FontStyle,O.SizeStyle].reduce(function(W,V){return W[V.keyName]=V,W},{}),N=function(W){U(V,W);function V(Z,Q){q(this,V);var Y=I(this,(V.__proto__||Object.getPrototypeOf(V)).call(this,Z,Q));return Y.quill.root.addEventListener("paste",Y.onPaste.bind(Y)),Y.container=Y.quill.addContainer("ql-clipboard"),Y.container.setAttribute("contenteditable",!0),Y.container.setAttribute("tabindex",-1),Y.matchers=[],D.concat(Y.options.matchers).forEach(function(ne){var ie=a(ne,2),ae=ie[0],me=ie[1];!Q.matchVisual&&me===le||Y.addMatcher(ae,me)}),Y}return c(V,[{key:"addMatcher",value:function(Q,Y){this.matchers.push([Q,Y])}},{key:"convert",value:function(Q){if(typeof Q=="string")return this.container.innerHTML=Q.replace(/\>\r?\n +\</g,"><"),this.convert();var Y=this.quill.getFormat(this.quill.selection.savedRange.index);if(Y[E.default.blotName]){var ne=this.container.innerText;return this.container.innerHTML="",new u.default().insert(ne,x({},E.default.blotName,Y[E.default.blotName]))}var ie=this.prepareMatching(),ae=a(ie,2),me=ae[0],we=ae[1],ce=M(this.container,me,we);return B(ce,`
`)&&ce.ops[ce.ops.length-1].attributes==null&&(ce=ce.compose(new u.default().retain(ce.length()-1).delete(1))),F.log("convert",this.container.innerHTML,ce),this.container.innerHTML="",ce}},{key:"dangerouslyPasteHTML",value:function(Q,Y){var ne=arguments.length>2&&arguments[2]!==void 0?arguments[2]:m.default.sources.API;if(typeof Q=="string")this.quill.setContents(this.convert(Q),Y),this.quill.setSelection(0,m.default.sources.SILENT);else{var ie=this.convert(Y);this.quill.updateContents(new u.default().retain(Q).concat(ie),ne),this.quill.setSelection(Q+ie.length(),m.default.sources.SILENT)}}},{key:"onPaste",value:function(Q){var Y=this;if(!(Q.defaultPrevented||!this.quill.isEnabled())){var ne=this.quill.getSelection(),ie=new u.default().retain(ne.index),ae=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(m.default.sources.SILENT),setTimeout(function(){ie=ie.concat(Y.convert()).delete(ne.length),Y.quill.updateContents(ie,m.default.sources.USER),Y.quill.setSelection(ie.length()-ne.length,m.default.sources.SILENT),Y.quill.scrollingContainer.scrollTop=ae,Y.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var Q=this,Y=[],ne=[];return this.matchers.forEach(function(ie){var ae=a(ie,2),me=ae[0],we=ae[1];switch(me){case Node.TEXT_NODE:ne.push(we);break;case Node.ELEMENT_NODE:Y.push(we);break;default:[].forEach.call(Q.container.querySelectorAll(me),function(ce){ce[H]=ce[H]||[],ce[H].push(we)});break}}),[Y,ne]}}]),V}(b.default);N.DEFAULTS={matchers:[],matchVisual:!0};function C(W,V,Z){return(typeof V=="undefined"?"undefined":i(V))==="object"?Object.keys(V).reduce(function(Q,Y){return C(Q,Y,V[Y])},W):W.reduce(function(Q,Y){return Y.attributes&&Y.attributes[V]?Q.push(Y):Q.insert(Y.insert,(0,y.default)({},x({},V,Z),Y.attributes))},new u.default)}function $(W){if(W.nodeType!==Node.ELEMENT_NODE)return{};var V="__ql-computed-style";return W[V]||(W[V]=window.getComputedStyle(W))}function B(W,V){for(var Z="",Q=W.ops.length-1;Q>=0&&Z.length<V.length;--Q){var Y=W.ops[Q];if(typeof Y.insert!="string")break;Z=Y.insert+Z}return Z.slice(-1*V.length)===V}function j(W){if(W.childNodes.length===0)return!1;var V=$(W);return["block","list-item"].indexOf(V.display)>-1}function M(W,V,Z){return W.nodeType===W.TEXT_NODE?Z.reduce(function(Q,Y){return Y(W,Q)},new u.default):W.nodeType===W.ELEMENT_NODE?[].reduce.call(W.childNodes||[],function(Q,Y){var ne=M(Y,V,Z);return Y.nodeType===W.ELEMENT_NODE&&(ne=V.reduce(function(ie,ae){return ae(Y,ie)},ne),ne=(Y[H]||[]).reduce(function(ie,ae){return ae(Y,ie)},ne)),Q.concat(ne)},new u.default):new u.default}function z(W,V,Z){return C(Z,W,!0)}function G(W,V){var Z=s.default.Attributor.Attribute.keys(W),Q=s.default.Attributor.Class.keys(W),Y=s.default.Attributor.Style.keys(W),ne={};return Z.concat(Q).concat(Y).forEach(function(ie){var ae=s.default.query(ie,s.default.Scope.ATTRIBUTE);ae!=null&&(ne[ae.attrName]=ae.value(W),ne[ae.attrName])||(ae=L[ie],ae!=null&&(ae.attrName===ie||ae.keyName===ie)&&(ne[ae.attrName]=ae.value(W)||void 0),ae=P[ie],ae!=null&&(ae.attrName===ie||ae.keyName===ie)&&(ae=P[ie],ne[ae.attrName]=ae.value(W)||void 0))}),Object.keys(ne).length>0&&(V=C(V,ne)),V}function J(W,V){var Z=s.default.query(W);if(Z==null)return V;if(Z.prototype instanceof s.default.Embed){var Q={},Y=Z.value(W);Y!=null&&(Q[Z.blotName]=Y,V=new u.default().insert(Q,Z.formats(W)))}else typeof Z.formats=="function"&&(V=C(V,Z.blotName,Z.formats(W)));return V}function X(W,V){return B(V,`
`)||V.insert(`
`),V}function ee(){return new u.default}function te(W,V){var Z=s.default.query(W);if(Z==null||Z.blotName!=="list-item"||!B(V,`
`))return V;for(var Q=-1,Y=W.parentNode;!Y.classList.contains("ql-clipboard");)(s.default.query(Y)||{}).blotName==="list"&&(Q+=1),Y=Y.parentNode;return Q<=0?V:V.compose(new u.default().retain(V.length()-1).retain(1,{indent:Q}))}function se(W,V){return B(V,`
`)||(j(W)||V.length()>0&&W.nextSibling&&j(W.nextSibling))&&V.insert(`
`),V}function le(W,V){if(j(W)&&W.nextElementSibling!=null&&!B(V,`

`)){var Z=W.offsetHeight+parseFloat($(W).marginTop)+parseFloat($(W).marginBottom);W.nextElementSibling.offsetTop>W.offsetTop+Z*1.5&&V.insert(`
`)}return V}function ye(W,V){var Z={},Q=W.style||{};return Q.fontStyle&&$(W).fontStyle==="italic"&&(Z.italic=!0),Q.fontWeight&&($(W).fontWeight.startsWith("bold")||parseInt($(W).fontWeight)>=700)&&(Z.bold=!0),Object.keys(Z).length>0&&(V=C(V,Z)),parseFloat(Q.textIndent||0)>0&&(V=new u.default().insert("	").concat(V)),V}function he(W,V){var Z=W.data;if(W.parentNode.tagName==="O:P")return V.insert(Z.trim());if(Z.trim().length===0&&W.parentNode.classList.contains("ql-clipboard"))return V;if(!$(W.parentNode).whiteSpace.startsWith("pre")){var Q=function(ne,ie){return ie=ie.replace(/[^\u00a0]/g,""),ie.length<1&&ne?" ":ie};Z=Z.replace(/\r\n/g," ").replace(/\n/g," "),Z=Z.replace(/\s\s+/g,Q.bind(Q,!0)),(W.previousSibling==null&&j(W.parentNode)||W.previousSibling!=null&&j(W.previousSibling))&&(Z=Z.replace(/^\s+/,Q.bind(Q,!1))),(W.nextSibling==null&&j(W.parentNode)||W.nextSibling!=null&&j(W.nextSibling))&&(Z=Z.replace(/\s+$/,Q.bind(Q,!1)))}return V.insert(Z)}t.default=N,t.matchAttributor=G,t.matchBlot=J,t.matchNewline=se,t.matchSpacing=le,t.matchText=he},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function _(m,g){for(var v=0;v<g.length;v++){var p=g[v];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(m,p.key,p)}}return function(m,g,v){return g&&_(m.prototype,g),v&&_(m,v),m}}(),a=function _(m,g,v){m===null&&(m=Function.prototype);var p=Object.getOwnPropertyDescriptor(m,g);if(p===void 0){var b=Object.getPrototypeOf(m);return b===null?void 0:_(b,g,v)}else{if("value"in p)return p.value;var h=p.get;return h===void 0?void 0:h.call(v)}},c=n(6),f=y(c);function y(_){return _&&_.__esModule?_:{default:_}}function d(_,m){if(!(_ instanceof m))throw new TypeError("Cannot call a class as a function")}function u(_,m){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:_}function o(_,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);_.prototype=Object.create(m&&m.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(_,m):_.__proto__=m)}var s=function(_){o(m,_);function m(){return d(this,m),u(this,(m.__proto__||Object.getPrototypeOf(m)).apply(this,arguments))}return i(m,[{key:"optimize",value:function(v){a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"optimize",this).call(this,v),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return a(m.__proto__||Object.getPrototypeOf(m),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),m}(f.default);s.blotName="bold",s.tagName=["STRONG","B"],t.default=s},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.addControls=t.default=void 0;var i=function(){function O(k,x){var q=[],I=!0,U=!1,F=void 0;try{for(var H=k[Symbol.iterator](),D;!(I=(D=H.next()).done)&&(q.push(D.value),!(x&&q.length===x));I=!0);}catch(L){U=!0,F=L}finally{try{!I&&H.return&&H.return()}finally{if(U)throw F}}return q}return function(k,x){if(Array.isArray(k))return k;if(Symbol.iterator in Object(k))return O(k,x);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=function(){function O(k,x){for(var q=0;q<x.length;q++){var I=x[q];I.enumerable=I.enumerable||!1,I.configurable=!0,"value"in I&&(I.writable=!0),Object.defineProperty(k,I.key,I)}}return function(k,x,q){return x&&O(k.prototype,x),q&&O(k,q),k}}(),c=n(2),f=v(c),y=n(0),d=v(y),u=n(5),o=v(u),s=n(10),_=v(s),m=n(9),g=v(m);function v(O){return O&&O.__esModule?O:{default:O}}function p(O,k,x){return k in O?Object.defineProperty(O,k,{value:x,enumerable:!0,configurable:!0,writable:!0}):O[k]=x,O}function b(O,k){if(!(O instanceof k))throw new TypeError("Cannot call a class as a function")}function h(O,k){if(!O)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return k&&(typeof k=="object"||typeof k=="function")?k:O}function w(O,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof k);O.prototype=Object.create(k&&k.prototype,{constructor:{value:O,enumerable:!1,writable:!0,configurable:!0}}),k&&(Object.setPrototypeOf?Object.setPrototypeOf(O,k):O.__proto__=k)}var S=(0,_.default)("quill:toolbar"),E=function(O){w(k,O);function k(x,q){b(this,k);var I=h(this,(k.__proto__||Object.getPrototypeOf(k)).call(this,x,q));if(Array.isArray(I.options.container)){var U=document.createElement("div");R(U,I.options.container),x.container.parentNode.insertBefore(U,x.container),I.container=U}else typeof I.options.container=="string"?I.container=document.querySelector(I.options.container):I.container=I.options.container;if(!(I.container instanceof HTMLElement)){var F;return F=S.error("Container required for toolbar",I.options),h(I,F)}return I.container.classList.add("ql-toolbar"),I.controls=[],I.handlers={},Object.keys(I.options.handlers).forEach(function(H){I.addHandler(H,I.options.handlers[H])}),[].forEach.call(I.container.querySelectorAll("button, select"),function(H){I.attach(H)}),I.quill.on(o.default.events.EDITOR_CHANGE,function(H,D){H===o.default.events.SELECTION_CHANGE&&I.update(D)}),I.quill.on(o.default.events.SCROLL_OPTIMIZE,function(){var H=I.quill.selection.getRange(),D=i(H,1),L=D[0];I.update(L)}),I}return a(k,[{key:"addHandler",value:function(q,I){this.handlers[q]=I}},{key:"attach",value:function(q){var I=this,U=[].find.call(q.classList,function(H){return H.indexOf("ql-")===0});if(U){if(U=U.slice(3),q.tagName==="BUTTON"&&q.setAttribute("type","button"),this.handlers[U]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[U]==null){S.warn("ignoring attaching to disabled format",U,q);return}if(d.default.query(U)==null){S.warn("ignoring attaching to nonexistent format",U,q);return}}var F=q.tagName==="SELECT"?"change":"click";q.addEventListener(F,function(H){var D=void 0;if(q.tagName==="SELECT"){if(q.selectedIndex<0)return;var L=q.options[q.selectedIndex];L.hasAttribute("selected")?D=!1:D=L.value||!1}else q.classList.contains("ql-active")?D=!1:D=q.value||!q.hasAttribute("value"),H.preventDefault();I.quill.focus();var P=I.quill.selection.getRange(),N=i(P,1),C=N[0];if(I.handlers[U]!=null)I.handlers[U].call(I,D);else if(d.default.query(U).prototype instanceof d.default.Embed){if(D=prompt("Enter "+U),!D)return;I.quill.updateContents(new f.default().retain(C.index).delete(C.length).insert(p({},U,D)),o.default.sources.USER)}else I.quill.format(U,D,o.default.sources.USER);I.update(C)}),this.controls.push([U,q])}}},{key:"update",value:function(q){var I=q==null?{}:this.quill.getFormat(q);this.controls.forEach(function(U){var F=i(U,2),H=F[0],D=F[1];if(D.tagName==="SELECT"){var L=void 0;if(q==null)L=null;else if(I[H]==null)L=D.querySelector("option[selected]");else if(!Array.isArray(I[H])){var P=I[H];typeof P=="string"&&(P=P.replace(/\"/g,'\\"')),L=D.querySelector('option[value="'+P+'"]')}L==null?(D.value="",D.selectedIndex=-1):L.selected=!0}else if(q==null)D.classList.remove("ql-active");else if(D.hasAttribute("value")){var N=I[H]===D.getAttribute("value")||I[H]!=null&&I[H].toString()===D.getAttribute("value")||I[H]==null&&!D.getAttribute("value");D.classList.toggle("ql-active",N)}else D.classList.toggle("ql-active",I[H]!=null)})}}]),k}(g.default);E.DEFAULTS={};function T(O,k,x){var q=document.createElement("button");q.setAttribute("type","button"),q.classList.add("ql-"+k),x!=null&&(q.value=x),O.appendChild(q)}function R(O,k){Array.isArray(k[0])||(k=[k]),k.forEach(function(x){var q=document.createElement("span");q.classList.add("ql-formats"),x.forEach(function(I){if(typeof I=="string")T(q,I);else{var U=Object.keys(I)[0],F=I[U];Array.isArray(F)?A(q,U,F):T(q,U,F)}}),O.appendChild(q)})}function A(O,k,x){var q=document.createElement("select");q.classList.add("ql-"+k),x.forEach(function(I){var U=document.createElement("option");I!==!1?U.setAttribute("value",I):U.setAttribute("selected","selected"),q.appendChild(U)}),O.appendChild(q)}E.DEFAULTS={container:null,handlers:{clean:function(){var k=this,x=this.quill.getSelection();if(x!=null)if(x.length==0){var q=this.quill.getFormat();Object.keys(q).forEach(function(I){d.default.query(I,d.default.Scope.INLINE)!=null&&k.quill.format(I,!1)})}else this.quill.removeFormat(x,o.default.sources.USER)},direction:function(k){var x=this.quill.getFormat().align;k==="rtl"&&x==null?this.quill.format("align","right",o.default.sources.USER):!k&&x==="right"&&this.quill.format("align",!1,o.default.sources.USER),this.quill.format("direction",k,o.default.sources.USER)},indent:function(k){var x=this.quill.getSelection(),q=this.quill.getFormat(x),I=parseInt(q.indent||0);if(k==="+1"||k==="-1"){var U=k==="+1"?1:-1;q.direction==="rtl"&&(U*=-1),this.quill.format("indent",I+U,o.default.sources.USER)}},link:function(k){k===!0&&(k=prompt("Enter link URL:")),this.quill.format("link",k,o.default.sources.USER)},list:function(k){var x=this.quill.getSelection(),q=this.quill.getFormat(x);k==="check"?q.list==="checked"||q.list==="unchecked"?this.quill.format("list",!1,o.default.sources.USER):this.quill.format("list","unchecked",o.default.sources.USER):this.quill.format("list",k,o.default.sources.USER)}}},t.default=E,t.addControls=R},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function _(m,g){for(var v=0;v<g.length;v++){var p=g[v];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(m,p.key,p)}}return function(m,g,v){return g&&_(m.prototype,g),v&&_(m,v),m}}(),a=function _(m,g,v){m===null&&(m=Function.prototype);var p=Object.getOwnPropertyDescriptor(m,g);if(p===void 0){var b=Object.getPrototypeOf(m);return b===null?void 0:_(b,g,v)}else{if("value"in p)return p.value;var h=p.get;return h===void 0?void 0:h.call(v)}},c=n(28),f=y(c);function y(_){return _&&_.__esModule?_:{default:_}}function d(_,m){if(!(_ instanceof m))throw new TypeError("Cannot call a class as a function")}function u(_,m){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:_}function o(_,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);_.prototype=Object.create(m&&m.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(_,m):_.__proto__=m)}var s=function(_){o(m,_);function m(g,v){d(this,m);var p=u(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,g));return p.label.innerHTML=v,p.container.classList.add("ql-color-picker"),[].slice.call(p.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(b){b.classList.add("ql-primary")}),p}return i(m,[{key:"buildItem",value:function(v){var p=a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"buildItem",this).call(this,v);return p.style.backgroundColor=v.getAttribute("value")||"",p}},{key:"selectItem",value:function(v,p){a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"selectItem",this).call(this,v,p);var b=this.label.querySelector(".ql-color-label"),h=v&&v.getAttribute("data-value")||"";b&&(b.tagName==="line"?b.style.stroke=h:b.style.fill=h)}}]),m}(f.default);t.default=s},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function _(m,g){for(var v=0;v<g.length;v++){var p=g[v];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(m,p.key,p)}}return function(m,g,v){return g&&_(m.prototype,g),v&&_(m,v),m}}(),a=function _(m,g,v){m===null&&(m=Function.prototype);var p=Object.getOwnPropertyDescriptor(m,g);if(p===void 0){var b=Object.getPrototypeOf(m);return b===null?void 0:_(b,g,v)}else{if("value"in p)return p.value;var h=p.get;return h===void 0?void 0:h.call(v)}},c=n(28),f=y(c);function y(_){return _&&_.__esModule?_:{default:_}}function d(_,m){if(!(_ instanceof m))throw new TypeError("Cannot call a class as a function")}function u(_,m){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:_}function o(_,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);_.prototype=Object.create(m&&m.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(_,m):_.__proto__=m)}var s=function(_){o(m,_);function m(g,v){d(this,m);var p=u(this,(m.__proto__||Object.getPrototypeOf(m)).call(this,g));return p.container.classList.add("ql-icon-picker"),[].forEach.call(p.container.querySelectorAll(".ql-picker-item"),function(b){b.innerHTML=v[b.getAttribute("data-value")||""]}),p.defaultItem=p.container.querySelector(".ql-selected"),p.selectItem(p.defaultItem),p}return i(m,[{key:"selectItem",value:function(v,p){a(m.prototype.__proto__||Object.getPrototypeOf(m.prototype),"selectItem",this).call(this,v,p),v=v||this.defaultItem,this.label.innerHTML=v.innerHTML}}]),m}(f.default);t.default=s},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function f(y,d){for(var u=0;u<d.length;u++){var o=d[u];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(y,o.key,o)}}return function(y,d,u){return d&&f(y.prototype,d),u&&f(y,u),y}}();function a(f,y){if(!(f instanceof y))throw new TypeError("Cannot call a class as a function")}var c=function(){function f(y,d){var u=this;a(this,f),this.quill=y,this.boundsContainer=d||document.body,this.root=y.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){u.root.style.marginTop=-1*u.quill.root.scrollTop+"px"}),this.hide()}return i(f,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(d){var u=d.left+d.width/2-this.root.offsetWidth/2,o=d.bottom+this.quill.root.scrollTop;this.root.style.left=u+"px",this.root.style.top=o+"px",this.root.classList.remove("ql-flip");var s=this.boundsContainer.getBoundingClientRect(),_=this.root.getBoundingClientRect(),m=0;if(_.right>s.right&&(m=s.right-_.right,this.root.style.left=u+m+"px"),_.left<s.left&&(m=s.left-_.left,this.root.style.left=u+m+"px"),_.bottom>s.bottom){var g=_.bottom-_.top,v=d.bottom-d.top+g;this.root.style.top=o-v+"px",this.root.classList.add("ql-flip")}return m}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),f}();t.default=c},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function A(O,k){var x=[],q=!0,I=!1,U=void 0;try{for(var F=O[Symbol.iterator](),H;!(q=(H=F.next()).done)&&(x.push(H.value),!(k&&x.length===k));q=!0);}catch(D){I=!0,U=D}finally{try{!q&&F.return&&F.return()}finally{if(I)throw U}}return x}return function(O,k){if(Array.isArray(O))return O;if(Symbol.iterator in Object(O))return A(O,k);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=function A(O,k,x){O===null&&(O=Function.prototype);var q=Object.getOwnPropertyDescriptor(O,k);if(q===void 0){var I=Object.getPrototypeOf(O);return I===null?void 0:A(I,k,x)}else{if("value"in q)return q.value;var U=q.get;return U===void 0?void 0:U.call(x)}},c=function(){function A(O,k){for(var x=0;x<k.length;x++){var q=k[x];q.enumerable=q.enumerable||!1,q.configurable=!0,"value"in q&&(q.writable=!0),Object.defineProperty(O,q.key,q)}}return function(O,k,x){return k&&A(O.prototype,k),x&&A(O,x),O}}(),f=n(3),y=b(f),d=n(8),u=b(d),o=n(43),s=b(o),_=n(27),m=b(_),g=n(15),v=n(41),p=b(v);function b(A){return A&&A.__esModule?A:{default:A}}function h(A,O){if(!(A instanceof O))throw new TypeError("Cannot call a class as a function")}function w(A,O){if(!A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O&&(typeof O=="object"||typeof O=="function")?O:A}function S(A,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof O);A.prototype=Object.create(O&&O.prototype,{constructor:{value:A,enumerable:!1,writable:!0,configurable:!0}}),O&&(Object.setPrototypeOf?Object.setPrototypeOf(A,O):A.__proto__=O)}var E=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],T=function(A){S(O,A);function O(k,x){h(this,O),x.modules.toolbar!=null&&x.modules.toolbar.container==null&&(x.modules.toolbar.container=E);var q=w(this,(O.__proto__||Object.getPrototypeOf(O)).call(this,k,x));return q.quill.container.classList.add("ql-snow"),q}return c(O,[{key:"extendToolbar",value:function(x){x.container.classList.add("ql-snow"),this.buildButtons([].slice.call(x.container.querySelectorAll("button")),p.default),this.buildPickers([].slice.call(x.container.querySelectorAll("select")),p.default),this.tooltip=new R(this.quill,this.options.bounds),x.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(q,I){x.handlers.link.call(x,!I.format.link)})}}]),O}(s.default);T.DEFAULTS=(0,y.default)(!0,{},s.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(O){if(O){var k=this.quill.getSelection();if(k==null||k.length==0)return;var x=this.quill.getText(k);/^\S+@\S+\.\S+$/.test(x)&&x.indexOf("mailto:")!==0&&(x="mailto:"+x);var q=this.quill.theme.tooltip;q.edit("link",x)}else this.quill.format("link",!1)}}}}});var R=function(A){S(O,A);function O(k,x){h(this,O);var q=w(this,(O.__proto__||Object.getPrototypeOf(O)).call(this,k,x));return q.preview=q.root.querySelector("a.ql-preview"),q}return c(O,[{key:"listen",value:function(){var x=this;a(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(q){x.root.classList.contains("ql-editing")?x.save():x.edit("link",x.preview.textContent),q.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(q){if(x.linkRange!=null){var I=x.linkRange;x.restoreFocus(),x.quill.formatText(I,"link",!1,u.default.sources.USER),delete x.linkRange}q.preventDefault(),x.hide()}),this.quill.on(u.default.events.SELECTION_CHANGE,function(q,I,U){if(q!=null){if(q.length===0&&U===u.default.sources.USER){var F=x.quill.scroll.descendant(m.default,q.index),H=i(F,2),D=H[0],L=H[1];if(D!=null){x.linkRange=new g.Range(q.index-L,D.length());var P=m.default.formats(D.domNode);x.preview.textContent=P,x.preview.setAttribute("href",P),x.show(),x.position(x.quill.getBounds(x.linkRange));return}}else delete x.linkRange;x.hide()}})}},{key:"show",value:function(){a(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),O}(o.BaseTooltip);R.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),t.default=T},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(29),a=Y(i),c=n(36),f=n(38),y=n(64),d=n(65),u=Y(d),o=n(66),s=Y(o),_=n(67),m=Y(_),g=n(37),v=n(26),p=n(39),b=n(40),h=n(56),w=Y(h),S=n(68),E=Y(S),T=n(27),R=Y(T),A=n(69),O=Y(A),k=n(70),x=Y(k),q=n(71),I=Y(q),U=n(72),F=Y(U),H=n(73),D=Y(H),L=n(13),P=Y(L),N=n(74),C=Y(N),$=n(75),B=Y($),j=n(57),M=Y(j),z=n(41),G=Y(z),J=n(28),X=Y(J),ee=n(59),te=Y(ee),se=n(60),le=Y(se),ye=n(61),he=Y(ye),W=n(108),V=Y(W),Z=n(62),Q=Y(Z);function Y(ne){return ne&&ne.__esModule?ne:{default:ne}}a.default.register({"attributors/attribute/direction":f.DirectionAttribute,"attributors/class/align":c.AlignClass,"attributors/class/background":g.BackgroundClass,"attributors/class/color":v.ColorClass,"attributors/class/direction":f.DirectionClass,"attributors/class/font":p.FontClass,"attributors/class/size":b.SizeClass,"attributors/style/align":c.AlignStyle,"attributors/style/background":g.BackgroundStyle,"attributors/style/color":v.ColorStyle,"attributors/style/direction":f.DirectionStyle,"attributors/style/font":p.FontStyle,"attributors/style/size":b.SizeStyle},!0),a.default.register({"formats/align":c.AlignClass,"formats/direction":f.DirectionClass,"formats/indent":y.IndentClass,"formats/background":g.BackgroundStyle,"formats/color":v.ColorStyle,"formats/font":p.FontClass,"formats/size":b.SizeClass,"formats/blockquote":u.default,"formats/code-block":P.default,"formats/header":s.default,"formats/list":m.default,"formats/bold":w.default,"formats/code":L.Code,"formats/italic":E.default,"formats/link":R.default,"formats/script":O.default,"formats/strike":x.default,"formats/underline":I.default,"formats/image":F.default,"formats/video":D.default,"formats/list/item":_.ListItem,"modules/formula":C.default,"modules/syntax":B.default,"modules/toolbar":M.default,"themes/bubble":V.default,"themes/snow":Q.default,"ui/icons":G.default,"ui/picker":X.default,"ui/icon-picker":le.default,"ui/color-picker":te.default,"ui/tooltip":he.default},!0),t.default=a.default},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.IndentClass=void 0;var i=function(){function m(g,v){for(var p=0;p<v.length;p++){var b=v[p];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(g,b.key,b)}}return function(g,v,p){return v&&m(g.prototype,v),p&&m(g,p),g}}(),a=function m(g,v,p){g===null&&(g=Function.prototype);var b=Object.getOwnPropertyDescriptor(g,v);if(b===void 0){var h=Object.getPrototypeOf(g);return h===null?void 0:m(h,v,p)}else{if("value"in b)return b.value;var w=b.get;return w===void 0?void 0:w.call(p)}},c=n(0),f=y(c);function y(m){return m&&m.__esModule?m:{default:m}}function d(m,g){if(!(m instanceof g))throw new TypeError("Cannot call a class as a function")}function u(m,g){if(!m)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:m}function o(m,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);m.prototype=Object.create(g&&g.prototype,{constructor:{value:m,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(m,g):m.__proto__=g)}var s=function(m){o(g,m);function g(){return d(this,g),u(this,(g.__proto__||Object.getPrototypeOf(g)).apply(this,arguments))}return i(g,[{key:"add",value:function(p,b){if(b==="+1"||b==="-1"){var h=this.value(p)||0;b=b==="+1"?h+1:h-1}return b===0?(this.remove(p),!0):a(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"add",this).call(this,p,b)}},{key:"canAdd",value:function(p,b){return a(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"canAdd",this).call(this,p,b)||a(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"canAdd",this).call(this,p,parseInt(b))}},{key:"value",value:function(p){return parseInt(a(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"value",this).call(this,p))||void 0}}]),g}(f.default.Attributor.Class),_=new s("indent","ql-indent",{scope:f.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});t.IndentClass=_},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(4),a=c(i);function c(o){return o&&o.__esModule?o:{default:o}}function f(o,s){if(!(o instanceof s))throw new TypeError("Cannot call a class as a function")}function y(o,s){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:o}function d(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);o.prototype=Object.create(s&&s.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(o,s):o.__proto__=s)}var u=function(o){d(s,o);function s(){return f(this,s),y(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return s}(a.default);u.blotName="blockquote",u.tagName="blockquote",t.default=u},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function s(_,m){for(var g=0;g<m.length;g++){var v=m[g];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(_,v.key,v)}}return function(_,m,g){return m&&s(_.prototype,m),g&&s(_,g),_}}(),a=n(4),c=f(a);function f(s){return s&&s.__esModule?s:{default:s}}function y(s,_){if(!(s instanceof _))throw new TypeError("Cannot call a class as a function")}function d(s,_){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:s}function u(s,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);s.prototype=Object.create(_&&_.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(s,_):s.__proto__=_)}var o=function(s){u(_,s);function _(){return y(this,_),d(this,(_.__proto__||Object.getPrototypeOf(_)).apply(this,arguments))}return i(_,null,[{key:"formats",value:function(g){return this.tagName.indexOf(g.tagName)+1}}]),_}(c.default);o.blotName="header",o.tagName=["H1","H2","H3","H4","H5","H6"],t.default=o},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.ListItem=void 0;var i=function(){function h(w,S){for(var E=0;E<S.length;E++){var T=S[E];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(w,T.key,T)}}return function(w,S,E){return S&&h(w.prototype,S),E&&h(w,E),w}}(),a=function h(w,S,E){w===null&&(w=Function.prototype);var T=Object.getOwnPropertyDescriptor(w,S);if(T===void 0){var R=Object.getPrototypeOf(w);return R===null?void 0:h(R,S,E)}else{if("value"in T)return T.value;var A=T.get;return A===void 0?void 0:A.call(E)}},c=n(0),f=s(c),y=n(4),d=s(y),u=n(25),o=s(u);function s(h){return h&&h.__esModule?h:{default:h}}function _(h,w,S){return w in h?Object.defineProperty(h,w,{value:S,enumerable:!0,configurable:!0,writable:!0}):h[w]=S,h}function m(h,w){if(!(h instanceof w))throw new TypeError("Cannot call a class as a function")}function g(h,w){if(!h)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return w&&(typeof w=="object"||typeof w=="function")?w:h}function v(h,w){if(typeof w!="function"&&w!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof w);h.prototype=Object.create(w&&w.prototype,{constructor:{value:h,enumerable:!1,writable:!0,configurable:!0}}),w&&(Object.setPrototypeOf?Object.setPrototypeOf(h,w):h.__proto__=w)}var p=function(h){v(w,h);function w(){return m(this,w),g(this,(w.__proto__||Object.getPrototypeOf(w)).apply(this,arguments))}return i(w,[{key:"format",value:function(E,T){E===b.blotName&&!T?this.replaceWith(f.default.create(this.statics.scope)):a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"format",this).call(this,E,T)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(E,T){return this.parent.isolate(this.offset(this.parent),this.length()),E===this.parent.statics.blotName?(this.parent.replaceWith(E,T),this):(this.parent.unwrap(),a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"replaceWith",this).call(this,E,T))}}],[{key:"formats",value:function(E){return E.tagName===this.tagName?void 0:a(w.__proto__||Object.getPrototypeOf(w),"formats",this).call(this,E)}}]),w}(d.default);p.blotName="list-item",p.tagName="LI";var b=function(h){v(w,h),i(w,null,[{key:"create",value:function(E){var T=E==="ordered"?"OL":"UL",R=a(w.__proto__||Object.getPrototypeOf(w),"create",this).call(this,T);return(E==="checked"||E==="unchecked")&&R.setAttribute("data-checked",E==="checked"),R}},{key:"formats",value:function(E){if(E.tagName==="OL")return"ordered";if(E.tagName==="UL")return E.hasAttribute("data-checked")?E.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function w(S){m(this,w);var E=g(this,(w.__proto__||Object.getPrototypeOf(w)).call(this,S)),T=function(A){if(A.target.parentNode===S){var O=E.statics.formats(S),k=f.default.find(A.target);O==="checked"?k.format("list","unchecked"):O==="unchecked"&&k.format("list","checked")}};return S.addEventListener("touchstart",T),S.addEventListener("mousedown",T),E}return i(w,[{key:"format",value:function(E,T){this.children.length>0&&this.children.tail.format(E,T)}},{key:"formats",value:function(){return _({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(E,T){if(E instanceof p)a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"insertBefore",this).call(this,E,T);else{var R=T==null?this.length():T.offset(this),A=this.split(R);A.parent.insertBefore(E,A)}}},{key:"optimize",value:function(E){a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"optimize",this).call(this,E);var T=this.next;T!=null&&T.prev===this&&T.statics.blotName===this.statics.blotName&&T.domNode.tagName===this.domNode.tagName&&T.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(T.moveChildren(this),T.remove())}},{key:"replace",value:function(E){if(E.statics.blotName!==this.statics.blotName){var T=f.default.create(this.statics.defaultChild);E.moveChildren(T),this.appendChild(T)}a(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"replace",this).call(this,E)}}]),w}(o.default);b.blotName="list",b.scope=f.default.Scope.BLOCK_BLOT,b.tagName=["OL","UL"],b.defaultChild="list-item",b.allowedChildren=[p],t.ListItem=p,t.default=b},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(56),a=c(i);function c(o){return o&&o.__esModule?o:{default:o}}function f(o,s){if(!(o instanceof s))throw new TypeError("Cannot call a class as a function")}function y(o,s){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:o}function d(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);o.prototype=Object.create(s&&s.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(o,s):o.__proto__=s)}var u=function(o){d(s,o);function s(){return f(this,s),y(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return s}(a.default);u.blotName="italic",u.tagName=["EM","I"],t.default=u},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function _(m,g){for(var v=0;v<g.length;v++){var p=g[v];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(m,p.key,p)}}return function(m,g,v){return g&&_(m.prototype,g),v&&_(m,v),m}}(),a=function _(m,g,v){m===null&&(m=Function.prototype);var p=Object.getOwnPropertyDescriptor(m,g);if(p===void 0){var b=Object.getPrototypeOf(m);return b===null?void 0:_(b,g,v)}else{if("value"in p)return p.value;var h=p.get;return h===void 0?void 0:h.call(v)}},c=n(6),f=y(c);function y(_){return _&&_.__esModule?_:{default:_}}function d(_,m){if(!(_ instanceof m))throw new TypeError("Cannot call a class as a function")}function u(_,m){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return m&&(typeof m=="object"||typeof m=="function")?m:_}function o(_,m){if(typeof m!="function"&&m!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof m);_.prototype=Object.create(m&&m.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(_,m):_.__proto__=m)}var s=function(_){o(m,_);function m(){return d(this,m),u(this,(m.__proto__||Object.getPrototypeOf(m)).apply(this,arguments))}return i(m,null,[{key:"create",value:function(v){return v==="super"?document.createElement("sup"):v==="sub"?document.createElement("sub"):a(m.__proto__||Object.getPrototypeOf(m),"create",this).call(this,v)}},{key:"formats",value:function(v){if(v.tagName==="SUB")return"sub";if(v.tagName==="SUP")return"super"}}]),m}(f.default);s.blotName="script",s.tagName=["SUB","SUP"],t.default=s},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(6),a=c(i);function c(o){return o&&o.__esModule?o:{default:o}}function f(o,s){if(!(o instanceof s))throw new TypeError("Cannot call a class as a function")}function y(o,s){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:o}function d(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);o.prototype=Object.create(s&&s.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(o,s):o.__proto__=s)}var u=function(o){d(s,o);function s(){return f(this,s),y(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return s}(a.default);u.blotName="strike",u.tagName="S",t.default=u},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=n(6),a=c(i);function c(o){return o&&o.__esModule?o:{default:o}}function f(o,s){if(!(o instanceof s))throw new TypeError("Cannot call a class as a function")}function y(o,s){if(!o)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:o}function d(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);o.prototype=Object.create(s&&s.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(o,s):o.__proto__=s)}var u=function(o){d(s,o);function s(){return f(this,s),y(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return s}(a.default);u.blotName="underline",u.tagName="U",t.default=u},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function g(v,p){for(var b=0;b<p.length;b++){var h=p[b];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(v,h.key,h)}}return function(v,p,b){return p&&g(v.prototype,p),b&&g(v,b),v}}(),a=function g(v,p,b){v===null&&(v=Function.prototype);var h=Object.getOwnPropertyDescriptor(v,p);if(h===void 0){var w=Object.getPrototypeOf(v);return w===null?void 0:g(w,p,b)}else{if("value"in h)return h.value;var S=h.get;return S===void 0?void 0:S.call(b)}},c=n(0),f=d(c),y=n(27);function d(g){return g&&g.__esModule?g:{default:g}}function u(g,v){if(!(g instanceof v))throw new TypeError("Cannot call a class as a function")}function o(g,v){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:g}function s(g,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);g.prototype=Object.create(v&&v.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(g,v):g.__proto__=v)}var _=["alt","height","width"],m=function(g){s(v,g);function v(){return u(this,v),o(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return i(v,[{key:"format",value:function(b,h){_.indexOf(b)>-1?h?this.domNode.setAttribute(b,h):this.domNode.removeAttribute(b):a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"format",this).call(this,b,h)}}],[{key:"create",value:function(b){var h=a(v.__proto__||Object.getPrototypeOf(v),"create",this).call(this,b);return typeof b=="string"&&h.setAttribute("src",this.sanitize(b)),h}},{key:"formats",value:function(b){return _.reduce(function(h,w){return b.hasAttribute(w)&&(h[w]=b.getAttribute(w)),h},{})}},{key:"match",value:function(b){return/\.(jpe?g|gif|png)$/.test(b)||/^data:image\/.+;base64/.test(b)}},{key:"sanitize",value:function(b){return(0,y.sanitize)(b,["http","https","data"])?b:"//:0"}},{key:"value",value:function(b){return b.getAttribute("src")}}]),v}(f.default.Embed);m.blotName="image",m.tagName="IMG",t.default=m},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function g(v,p){for(var b=0;b<p.length;b++){var h=p[b];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(v,h.key,h)}}return function(v,p,b){return p&&g(v.prototype,p),b&&g(v,b),v}}(),a=function g(v,p,b){v===null&&(v=Function.prototype);var h=Object.getOwnPropertyDescriptor(v,p);if(h===void 0){var w=Object.getPrototypeOf(v);return w===null?void 0:g(w,p,b)}else{if("value"in h)return h.value;var S=h.get;return S===void 0?void 0:S.call(b)}},c=n(4),f=n(27),y=d(f);function d(g){return g&&g.__esModule?g:{default:g}}function u(g,v){if(!(g instanceof v))throw new TypeError("Cannot call a class as a function")}function o(g,v){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return v&&(typeof v=="object"||typeof v=="function")?v:g}function s(g,v){if(typeof v!="function"&&v!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof v);g.prototype=Object.create(v&&v.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),v&&(Object.setPrototypeOf?Object.setPrototypeOf(g,v):g.__proto__=v)}var _=["height","width"],m=function(g){s(v,g);function v(){return u(this,v),o(this,(v.__proto__||Object.getPrototypeOf(v)).apply(this,arguments))}return i(v,[{key:"format",value:function(b,h){_.indexOf(b)>-1?h?this.domNode.setAttribute(b,h):this.domNode.removeAttribute(b):a(v.prototype.__proto__||Object.getPrototypeOf(v.prototype),"format",this).call(this,b,h)}}],[{key:"create",value:function(b){var h=a(v.__proto__||Object.getPrototypeOf(v),"create",this).call(this,b);return h.setAttribute("frameborder","0"),h.setAttribute("allowfullscreen",!0),h.setAttribute("src",this.sanitize(b)),h}},{key:"formats",value:function(b){return _.reduce(function(h,w){return b.hasAttribute(w)&&(h[w]=b.getAttribute(w)),h},{})}},{key:"sanitize",value:function(b){return y.default.sanitize(b)}},{key:"value",value:function(b){return b.getAttribute("src")}}]),v}(c.BlockEmbed);m.blotName="video",m.className="ql-video",m.tagName="IFRAME",t.default=m},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.FormulaBlot=void 0;var i=function(){function b(h,w){for(var S=0;S<w.length;S++){var E=w[S];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(h,E.key,E)}}return function(h,w,S){return w&&b(h.prototype,w),S&&b(h,S),h}}(),a=function b(h,w,S){h===null&&(h=Function.prototype);var E=Object.getOwnPropertyDescriptor(h,w);if(E===void 0){var T=Object.getPrototypeOf(h);return T===null?void 0:b(T,w,S)}else{if("value"in E)return E.value;var R=E.get;return R===void 0?void 0:R.call(S)}},c=n(35),f=s(c),y=n(5),d=s(y),u=n(9),o=s(u);function s(b){return b&&b.__esModule?b:{default:b}}function _(b,h){if(!(b instanceof h))throw new TypeError("Cannot call a class as a function")}function m(b,h){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:b}function g(b,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);b.prototype=Object.create(h&&h.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(b,h):b.__proto__=h)}var v=function(b){g(h,b);function h(){return _(this,h),m(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}return i(h,null,[{key:"create",value:function(S){var E=a(h.__proto__||Object.getPrototypeOf(h),"create",this).call(this,S);return typeof S=="string"&&(window.katex.render(S,E,{throwOnError:!1,errorColor:"#f00"}),E.setAttribute("data-value",S)),E}},{key:"value",value:function(S){return S.getAttribute("data-value")}}]),h}(f.default);v.blotName="formula",v.className="ql-formula",v.tagName="SPAN";var p=function(b){g(h,b),i(h,null,[{key:"register",value:function(){d.default.register(v,!0)}}]);function h(){_(this,h);var w=m(this,(h.__proto__||Object.getPrototypeOf(h)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return w}return h}(o.default);t.FormulaBlot=v,t.default=p},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.CodeToken=t.CodeBlock=void 0;var i=function(){function S(E,T){for(var R=0;R<T.length;R++){var A=T[R];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(E,A.key,A)}}return function(E,T,R){return T&&S(E.prototype,T),R&&S(E,R),E}}(),a=function S(E,T,R){E===null&&(E=Function.prototype);var A=Object.getOwnPropertyDescriptor(E,T);if(A===void 0){var O=Object.getPrototypeOf(E);return O===null?void 0:S(O,T,R)}else{if("value"in A)return A.value;var k=A.get;return k===void 0?void 0:k.call(R)}},c=n(0),f=m(c),y=n(5),d=m(y),u=n(9),o=m(u),s=n(13),_=m(s);function m(S){return S&&S.__esModule?S:{default:S}}function g(S,E){if(!(S instanceof E))throw new TypeError("Cannot call a class as a function")}function v(S,E){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E&&(typeof E=="object"||typeof E=="function")?E:S}function p(S,E){if(typeof E!="function"&&E!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof E);S.prototype=Object.create(E&&E.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),E&&(Object.setPrototypeOf?Object.setPrototypeOf(S,E):S.__proto__=E)}var b=function(S){p(E,S);function E(){return g(this,E),v(this,(E.__proto__||Object.getPrototypeOf(E)).apply(this,arguments))}return i(E,[{key:"replaceWith",value:function(R){this.domNode.textContent=this.domNode.textContent,this.attach(),a(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"replaceWith",this).call(this,R)}},{key:"highlight",value:function(R){var A=this.domNode.textContent;this.cachedText!==A&&((A.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=R(A),this.domNode.normalize(),this.attach()),this.cachedText=A)}}]),E}(_.default);b.className="ql-syntax";var h=new f.default.Attributor.Class("token","hljs",{scope:f.default.Scope.INLINE}),w=function(S){p(E,S),i(E,null,[{key:"register",value:function(){d.default.register(h,!0),d.default.register(b,!0)}}]);function E(T,R){g(this,E);var A=v(this,(E.__proto__||Object.getPrototypeOf(E)).call(this,T,R));if(typeof A.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var O=null;return A.quill.on(d.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(O),O=setTimeout(function(){A.highlight(),O=null},A.options.interval)}),A.highlight(),A}return i(E,[{key:"highlight",value:function(){var R=this;if(!this.quill.selection.composing){this.quill.update(d.default.sources.USER);var A=this.quill.getSelection();this.quill.scroll.descendants(b).forEach(function(O){O.highlight(R.options.highlight)}),this.quill.update(d.default.sources.SILENT),A!=null&&this.quill.setSelection(A,d.default.sources.SILENT)}}}]),E}(o.default);w.DEFAULTS={highlight:function(){return window.hljs==null?null:function(S){var E=window.hljs.highlightAuto(S);return E.value}}(),interval:1e3},t.CodeBlock=b,t.CodeToken=h,t.default=w},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(e,t){e.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(e,t){e.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(e,t){e.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(e,t){e.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(e,t){e.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.BubbleTooltip=void 0;var i=function E(T,R,A){T===null&&(T=Function.prototype);var O=Object.getOwnPropertyDescriptor(T,R);if(O===void 0){var k=Object.getPrototypeOf(T);return k===null?void 0:E(k,R,A)}else{if("value"in O)return O.value;var x=O.get;return x===void 0?void 0:x.call(A)}},a=function(){function E(T,R){for(var A=0;A<R.length;A++){var O=R[A];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(T,O.key,O)}}return function(T,R,A){return R&&E(T.prototype,R),A&&E(T,A),T}}(),c=n(3),f=g(c),y=n(8),d=g(y),u=n(43),o=g(u),s=n(15),_=n(41),m=g(_);function g(E){return E&&E.__esModule?E:{default:E}}function v(E,T){if(!(E instanceof T))throw new TypeError("Cannot call a class as a function")}function p(E,T){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return T&&(typeof T=="object"||typeof T=="function")?T:E}function b(E,T){if(typeof T!="function"&&T!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof T);E.prototype=Object.create(T&&T.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),T&&(Object.setPrototypeOf?Object.setPrototypeOf(E,T):E.__proto__=T)}var h=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],w=function(E){b(T,E);function T(R,A){v(this,T),A.modules.toolbar!=null&&A.modules.toolbar.container==null&&(A.modules.toolbar.container=h);var O=p(this,(T.__proto__||Object.getPrototypeOf(T)).call(this,R,A));return O.quill.container.classList.add("ql-bubble"),O}return a(T,[{key:"extendToolbar",value:function(A){this.tooltip=new S(this.quill,this.options.bounds),this.tooltip.root.appendChild(A.container),this.buildButtons([].slice.call(A.container.querySelectorAll("button")),m.default),this.buildPickers([].slice.call(A.container.querySelectorAll("select")),m.default)}}]),T}(o.default);w.DEFAULTS=(0,f.default)(!0,{},o.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(T){T?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var S=function(E){b(T,E);function T(R,A){v(this,T);var O=p(this,(T.__proto__||Object.getPrototypeOf(T)).call(this,R,A));return O.quill.on(d.default.events.EDITOR_CHANGE,function(k,x,q,I){if(k===d.default.events.SELECTION_CHANGE)if(x!=null&&x.length>0&&I===d.default.sources.USER){O.show(),O.root.style.left="0px",O.root.style.width="",O.root.style.width=O.root.offsetWidth+"px";var U=O.quill.getLines(x.index,x.length);if(U.length===1)O.position(O.quill.getBounds(x));else{var F=U[U.length-1],H=O.quill.getIndex(F),D=Math.min(F.length()-1,x.index+x.length-H),L=O.quill.getBounds(new s.Range(H,D));O.position(L)}}else document.activeElement!==O.textbox&&O.quill.hasFocus()&&O.hide()}),O}return a(T,[{key:"listen",value:function(){var A=this;i(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){A.root.classList.remove("ql-editing")}),this.quill.on(d.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!A.root.classList.contains("ql-hidden")){var O=A.quill.getSelection();O!=null&&A.position(A.quill.getBounds(O))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(A){var O=i(T.prototype.__proto__||Object.getPrototypeOf(T.prototype),"position",this).call(this,A),k=this.root.querySelector(".ql-tooltip-arrow");if(k.style.marginLeft="",O===0)return O;k.style.marginLeft=-1*O-k.offsetWidth/2+"px"}}]),T}(u.BaseTooltip);S.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),t.BubbleTooltip=S,t.default=w},function(e,t,n){e.exports=n(63)}]).default})}(dt)),dt.exports}var vt={exports:{}},xu=vt.exports,Wa;function Ru(){return Wa||(Wa=1,function(l,r){(function(e,t,n){l.exports=n(),l.exports.default=n()})("slugify",xu,function(){var e=JSON.parse(`{"$":"dollar","%":"percent","&":"and","<":"less",">":"greater","|":"or","¢":"cent","£":"pound","¤":"currency","¥":"yen","©":"(c)","ª":"a","®":"(r)","º":"o","À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Æ":"AE","Ç":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ý":"Y","Þ":"TH","ß":"ss","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","æ":"ae","ç":"c","è":"e","é":"e","ê":"e","ë":"e","ì":"i","í":"i","î":"i","ï":"i","ð":"d","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ù":"u","ú":"u","û":"u","ü":"u","ý":"y","þ":"th","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Č":"C","č":"c","Ď":"D","ď":"d","Đ":"DJ","đ":"dj","Ē":"E","ē":"e","Ė":"E","ė":"e","Ę":"e","ę":"e","Ě":"E","ě":"e","Ğ":"G","ğ":"g","Ģ":"G","ģ":"g","Ĩ":"I","ĩ":"i","Ī":"i","ī":"i","Į":"I","į":"i","İ":"I","ı":"i","Ķ":"k","ķ":"k","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ł":"L","ł":"l","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","Ō":"O","ō":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","Ŕ":"R","ŕ":"r","Ř":"R","ř":"r","Ś":"S","ś":"s","Ş":"S","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","Ť":"T","ť":"t","Ũ":"U","ũ":"u","Ū":"u","ū":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","Ə":"E","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","ǈ":"LJ","ǉ":"lj","ǋ":"NJ","ǌ":"nj","Ș":"S","ș":"s","Ț":"T","ț":"t","ə":"e","˚":"o","Ά":"A","Έ":"E","Ή":"H","Ί":"I","Ό":"O","Ύ":"Y","Ώ":"W","ΐ":"i","Α":"A","Β":"B","Γ":"G","Δ":"D","Ε":"E","Ζ":"Z","Η":"H","Θ":"8","Ι":"I","Κ":"K","Λ":"L","Μ":"M","Ν":"N","Ξ":"3","Ο":"O","Π":"P","Ρ":"R","Σ":"S","Τ":"T","Υ":"Y","Φ":"F","Χ":"X","Ψ":"PS","Ω":"W","Ϊ":"I","Ϋ":"Y","ά":"a","έ":"e","ή":"h","ί":"i","ΰ":"y","α":"a","β":"b","γ":"g","δ":"d","ε":"e","ζ":"z","η":"h","θ":"8","ι":"i","κ":"k","λ":"l","μ":"m","ν":"n","ξ":"3","ο":"o","π":"p","ρ":"r","ς":"s","σ":"s","τ":"t","υ":"y","φ":"f","χ":"x","ψ":"ps","ω":"w","ϊ":"i","ϋ":"y","ό":"o","ύ":"y","ώ":"w","Ё":"Yo","Ђ":"DJ","Є":"Ye","І":"I","Ї":"Yi","Ј":"J","Љ":"LJ","Њ":"NJ","Ћ":"C","Џ":"DZ","А":"A","Б":"B","В":"V","Г":"G","Д":"D","Е":"E","Ж":"Zh","З":"Z","И":"I","Й":"J","К":"K","Л":"L","М":"M","Н":"N","О":"O","П":"P","Р":"R","С":"S","Т":"T","У":"U","Ф":"F","Х":"H","Ц":"C","Ч":"Ch","Ш":"Sh","Щ":"Sh","Ъ":"U","Ы":"Y","Ь":"","Э":"E","Ю":"Yu","Я":"Ya","а":"a","б":"b","в":"v","г":"g","д":"d","е":"e","ж":"zh","з":"z","и":"i","й":"j","к":"k","л":"l","м":"m","н":"n","о":"o","п":"p","р":"r","с":"s","т":"t","у":"u","ф":"f","х":"h","ц":"c","ч":"ch","ш":"sh","щ":"sh","ъ":"u","ы":"y","ь":"","э":"e","ю":"yu","я":"ya","ё":"yo","ђ":"dj","є":"ye","і":"i","ї":"yi","ј":"j","љ":"lj","њ":"nj","ћ":"c","ѝ":"u","џ":"dz","Ґ":"G","ґ":"g","Ғ":"GH","ғ":"gh","Қ":"KH","қ":"kh","Ң":"NG","ң":"ng","Ү":"UE","ү":"ue","Ұ":"U","ұ":"u","Һ":"H","һ":"h","Ә":"AE","ә":"ae","Ө":"OE","ө":"oe","Ա":"A","Բ":"B","Գ":"G","Դ":"D","Ե":"E","Զ":"Z","Է":"E'","Ը":"Y'","Թ":"T'","Ժ":"JH","Ի":"I","Լ":"L","Խ":"X","Ծ":"C'","Կ":"K","Հ":"H","Ձ":"D'","Ղ":"GH","Ճ":"TW","Մ":"M","Յ":"Y","Ն":"N","Շ":"SH","Չ":"CH","Պ":"P","Ջ":"J","Ռ":"R'","Ս":"S","Վ":"V","Տ":"T","Ր":"R","Ց":"C","Փ":"P'","Ք":"Q'","Օ":"O''","Ֆ":"F","և":"EV","ء":"a","آ":"aa","أ":"a","ؤ":"u","إ":"i","ئ":"e","ا":"a","ب":"b","ة":"h","ت":"t","ث":"th","ج":"j","ح":"h","خ":"kh","د":"d","ذ":"th","ر":"r","ز":"z","س":"s","ش":"sh","ص":"s","ض":"dh","ط":"t","ظ":"z","ع":"a","غ":"gh","ف":"f","ق":"q","ك":"k","ل":"l","م":"m","ن":"n","ه":"h","و":"w","ى":"a","ي":"y","ً":"an","ٌ":"on","ٍ":"en","َ":"a","ُ":"u","ِ":"e","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","پ":"p","چ":"ch","ژ":"zh","ک":"k","گ":"g","ی":"y","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","฿":"baht","ა":"a","ბ":"b","გ":"g","დ":"d","ე":"e","ვ":"v","ზ":"z","თ":"t","ი":"i","კ":"k","ლ":"l","მ":"m","ნ":"n","ო":"o","პ":"p","ჟ":"zh","რ":"r","ს":"s","ტ":"t","უ":"u","ფ":"f","ქ":"k","ღ":"gh","ყ":"q","შ":"sh","ჩ":"ch","ც":"ts","ძ":"dz","წ":"ts","ჭ":"ch","ხ":"kh","ჯ":"j","ჰ":"h","Ṣ":"S","ṣ":"s","Ẁ":"W","ẁ":"w","Ẃ":"W","ẃ":"w","Ẅ":"W","ẅ":"w","ẞ":"SS","Ạ":"A","ạ":"a","Ả":"A","ả":"a","Ấ":"A","ấ":"a","Ầ":"A","ầ":"a","Ẩ":"A","ẩ":"a","Ẫ":"A","ẫ":"a","Ậ":"A","ậ":"a","Ắ":"A","ắ":"a","Ằ":"A","ằ":"a","Ẳ":"A","ẳ":"a","Ẵ":"A","ẵ":"a","Ặ":"A","ặ":"a","Ẹ":"E","ẹ":"e","Ẻ":"E","ẻ":"e","Ẽ":"E","ẽ":"e","Ế":"E","ế":"e","Ề":"E","ề":"e","Ể":"E","ể":"e","Ễ":"E","ễ":"e","Ệ":"E","ệ":"e","Ỉ":"I","ỉ":"i","Ị":"I","ị":"i","Ọ":"O","ọ":"o","Ỏ":"O","ỏ":"o","Ố":"O","ố":"o","Ồ":"O","ồ":"o","Ổ":"O","ổ":"o","Ỗ":"O","ỗ":"o","Ộ":"O","ộ":"o","Ớ":"O","ớ":"o","Ờ":"O","ờ":"o","Ở":"O","ở":"o","Ỡ":"O","ỡ":"o","Ợ":"O","ợ":"o","Ụ":"U","ụ":"u","Ủ":"U","ủ":"u","Ứ":"U","ứ":"u","Ừ":"U","ừ":"u","Ử":"U","ử":"u","Ữ":"U","ữ":"u","Ự":"U","ự":"u","Ỳ":"Y","ỳ":"y","Ỵ":"Y","ỵ":"y","Ỷ":"Y","ỷ":"y","Ỹ":"Y","ỹ":"y","–":"-","‘":"'","’":"'","“":"\\"","”":"\\"","„":"\\"","†":"+","•":"*","…":"...","₠":"ecu","₢":"cruzeiro","₣":"french franc","₤":"lira","₥":"mill","₦":"naira","₧":"peseta","₨":"rupee","₩":"won","₪":"new shequel","₫":"dong","€":"euro","₭":"kip","₮":"tugrik","₯":"drachma","₰":"penny","₱":"peso","₲":"guarani","₳":"austral","₴":"hryvnia","₵":"cedi","₸":"kazakhstani tenge","₹":"indian rupee","₺":"turkish lira","₽":"russian ruble","₿":"bitcoin","℠":"sm","™":"tm","∂":"d","∆":"delta","∑":"sum","∞":"infinity","♥":"love","元":"yuan","円":"yen","﷼":"rial","ﻵ":"laa","ﻷ":"laa","ﻹ":"lai","ﻻ":"la"}`),t=JSON.parse('{"bg":{"Й":"Y","Ц":"Ts","Щ":"Sht","Ъ":"A","Ь":"Y","й":"y","ц":"ts","щ":"sht","ъ":"a","ь":"y"},"de":{"Ä":"AE","ä":"ae","Ö":"OE","ö":"oe","Ü":"UE","ü":"ue","ß":"ss","%":"prozent","&":"und","|":"oder","∑":"summe","∞":"unendlich","♥":"liebe"},"es":{"%":"por ciento","&":"y","<":"menor que",">":"mayor que","|":"o","¢":"centavos","£":"libras","¤":"moneda","₣":"francos","∑":"suma","∞":"infinito","♥":"amor"},"fr":{"%":"pourcent","&":"et","<":"plus petit",">":"plus grand","|":"ou","¢":"centime","£":"livre","¤":"devise","₣":"franc","∑":"somme","∞":"infini","♥":"amour"},"pt":{"%":"porcento","&":"e","<":"menor",">":"maior","|":"ou","¢":"centavo","∑":"soma","£":"libra","∞":"infinito","♥":"amor"},"uk":{"И":"Y","и":"y","Й":"Y","й":"y","Ц":"Ts","ц":"ts","Х":"Kh","х":"kh","Щ":"Shch","щ":"shch","Г":"H","г":"h"},"vi":{"Đ":"D","đ":"d"},"da":{"Ø":"OE","ø":"oe","Å":"AA","å":"aa","%":"procent","&":"og","|":"eller","$":"dollar","<":"mindre end",">":"større end"},"nb":{"&":"og","Å":"AA","Æ":"AE","Ø":"OE","å":"aa","æ":"ae","ø":"oe"},"it":{"&":"e"},"nl":{"&":"en"},"sv":{"&":"och","Å":"AA","Ä":"AE","Ö":"OE","å":"aa","ä":"ae","ö":"oe"}}');function n(i,a){if(typeof i!="string")throw new Error("slugify: string argument expected");a=typeof a=="string"?{replacement:a}:a||{};var c=t[a.locale]||{},f=a.replacement===void 0?"-":a.replacement,y=a.trim===void 0?!0:a.trim,d=i.normalize().split("").reduce(function(u,o){var s=c[o];return s===void 0&&(s=e[o]),s===void 0&&(s=o),s===f&&(s=" "),u+s.replace(a.remove||/[^\w\s$*_+~.()'"!\-:@]+/g,"")},"");return a.strict&&(d=d.replace(/[^A-Za-z0-9\s]/g,"")),y&&(d=d.trim()),d=d.replace(/\s+/g,f),a.lower&&(d=d.toLowerCase()),d}return n.extend=function(i){Object.assign(e,i)},n})}(vt)),vt.exports}var qu=Ru();const Yu=Va(qu);export{Wu as A,Mu as F,Fo as H,Bu as P,zu as R,Gu as S,ot as _,Vu as a,Ju as b,Iu as h,Du as j,Cu as m,$o as n,Lu as r,Yu as s,An as u};
