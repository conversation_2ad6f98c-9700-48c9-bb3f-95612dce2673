var f=(o,u,i)=>new Promise((d,s)=>{var p=t=>{try{n(i.next(t))}catch(a){s(a)}},c=t=>{try{n(i.throw(t))}catch(a){s(a)}},n=t=>t.done?d(t.value):Promise.resolve(t.value).then(p,c);n((i=i.apply(o,u)).next())});import{c as x,r as l,j as e}from"./react-vendor-QWuLYCV5.js";import{l as g,m as v}from"./utils-BwAkzwKy.js";import{P as D}from"./components-BP4YpoFo.js";import"./vendor-CJchTyIO.js";import"./supabase-vendor-DEo8APS7.js";const U=({searchQuery:o})=>{const{username:u}=x(),[i,d]=l.useState([]),[s,p]=l.useState(null),[c,n]=l.useState(!0),[t,a]=l.useState(null);l.useEffect(()=>{u&&b()},[u,o]);const b=()=>f(null,null,function*(){try{n(!0);const r=yield g(u);if(!r)throw new Error("Author not found");p(r);const m=yield v(r.id,o);d(m)}catch(r){a("Author not found")}finally{n(!1)}}),N=r=>r?r.split(" ").map(m=>m.charAt(0)).join("").toUpperCase().substring(0,2):"A",h=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return c?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"loading",children:"Loading author..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:65,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:64,columnNumber:7},void 0):t||!s?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"error",children:t||"Author not found"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:73,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:72,columnNumber:7},void 0):e.jsxDEV(e.Fragment,{children:[s&&e.jsxDEV("div",{style:{textAlign:"center",marginBottom:"30px",padding:"40px 20px",background:"white",border:"1px solid #f0f0f0"},children:[e.jsxDEV("div",{style:{width:"80px",height:"80px",background:"#ddd",borderRadius:"50%",margin:"0 auto 20px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"32px",fontWeight:"bold",color:"#666"},children:N(s.display_name)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:88,columnNumber:11},void 0),e.jsxDEV("h1",{style:{fontSize:"32px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:s.display_name},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:104,columnNumber:11},void 0),e.jsxDEV("p",{style:{color:"#666",fontSize:"16px",marginBottom:"10px"},children:["@",s.user_login]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:113,columnNumber:11},void 0),e.jsxDEV("p",{style:{color:"#999",fontSize:"14px",marginBottom:"10px"},children:["Member since ",h(s.user_registered)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:117,columnNumber:11},void 0),e.jsxDEV("p",{style:{color:"#999",fontSize:"14px"},children:[i.length," ",i.length===1?"post":"posts"," published"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:121,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:81,columnNumber:9},void 0),i.length===0?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"loading",children:o?`No posts found by ${s==null?void 0:s.display_name} for "${o}"`:`No posts found by ${s==null?void 0:s.display_name}`},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:129,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:128,columnNumber:9},void 0):e.jsxDEV("div",{className:"main-grid",children:i.map(r=>e.jsxDEV(D,{post:r},r.id,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:139,columnNumber:13},void 0))},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:137,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Author.jsx",lineNumber:79,columnNumber:5},void 0)};export{U as default};
