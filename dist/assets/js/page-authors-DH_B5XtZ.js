var f=(i,l,t)=>new Promise((c,a)=>{var n=r=>{try{o(t.next(r))}catch(e){a(e)}},p=r=>{try{o(t.throw(r))}catch(e){a(e)}},o=r=>r.done?c(r.value):Promise.resolve(r.value).then(n,p);o((t=t.apply(i,l)).next())});import{r as d,j as s,L as N}from"./react-vendor-QWuLYCV5.js";import{n as b}from"./utils-BwAkzwKy.js";import"./vendor-CJchTyIO.js";import"./supabase-vendor-DEo8APS7.js";const k=({searchQuery:i})=>{const[l,t]=d.useState([]),[c,a]=d.useState(!0),[n,p]=d.useState(null);d.useEffect(()=>{o()},[i]);const o=()=>f(null,null,function*(){try{a(!0);let e=yield b();if(i&&i.trim()){const u=i.toLowerCase();e=e.filter(m=>m.display_name.toLowerCase().includes(u)||m.username.toLowerCase().includes(u))}e.sort((u,m)=>(m.post_count||0)-(u.post_count||0)),t(e)}catch(e){p("Failed to load authors")}finally{a(!1)}}),r=e=>e?e.split(" ").map(u=>u.charAt(0)).join("").toUpperCase().substring(0,2):"A";return c?s.jsxDEV("div",{className:"author-grid",children:s.jsxDEV("div",{className:"loading",children:"Loading authors..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:57,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:56,columnNumber:7},void 0):n?s.jsxDEV("div",{className:"author-grid",children:s.jsxDEV("div",{className:"error",children:n},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:65,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:64,columnNumber:7},void 0):l.length===0?s.jsxDEV("div",{className:"author-grid",children:s.jsxDEV("div",{className:"loading",children:i?`No authors found for "${i}"`:"No authors available"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:73,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:72,columnNumber:7},void 0):s.jsxDEV("div",{className:"author-grid",children:l.map(e=>s.jsxDEV(N,{to:`/author/${e.username}`,className:"author-card",children:[s.jsxDEV("div",{className:"author-avatar",children:r(e.display_name)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:88,columnNumber:11},void 0),s.jsxDEV("div",{className:"author-name",children:e.display_name},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:91,columnNumber:11},void 0),s.jsxDEV("div",{className:"author-count",children:[e.post_count," ",e.post_count===1?"post":"posts"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:92,columnNumber:11},void 0)]},e.id,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:83,columnNumber:9},void 0))},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Authors.jsx",lineNumber:81,columnNumber:5},void 0)};export{k as default};
