var ee=Object.defineProperty,se=Object.defineProperties;var re=Object.getOwnPropertyDescriptors;var H=Object.getOwnPropertySymbols;var oe=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var W=(s,n,o)=>n in s?ee(s,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[n]=o,N=(s,n)=>{for(var o in n||(n={}))oe.call(n,o)&&W(s,o,n[o]);if(H)for(var o of H(n))ne.call(n,o)&&W(s,o,n[o]);return s},x=(s,n)=>se(s,re(n));var h=(s,n,o)=>new Promise((m,r)=>{var i=u=>{try{b(o.next(u))}catch(p){r(p)}},f=u=>{try{b(o.throw(u))}catch(p){r(p)}},b=u=>u.done?m(u.value):Promise.resolve(u.value).then(i,f);b((o=o.apply(s,n)).next())});import{r as a,V as k,j as e,u as M,L as R,R as ie,a as te,b as le,N as ae}from"./react-vendor-QWuLYCV5.js";import{s as C,d as me,c as Q,g as $,a as G,f as ue,b as de,u as ce,e as pe,h as K,i as Y,v as be,j as B,k as fe}from"./utils-BwAkzwKy.js";const X=a.createContext({}),J=()=>{const s=a.useContext(X);if(!s)throw new Error("useAuth must be used within an AuthProvider");return s},ye=({children:s})=>{const[n,o]=a.useState(null),[m,r]=a.useState(!0),[i,f]=a.useState(null);a.useEffect(()=>{C.auth.getSession().then(({data:{session:d}})=>{var t;f(d),o((t=d==null?void 0:d.user)!=null?t:null),r(!1)});const{data:{subscription:l}}=C.auth.onAuthStateChange((d,t)=>h(null,null,function*(){var v;f(t),o((v=t==null?void 0:t.user)!=null?v:null),r(!1),d==="SIGNED_IN"?k.success("Successfully signed in!"):d==="SIGNED_OUT"&&k.success("Successfully signed out!")}));return()=>l.unsubscribe()},[]);const b=(l,d)=>h(null,null,function*(){try{r(!0);const{data:t,error:v}=yield C.auth.signInWithPassword({email:l,password:d});if(v)throw v;return{data:t,error:null}}catch(t){return k.error(t.message||"Failed to sign in"),{data:null,error:t}}finally{r(!1)}}),u=()=>h(null,null,function*(){try{r(!0);const{error:l}=yield C.auth.signOut();if(l)throw l}catch(l){k.error(l.message||"Failed to sign out")}finally{r(!1)}}),p=(v,A,...P)=>h(null,[v,A,...P],function*(l,d,t={}){try{r(!0);const{data:w,error:U}=yield C.auth.signUp({email:l,password:d,options:{data:t}});if(U)throw U;return{data:w,error:null}}catch(w){return k.error(w.message||"Failed to sign up"),{data:null,error:w}}finally{r(!1)}}),c=l=>h(null,null,function*(){try{const{data:d,error:t}=yield C.auth.resetPasswordForEmail(l,{redirectTo:`${window.location.origin}/admin/reset-password`});if(t)throw t;return k.success("Password reset email sent!"),{data:d,error:null}}catch(d){return k.error(d.message||"Failed to send reset email"),{data:null,error:d}}}),_=l=>h(null,null,function*(){try{const{data:d,error:t}=yield C.auth.updateUser({password:l});if(t)throw t;return k.success("Password updated successfully!"),{data:d,error:null}}catch(d){return k.error(d.message||"Failed to update password"),{data:null,error:d}}}),g=()=>h(null,null,function*(){if(!n)return null;try{const{data:l,error:d}=yield C.from("users").select("*").eq("id",n.id).single();if(d)throw d;return l}catch(l){return null}}),E={user:n,session:i,loading:m,signIn:b,signOut:u,signUp:p,resetPassword:c,updatePassword:_,getUserProfile:g,isAdmin:()=>h(null,null,function*(){const l=yield g();return(l==null?void 0:l.role)==="admin"}),isEditor:()=>h(null,null,function*(){const l=yield g();return(l==null?void 0:l.role)==="editor"||(l==null?void 0:l.role)==="admin"})};return e.jsxDEV(X.Provider,{value:E,children:s},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/contexts/AuthContext.jsx",lineNumber:192,columnNumber:5},void 0)},Ne=a.memo(({onSearch:s,searchQuery:n,setSearchQuery:o})=>{const m=M(),r=u=>!!(u==="/"&&m.pathname==="/"||u!=="/"&&m.pathname.startsWith(u)),i=a.useCallback(me(u=>{s&&s(u)},300),[s]),f=u=>{const p=u.target.value;o(p),i(p)},b=()=>m.pathname.startsWith("/authors")?"Search Authors...":"Search...";return e.jsxDEV("div",{className:"header",children:[e.jsxDEV(R,{to:"/",className:"logo",children:"SAYARI"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:37,columnNumber:7},void 0),e.jsxDEV("nav",{className:"nav",children:[e.jsxDEV(R,{to:"/",className:`nav-item ${r("/")?"active":""}`,children:"All"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:42,columnNumber:9},void 0),e.jsxDEV(R,{to:"/category/shayari",className:`nav-item ${r("/category/shayari")?"active":""}`,children:"Shayari"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:48,columnNumber:9},void 0),e.jsxDEV(R,{to:"/category/quotes",className:`nav-item ${r("/category/quotes")?"active":""}`,children:"Quotes"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:54,columnNumber:9},void 0),e.jsxDEV(R,{to:"/category/wishes",className:`nav-item ${r("/category/wishes")?"active":""}`,children:"Wishes"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:60,columnNumber:9},void 0),e.jsxDEV(R,{to:"/authors",className:`nav-item ${r("/authors")?"active":""}`,children:"Authors"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:66,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:41,columnNumber:7},void 0),e.jsxDEV("div",{className:"search-container",children:e.jsxDEV("input",{type:"text",className:"search",placeholder:b(),value:n,onChange:f,autoComplete:"off",spellCheck:"false"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:75,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:74,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Header.jsx",lineNumber:36,columnNumber:5},void 0)});Ne.displayName="Header";const we=()=>e.jsxDEV("div",{className:"footer",children:e.jsxDEV("div",{className:"footer-content",children:[e.jsxDEV("p",{children:"© 2025 Sayari Blog. All rights reserved."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Footer.jsx",lineNumber:5,columnNumber:9},void 0),e.jsxDEV("p",{style:{marginTop:"10px",fontSize:"12px",color:"#999"},children:"A collection of beautiful Hindi Shayari, Quotes, and Wishes"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Footer.jsx",lineNumber:6,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Footer.jsx",lineNumber:4,columnNumber:7},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/Footer.jsx",lineNumber:3,columnNumber:5},void 0),q=a.memo(({type:s="post",count:n=1,className:o="",style:m={}})=>{const r=N({backgroundColor:"#f0f0f0",borderRadius:"4px",position:"relative",overflow:"hidden"},m),i={position:"absolute",top:0,left:"-100%",width:"100%",height:"100%",background:"linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)",animation:"shimmer 1.5s infinite"},f=()=>e.jsxDEV("div",{className:`skeleton-post ${o}`,style:{marginBottom:"20px"},children:[e.jsxDEV("div",{style:x(N({},r),{width:"100%",height:"200px",marginBottom:"15px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:35,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:34,columnNumber:7},void 0),e.jsxDEV("div",{style:x(N({},r),{width:"80%",height:"24px",marginBottom:"10px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:40,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:39,columnNumber:7},void 0),e.jsxDEV("div",{style:x(N({},r),{width:"100%",height:"16px",marginBottom:"8px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:45,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:44,columnNumber:7},void 0),e.jsxDEV("div",{style:x(N({},r),{width:"90%",height:"16px",marginBottom:"8px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:48,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:47,columnNumber:7},void 0),e.jsxDEV("div",{style:x(N({},r),{width:"70%",height:"16px",marginBottom:"15px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:51,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:50,columnNumber:7},void 0),e.jsxDEV("div",{style:{display:"flex",gap:"15px"},children:[e.jsxDEV("div",{style:x(N({},r),{width:"80px",height:"14px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:57,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:56,columnNumber:9},void 0),e.jsxDEV("div",{style:x(N({},r),{width:"100px",height:"14px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:60,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:59,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:55,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:32,columnNumber:5},void 0),b=()=>e.jsxDEV("div",{className:`skeleton-author ${o}`,style:{marginBottom:"15px"},children:e.jsxDEV("div",{style:{display:"flex",alignItems:"center",gap:"15px"},children:[e.jsxDEV("div",{style:x(N({},r),{width:"50px",height:"50px",borderRadius:"50%"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:71,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:70,columnNumber:9},void 0),e.jsxDEV("div",{style:{flex:1},children:[e.jsxDEV("div",{style:x(N({},r),{width:"150px",height:"18px",marginBottom:"5px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:77,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:76,columnNumber:11},void 0),e.jsxDEV("div",{style:x(N({},r),{width:"200px",height:"14px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:82,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:81,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:74,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:68,columnNumber:7},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:67,columnNumber:5},void 0),u=()=>e.jsxDEV("div",{className:`skeleton-header ${o}`,style:{marginBottom:"30px"},children:e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxDEV("div",{style:x(N({},r),{width:"120px",height:"32px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:94,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:93,columnNumber:9},void 0),e.jsxDEV("div",{style:{display:"flex",gap:"20px"},children:[1,2,3,4,5].map(g=>e.jsxDEV("div",{style:x(N({},r),{width:"60px",height:"20px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:101,columnNumber:15},void 0)},g,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:100,columnNumber:13},void 0))},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:98,columnNumber:9},void 0),e.jsxDEV("div",{style:x(N({},r),{width:"200px",height:"36px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:108,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:107,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:91,columnNumber:7},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:90,columnNumber:5},void 0),p=({lines:g=3,width:j="100%"})=>e.jsxDEV("div",{className:`skeleton-text ${o}`,children:Array.from({length:g}).map((y,E)=>e.jsxDEV("div",{style:x(N({},r),{width:E===g-1?"70%":j,height:"16px",marginBottom:"8px"}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:126,columnNumber:11},void 0)},E,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:117,columnNumber:9},void 0))},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:115,columnNumber:5},void 0),c=({width:g="100%",height:j="200px"})=>e.jsxDEV("div",{className:`skeleton-image ${o}`,style:x(N({},r),{width:g,height:j}),children:e.jsxDEV("div",{style:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:137,columnNumber:7},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:133,columnNumber:5},void 0),_=()=>{switch(s){case"post":return e.jsxDEV(f,{},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:144,columnNumber:16},void 0);case"author":return e.jsxDEV(b,{},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:146,columnNumber:16},void 0);case"header":return e.jsxDEV(u,{},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:148,columnNumber:16},void 0);case"text":return e.jsxDEV(p,{},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:150,columnNumber:16},void 0);case"image":return e.jsxDEV(c,{},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:152,columnNumber:16},void 0);default:return e.jsxDEV(f,{},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:154,columnNumber:16},void 0)}};return e.jsxDEV(e.Fragment,{children:[e.jsxDEV("style",{children:`
          @keyframes shimmer {
            0% {
              transform: translateX(-100%);
            }
            100% {
              transform: translateX(100%);
            }
          }
        `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:160,columnNumber:7},void 0),e.jsxDEV("div",{className:`skeleton-loader ${o}`,children:Array.from({length:n}).map((g,j)=>e.jsxDEV("div",{children:_()},j,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:174,columnNumber:11},void 0))},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:172,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:159,columnNumber:5},void 0)});q.displayName="SkeletonLoader";a.memo(()=>e.jsxDEV(q,{type:"post",count:1},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:188,columnNumber:40},void 0));a.memo(()=>e.jsxDEV(q,{type:"author",count:1},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:189,columnNumber:42},void 0));a.memo(()=>e.jsxDEV(q,{type:"header",count:1},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:190,columnNumber:42},void 0));a.memo(({lines:s=3})=>e.jsxDEV(q,{type:"text",lines:s},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:191,columnNumber:53},void 0));a.memo(({width:s,height:n})=>e.jsxDEV(q,{type:"image",width:s,height:n},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/SkeletonLoader.jsx",lineNumber:192,columnNumber:58},void 0));const O=a.memo(({src:s,alt:n,width:o,height:m,className:r="",style:i={},lazy:f=!0,priority:b=!1,sizes:u="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",aspectRatio:p=null})=>{const[c,_]=a.useState(!1),[g,j]=a.useState(!f||b),[y,E]=a.useState(!1),l=a.useRef(null),d=a.useRef(null);a.useEffect(()=>{if(!f||b||g)return;const U=new IntersectionObserver(z=>{z.forEach(L=>{L.isIntersecting&&(j(!0),U.disconnect())})},{rootMargin:"50px",threshold:.1});return l.current&&(U.observe(l.current),d.current=U),()=>{d.current&&d.current.disconnect()}},[f,b,g]);const t=(U,z,L=80)=>{if(!U)return null;const T=Q(U);if(T.includes("supabase.co/storage/v1/object/public/"))try{const V=new URL(T);return V.searchParams.set("width",z.toString()),V.searchParams.set("quality",L.toString()),V.searchParams.set("format","webp"),V.toString()}catch(V){return T}return T},v=(U,z="original")=>U?[320,480,640,768,1024,1280,1920].filter(V=>!o||V<=o*2).map(V=>{let I=t(U,V);if(z==="webp")if(I.includes("supabase.co/storage/v1/object/public/"))try{const F=new URL(I);F.searchParams.set("format","webp"),I=F.toString()}catch(F){}else I=I.replace(/\.(jpg|jpeg|png)$/i,".webp");return`${I} ${V}w`}).join(", "):"",A=()=>{_(!0)},P=()=>{E(!0)},w=e.jsxDEV("div",{className:`image-placeholder ${r}`,style:x(N({},i),{backgroundColor:"#f0f0f0",display:"flex",alignItems:"center",justifyContent:"center",color:"#999",fontSize:"14px"}),ref:l,children:y?"Failed to load":"Loading..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/OptimizedImage.jsx",lineNumber:130,columnNumber:5},void 0);return!g||y?w:e.jsxDEV("div",{className:`optimized-image-container ${r}`,style:i,ref:l,children:[e.jsxDEV("picture",{children:[e.jsxDEV("source",{srcSet:v(s,"webp"),sizes:u,type:"image/webp"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/OptimizedImage.jsx",lineNumber:161,columnNumber:9},void 0),e.jsxDEV("img",{src:t(s,o),srcSet:v(s),sizes:u,alt:n,width:o,height:m,loading:f&&!b?"lazy":"eager",fetchPriority:b?"high":"auto",decoding:"async",onLoad:A,onError:P,style:{width:"100%",height:"100%",objectFit:"cover",transition:"opacity 0.3s ease",opacity:c?1:0,display:"block"}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/OptimizedImage.jsx",lineNumber:167,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/OptimizedImage.jsx",lineNumber:159,columnNumber:7},void 0),!c&&e.jsxDEV("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"#f0f0f0",display:"flex",alignItems:"center",justifyContent:"center",color:"#999",fontSize:"14px"},children:e.jsxDEV("div",{style:{width:"16px",height:"16px",border:"2px solid #f3f3f3",borderTop:"2px solid #333",borderRadius:"50%",animation:"spin 1s linear infinite"}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/OptimizedImage.jsx",lineNumber:205,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/OptimizedImage.jsx",lineNumber:190,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/OptimizedImage.jsx",lineNumber:158,columnNumber:5},void 0)},(s,n)=>s.src===n.src&&s.width===n.width&&s.height===n.height&&s.lazy===n.lazy&&s.priority===n.priority);O.displayName="OptimizedImage";const ve=({post:s,showDebug:n=!1})=>{const[o,m]=a.useState(null);return a.useEffect(()=>{if(!n||!s)return;const r=s.featured_image_url,i=Q(r),f=$(s.content,s.featured_image_url),b="https://pub-your_account_id.r2.dev/blog-images",u=!b.includes("your_account_id");m({originalUrl:r,convertedUrl:i,thumbnailUrl:f,r2PublicUrl:b,isR2Configured:u,wasConverted:r!==i,isSupabaseUrl:r==null?void 0:r.includes("supabase.co"),isPlaceholderR2:r==null?void 0:r.includes("your-account.r2.cloudflarestorage.com"),isExternalUrl:r&&!r.includes("supabase.co")&&!r.includes("your-account.r2.cloudflarestorage.com"),hasImage:!!r})},[s,n]),!n||!o?null:e.jsxDEV("div",{style:{position:"fixed",top:"10px",right:"10px",background:"rgba(0, 0, 0, 0.9)",color:"white",padding:"15px",borderRadius:"8px",fontSize:"12px",maxWidth:"400px",zIndex:9999,fontFamily:"monospace"},children:[e.jsxDEV("h4",{style:{margin:"0 0 10px 0",color:"#4CAF50"},children:"🐛 Image Debug Info"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:53,columnNumber:7},void 0),e.jsxDEV("div",{style:{marginBottom:"8px"},children:[e.jsxDEV("strong",{children:"Post:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:56,columnNumber:9},void 0)," ",s.title]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:55,columnNumber:7},void 0),e.jsxDEV("div",{style:{marginBottom:"8px"},children:[e.jsxDEV("strong",{children:"R2 Configured:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:60,columnNumber:9},void 0),e.jsxDEV("span",{style:{color:o.isR2Configured?"#4CAF50":"#f44336"},children:o.isR2Configured?" ✅ Yes":" ❌ No"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:61,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:59,columnNumber:7},void 0),o.r2PublicUrl&&e.jsxDEV("div",{style:{marginBottom:"8px"},children:[e.jsxDEV("strong",{children:"R2 URL:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:68,columnNumber:11},void 0)," ",o.r2PublicUrl]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:67,columnNumber:9},void 0),e.jsxDEV("div",{style:{marginBottom:"8px"},children:[e.jsxDEV("strong",{children:"Original URL:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:73,columnNumber:9},void 0),e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",padding:"4px",borderRadius:"4px",wordBreak:"break-all",fontSize:"10px"},children:o.originalUrl||"None"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:74,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:72,columnNumber:7},void 0),e.jsxDEV("div",{style:{marginBottom:"8px"},children:[e.jsxDEV("strong",{children:"Converted URL:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:86,columnNumber:9},void 0),e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",padding:"4px",borderRadius:"4px",wordBreak:"break-all",fontSize:"10px"},children:o.convertedUrl||"None"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:87,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:85,columnNumber:7},void 0),e.jsxDEV("div",{style:{marginBottom:"8px"},children:[e.jsxDEV("strong",{children:"Status:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:99,columnNumber:9},void 0),!o.hasImage&&e.jsxDEV("span",{style:{color:"#9e9e9e"},children:" ℹ️ No featured image"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:101,columnNumber:11},void 0),o.isSupabaseUrl&&o.wasConverted&&e.jsxDEV("span",{style:{color:"#4CAF50"},children:" ✅ Supabase URL converted to R2"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:104,columnNumber:11},void 0),o.isSupabaseUrl&&!o.wasConverted&&e.jsxDEV("span",{style:{color:"#ff9800"},children:" ⚠️ Supabase URL not converted (R2 not configured)"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:107,columnNumber:11},void 0),o.isPlaceholderR2&&o.wasConverted&&e.jsxDEV("span",{style:{color:"#4CAF50"},children:" ✅ Placeholder R2 URL converted"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:110,columnNumber:11},void 0),o.isPlaceholderR2&&!o.wasConverted&&e.jsxDEV("span",{style:{color:"#f44336"},children:" ❌ Placeholder R2 URL (R2 not configured)"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:113,columnNumber:11},void 0),o.isExternalUrl&&e.jsxDEV("span",{style:{color:"#2196F3"},children:" ℹ️ External URL (no conversion needed)"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:116,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:98,columnNumber:7},void 0),o.thumbnailUrl&&e.jsxDEV("div",{style:{marginTop:"10px"},children:[e.jsxDEV("strong",{children:"Image Test:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:122,columnNumber:11},void 0),e.jsxDEV("img",{src:o.thumbnailUrl,alt:"Debug test",style:{width:"100px",height:"60px",objectFit:"cover",border:"1px solid #ccc",borderRadius:"4px",display:"block",marginTop:"5px"},onLoad:()=>{},onError:()=>{}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:123,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:121,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/ImageDebugger.jsx",lineNumber:40,columnNumber:5},void 0)},xe=a.memo(({post:s,featured:n=!1,priority:o=!1,showDebug:m=!1})=>{const r=()=>"Admin";if(n){const f=$(s.content,s.featured_image_url);return e.jsxDEV(R,{to:`/${s.slug}`,className:"featured",children:[f&&e.jsxDEV("div",{className:"featured-image",children:e.jsxDEV(O,{src:f,alt:s.title,width:800,height:200,priority:o||n,style:{borderRadius:"8px",marginBottom:"15px"}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:21,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:20,columnNumber:11},void 0),e.jsxDEV("div",{className:"featured-title",children:s.title},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:34,columnNumber:9},void 0),e.jsxDEV("div",{className:"featured-content",children:G(s.content,200)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:35,columnNumber:9},void 0),e.jsxDEV("div",{className:"featured-author",children:["By ",r()]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:38,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:18,columnNumber:7},void 0)}const i=$(s.content,s.featured_image_url);return e.jsxDEV(e.Fragment,{children:[e.jsxDEV(R,{to:`/${s.slug}`,className:"poem-card",children:[i&&e.jsxDEV("div",{className:"poem-image",children:e.jsxDEV(O,{src:i,alt:s.title,width:400,height:150,lazy:!o,priority:o,style:{borderRadius:"8px",marginBottom:"10px"}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:52,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:51,columnNumber:11},void 0),e.jsxDEV("div",{className:"poem-title",children:s.title},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:66,columnNumber:9},void 0),e.jsxDEV("div",{className:"poem-preview",children:G(s.content)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:67,columnNumber:9},void 0),e.jsxDEV("div",{className:"poem-meta",children:[e.jsxDEV("div",{className:"author",children:["By ",r()]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:71,columnNumber:11},void 0),e.jsxDEV("div",{className:"date",children:ue(s.published_at)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:72,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:70,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:49,columnNumber:7},void 0),m&&e.jsxDEV(ve,{post:s,showDebug:m},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:75,columnNumber:21},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/PostCard.jsx",lineNumber:48,columnNumber:5},void 0)},(s,n)=>s.post.id===n.post.id&&s.featured===n.featured&&s.priority===n.priority);xe.displayName="PostCard";const ge=({value:s,onChange:n,placeholder:o="Start writing..."})=>{const m=a.useRef(null),[r,i]=a.useState(s||"");a.useEffect(()=>{i(s||"")},[s]);const f=(c,_,g,j)=>{i(c),n(c)},b={toolbar:{container:[[{header:[1,2,3,!1]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{align:[]}],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],["blockquote","code-block"],["link","image"],["clean"],["center-text","hindi-format"]],handlers:{"center-text":function(){this.quill.getSelection()&&this.quill.format("align","center")},"hindi-format":function(){this.quill.getSelection()&&(this.quill.format("align","center"),this.quill.format("size","large"))}}},clipboard:{matchVisual:!1}},u=["header","font","size","bold","italic","underline","strike","blockquote","list","bullet","indent","link","image","video","align","color","background","code-block"],p={minHeight:"400px",fontFamily:'"Noto Sans Devanagari", "Mangal", "Kruti Dev 010", Arial, sans-serif'};return e.jsxDEV("div",{className:"rich-text-editor",children:[e.jsxDEV(ie,{ref:m,theme:"snow",value:r,onChange:f,modules:b,formats:u,placeholder:o,style:p},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:72,columnNumber:7},void 0),e.jsxDEV("div",{className:"editor-help",children:e.jsxDEV("details",{children:[e.jsxDEV("summary",{children:"Formatting Tips"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:85,columnNumber:11},void 0),e.jsxDEV("div",{className:"help-content",children:[e.jsxDEV("h4",{children:"For Hindi Shayari:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:87,columnNumber:13},void 0),e.jsxDEV("ul",{children:[e.jsxDEV("li",{children:[e.jsxDEV("strong",{children:"Center Text:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:89,columnNumber:19},void 0)," Use the center alignment button for verses"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:89,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("strong",{children:"Line Breaks:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:90,columnNumber:19},void 0)," Press Shift+Enter for line breaks within verses"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:90,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("strong",{children:"Stanza Breaks:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:91,columnNumber:19},void 0)," Press Enter twice for stanza separation"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:91,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("strong",{children:"Emphasis:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:92,columnNumber:19},void 0)," Use bold or italic for emphasis"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:92,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:88,columnNumber:13},void 0),e.jsxDEV("h4",{children:"Keyboard Shortcuts:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:94,columnNumber:13},void 0),e.jsxDEV("ul",{children:[e.jsxDEV("li",{children:[e.jsxDEV("kbd",{children:"Ctrl+B"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:96,columnNumber:19},void 0)," - Bold"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:96,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("kbd",{children:"Ctrl+I"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:97,columnNumber:19},void 0)," - Italic"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:97,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("kbd",{children:"Ctrl+U"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:98,columnNumber:19},void 0)," - Underline"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:98,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("kbd",{children:"Ctrl+Shift+C"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:99,columnNumber:19},void 0)," - Center align"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:99,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("kbd",{children:"Ctrl+Z"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:100,columnNumber:19},void 0)," - Undo"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:100,columnNumber:15},void 0),e.jsxDEV("li",{children:[e.jsxDEV("kbd",{children:"Ctrl+Y"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:101,columnNumber:19},void 0)," - Redo"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:101,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:95,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:86,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:84,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:83,columnNumber:7},void 0),e.jsxDEV("style",{jsx:"true",global:"true",children:`
        .rich-text-editor {
          position: relative;
        }

        .rich-text-editor .ql-editor {
          min-height: 400px;
          font-family: "Noto Sans Devanagari", "Mangal", "Kruti Dev 010", Arial, sans-serif;
          font-size: 16px;
          line-height: 1.6;
        }

        .rich-text-editor .ql-editor.ql-blank::before {
          font-style: italic;
          color: #adb5bd;
        }

        /* Hindi text specific styles */
        .rich-text-editor .ql-editor p {
          margin-bottom: 1em;
        }

        .rich-text-editor .ql-editor blockquote {
          border-left: 4px solid #1976d2;
          padding-left: 1rem;
          margin: 1rem 0;
          font-style: italic;
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 4px;
        }

        /* Custom toolbar styling */
        .rich-text-editor .ql-toolbar {
          border: 1px solid #ced4da;
          border-bottom: none;
          background: #f8f9fa;
        }

        .rich-text-editor .ql-container {
          border: 1px solid #ced4da;
          border-radius: 0 0 4px 4px;
        }

        .rich-text-editor .ql-toolbar .ql-formats {
          margin-right: 15px;
        }

        .rich-text-editor .ql-toolbar button {
          padding: 5px;
          margin: 2px;
        }

        .rich-text-editor .ql-toolbar button:hover {
          background: #e9ecef;
          border-radius: 3px;
        }

        .rich-text-editor .ql-toolbar button.ql-active {
          background: #1976d2;
          color: white;
          border-radius: 3px;
        }

        /* Custom button styles */
        .rich-text-editor .ql-toolbar button.ql-center-text::before {
          content: "⌘";
          font-weight: bold;
        }

        .rich-text-editor .ql-toolbar button.ql-hindi-format::before {
          content: "हि";
          font-weight: bold;
          font-family: "Noto Sans Devanagari", "Mangal", sans-serif;
        }

        /* Focus styles */
        .rich-text-editor .ql-container.ql-snow {
          border-color: #ced4da;
        }

        .rich-text-editor .ql-editor:focus {
          outline: none;
        }

        .rich-text-editor:focus-within .ql-container {
          border-color: #1976d2;
          box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .rich-text-editor .ql-toolbar {
            padding: 8px;
          }

          .rich-text-editor .ql-toolbar .ql-formats {
            margin-right: 10px;
          }

          .rich-text-editor .ql-editor {
            min-height: 300px;
            font-size: 14px;
          }
        }

        /* Help section styling */
        .editor-help {
          margin-top: 1rem;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          background: #f8f9fa;
        }

        .editor-help summary {
          padding: 0.75rem;
          cursor: pointer;
          font-weight: 500;
          color: #495057;
          user-select: none;
        }

        .editor-help summary:hover {
          background: #e9ecef;
        }

        .help-content {
          padding: 0 0.75rem 0.75rem;
          border-top: 1px solid #e9ecef;
        }

        .help-content h4 {
          margin: 1rem 0 0.5rem 0;
          color: #212529;
          font-size: 0.9rem;
        }

        .help-content ul {
          margin: 0.5rem 0;
          padding-left: 1.5rem;
        }

        .help-content li {
          margin-bottom: 0.25rem;
          font-size: 0.875rem;
          color: #6c757d;
        }

        .help-content kbd {
          background: #e9ecef;
          border: 1px solid #adb5bd;
          border-radius: 3px;
          padding: 2px 4px;
          font-size: 0.75rem;
          font-family: monospace;
        }

        /* Print styles */
        @media print {
          .rich-text-editor .ql-toolbar,
          .editor-help {
            display: none;
          }

          .rich-text-editor .ql-container {
            border: none;
          }
        }
      `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:107,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/RichTextEditor.jsx",lineNumber:71,columnNumber:5},void 0)},ke=({currentImage:s,onImageUpload:n,maxSize:o=5*1024*1024})=>{const[m,r]=a.useState(!1),[i,f]=a.useState(!1),[b,u]=a.useState(0),p=a.useRef(null),c=a.useCallback(t=>h(null,null,function*(){if(!t||t.length===0)return;const v=t[0];if(!v.type.startsWith("image/")){k.error("Please select an image file");return}if(v.size>o){k.error(`Image size must be less than ${Math.round(o/1024/1024)}MB`);return}yield _(v)}),[o]),_=t=>h(null,null,function*(){r(!0),u(0);try{const v=yield de(t,{maxWidth:1200,maxHeight:800,quality:.8}),A=yield ce(v,{onProgress:P=>{u(Math.round(P))}});if(!A.success)throw new Error(A.error||"Upload failed");n(A.url),k.success("Image uploaded successfully!")}catch(v){k.error(v.message||"Failed to upload image. Please try again.")}finally{r(!1),u(0)}}),g=t=>{t.preventDefault(),f(!0)},j=t=>{t.preventDefault(),f(!1)},y=t=>{t.preventDefault(),f(!1);const v=Array.from(t.dataTransfer.files);c(v)},E=t=>{const v=Array.from(t.target.files);c(v)},l=()=>{n(""),p.current&&(p.current.value="")},d=()=>{var t;(t=p.current)==null||t.click()};return e.jsxDEV("div",{className:"image-uploader",children:[s?e.jsxDEV("div",{className:"current-image",children:[e.jsxDEV("div",{className:"image-preview",children:[e.jsxDEV("img",{src:s,alt:"Featured"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:105,columnNumber:13},void 0),e.jsxDEV("div",{className:"image-overlay",children:[e.jsxDEV("button",{type:"button",onClick:d,className:"btn btn-sm btn-primary",disabled:m,children:"Change"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:107,columnNumber:15},void 0),e.jsxDEV("button",{type:"button",onClick:l,className:"btn btn-sm btn-danger",disabled:m,children:"Remove"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:115,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:106,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:104,columnNumber:11},void 0),e.jsxDEV("div",{className:"image-info",children:e.jsxDEV("p",{className:"image-url",children:s},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:126,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:125,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:103,columnNumber:9},void 0):e.jsxDEV("div",{className:`upload-area ${i?"drag-over":""} ${m?"uploading":""}`,onDragOver:g,onDragLeave:j,onDrop:y,onClick:d,children:m?e.jsxDEV("div",{className:"upload-progress",children:[e.jsxDEV("div",{className:"progress-bar",children:e.jsxDEV("div",{className:"progress-fill",style:{width:`${b}%`}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:140,columnNumber:17},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:139,columnNumber:15},void 0),e.jsxDEV("p",{children:["Uploading... ",b,"%"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:145,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:138,columnNumber:13},void 0):e.jsxDEV("div",{className:"upload-content",children:[e.jsxDEV("div",{className:"upload-icon",children:"📷"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:149,columnNumber:15},void 0),e.jsxDEV("h3",{children:"Upload Featured Image"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:150,columnNumber:15},void 0),e.jsxDEV("p",{children:"Drag and drop an image here, or click to browse"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:151,columnNumber:15},void 0),e.jsxDEV("p",{className:"upload-hint",children:["Supports JPG, PNG, WebP • Max ",Math.round(o/1024/1024),"MB"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:152,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:148,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:130,columnNumber:9},void 0),e.jsxDEV("input",{ref:p,type:"file",accept:"image/*",onChange:E,style:{display:"none"}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:160,columnNumber:7},void 0),e.jsxDEV("style",{jsx:"true",children:`
        .image-uploader {
          width: 100%;
        }

        .upload-area {
          border: 2px dashed #ced4da;
          border-radius: 8px;
          padding: 2rem;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s;
          background: #f8f9fa;
          min-height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .upload-area:hover {
          border-color: #1976d2;
          background: #f0f8ff;
        }

        .upload-area.drag-over {
          border-color: #1976d2;
          background: #e3f2fd;
          transform: scale(1.02);
        }

        .upload-area.uploading {
          cursor: not-allowed;
          opacity: 0.7;
        }

        .upload-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
        }

        .upload-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .upload-content h3 {
          margin: 0;
          color: #212529;
          font-size: 1.25rem;
        }

        .upload-content p {
          margin: 0;
          color: #6c757d;
        }

        .upload-hint {
          font-size: 0.875rem;
          color: #adb5bd;
        }

        .upload-progress {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          width: 100%;
          max-width: 300px;
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: #e9ecef;
          border-radius: 4px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background: #1976d2;
          transition: width 0.3s ease;
        }

        .current-image {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .image-preview {
          position: relative;
          border-radius: 8px;
          overflow: hidden;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
        }

        .image-preview img {
          width: 100%;
          height: 200px;
          object-fit: cover;
          display: block;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          opacity: 0;
          transition: opacity 0.2s;
        }

        .image-preview:hover .image-overlay {
          opacity: 1;
        }

        .image-info {
          padding: 0.5rem;
          background: #f8f9fa;
          border-radius: 4px;
          border: 1px solid #e9ecef;
        }

        .image-url {
          margin: 0;
          font-size: 0.75rem;
          color: #6c757d;
          word-break: break-all;
          font-family: monospace;
        }

        .btn {
          padding: 0.375rem 0.75rem;
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          text-decoration: none;
          display: inline-block;
          transition: all 0.2s;
          font-size: 0.875rem;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-sm {
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
        }

        .btn-primary {
          background: #1976d2;
          color: white;
          border-color: #1976d2;
        }

        .btn-primary:hover:not(:disabled) {
          background: #1565c0;
          border-color: #1565c0;
        }

        .btn-danger {
          background: #dc3545;
          color: white;
          border-color: #dc3545;
        }

        .btn-danger:hover:not(:disabled) {
          background: #c82333;
          border-color: #c82333;
        }

        @media (max-width: 768px) {
          .upload-area {
            padding: 1.5rem;
            min-height: 150px;
          }

          .upload-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
          }

          .upload-content h3 {
            font-size: 1rem;
          }

          .upload-content p {
            font-size: 0.875rem;
          }

          .image-preview img {
            height: 150px;
          }

          .image-overlay {
            flex-direction: column;
          }
        }
      `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:168,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ImageUploader.jsx",lineNumber:101,columnNumber:5},void 0)},he=({data:s})=>{const n=r=>{if(!r)return"Not published";try{return new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(i){return"Invalid date"}},o=a.useMemo(()=>{if(!s.content)return"";const r=document.createElement("div");return r.innerHTML=s.content,r.innerHTML},[s.content]),m=a.useMemo(()=>{if(s.excerpt)return s.excerpt;if(s.content){const r=document.createElement("div");r.innerHTML=s.content;const i=r.textContent||r.innerText||"";return i.substring(0,150)+(i.length>150?"...":"")}return"No excerpt available"},[s.content,s.excerpt]);return e.jsxDEV("div",{className:"post-preview",children:[e.jsxDEV("div",{className:"preview-header",children:[e.jsxDEV("h1",{children:"Preview Mode"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:49,columnNumber:9},void 0),e.jsxDEV("div",{className:"preview-status",children:e.jsxDEV("span",{className:`status-badge status-${s.status||"draft"}`,children:s.status||"draft"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:51,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:50,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:48,columnNumber:7},void 0),e.jsxDEV("article",{className:"preview-article",children:[s.featured_image_url&&e.jsxDEV("div",{className:"featured-image",children:e.jsxDEV("img",{src:s.featured_image_url,alt:s.title||"Featured image"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:61,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:60,columnNumber:11},void 0),e.jsxDEV("header",{className:"post-header",children:[e.jsxDEV("h1",{className:"post-title",children:s.title||"Untitled Post"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:70,columnNumber:11},void 0),e.jsxDEV("div",{className:"post-meta",children:[e.jsxDEV("time",{className:"post-date",children:n(s.published_at||s.created_at)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:75,columnNumber:13},void 0),s.slug&&e.jsxDEV("div",{className:"post-url",children:[e.jsxDEV("strong",{children:"URL:"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:80,columnNumber:17},void 0)," /",s.slug]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:79,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:74,columnNumber:11},void 0),m&&e.jsxDEV("div",{className:"post-excerpt",children:m},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:86,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:69,columnNumber:9},void 0),e.jsxDEV("div",{className:"post-content",children:o?e.jsxDEV("div",{dangerouslySetInnerHTML:{__html:o}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:95,columnNumber:13},void 0):e.jsxDEV("p",{className:"no-content",children:"No content available"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:97,columnNumber:13},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:93,columnNumber:9},void 0),(s.meta_title||s.meta_description)&&e.jsxDEV("div",{className:"seo-preview",children:[e.jsxDEV("h3",{children:"SEO Preview"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:104,columnNumber:13},void 0),e.jsxDEV("div",{className:"search-result-preview",children:[e.jsxDEV("div",{className:"search-title",children:s.meta_title||s.title||"Untitled Post"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:106,columnNumber:15},void 0),e.jsxDEV("div",{className:"search-url",children:[window.location.origin,"/",s.slug||"untitled-post"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:109,columnNumber:15},void 0),e.jsxDEV("div",{className:"search-description",children:s.meta_description||m},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:112,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:105,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:103,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:57,columnNumber:7},void 0),e.jsxDEV("style",{jsx:"true",children:`
        .post-preview {
          padding: 2rem;
          max-width: 800px;
          margin: 0 auto;
          background: white;
          min-height: 100vh;
        }

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
          padding-bottom: 1rem;
          border-bottom: 2px solid #e9ecef;
        }

        .preview-header h1 {
          margin: 0;
          color: #6c757d;
          font-size: 1.25rem;
          font-weight: 500;
        }

        .status-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;
        }

        .status-draft {
          background: #fff3cd;
          color: #856404;
        }

        .status-published {
          background: #d4edda;
          color: #155724;
        }

        .status-private {
          background: #f8d7da;
          color: #721c24;
        }

        .preview-article {
          line-height: 1.6;
        }

        .featured-image {
          margin-bottom: 2rem;
          border-radius: 8px;
          overflow: hidden;
        }

        .featured-image img {
          width: 100%;
          height: auto;
          display: block;
        }

        .post-header {
          margin-bottom: 2rem;
        }

        .post-title {
          font-size: 2.5rem;
          font-weight: 700;
          line-height: 1.2;
          margin: 0 0 1rem 0;
          color: #212529;
        }

        .post-meta {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-bottom: 1rem;
          font-size: 0.875rem;
          color: #6c757d;
        }

        .post-date {
          font-weight: 500;
        }

        .post-url {
          font-family: monospace;
          background: #f8f9fa;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          display: inline-block;
        }

        .post-excerpt {
          font-size: 1.125rem;
          color: #6c757d;
          font-style: italic;
          margin-top: 1rem;
          padding: 1rem;
          background: #f8f9fa;
          border-left: 4px solid #1976d2;
          border-radius: 4px;
        }

        .post-content {
          font-size: 1.1rem;
          line-height: 1.7;
          color: #212529;
        }

        .post-content h1,
        .post-content h2,
        .post-content h3,
        .post-content h4,
        .post-content h5,
        .post-content h6 {
          margin-top: 2rem;
          margin-bottom: 1rem;
          font-weight: 600;
          line-height: 1.3;
        }

        .post-content h1 { font-size: 2rem; }
        .post-content h2 { font-size: 1.75rem; }
        .post-content h3 { font-size: 1.5rem; }
        .post-content h4 { font-size: 1.25rem; }
        .post-content h5 { font-size: 1.125rem; }
        .post-content h6 { font-size: 1rem; }

        .post-content p {
          margin-bottom: 1.5rem;
        }

        .post-content blockquote {
          margin: 2rem 0;
          padding: 1rem 2rem;
          border-left: 4px solid #1976d2;
          background: #f8f9fa;
          font-style: italic;
          border-radius: 4px;
        }

        .post-content ul,
        .post-content ol {
          margin-bottom: 1.5rem;
          padding-left: 2rem;
        }

        .post-content li {
          margin-bottom: 0.5rem;
        }

        .post-content img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          margin: 1rem 0;
        }

        .post-content code {
          background: #f8f9fa;
          padding: 0.2rem 0.4rem;
          border-radius: 3px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }

        .post-content pre {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 4px;
          overflow-x: auto;
          margin: 1rem 0;
        }

        .post-content pre code {
          background: none;
          padding: 0;
        }

        .no-content {
          color: #6c757d;
          font-style: italic;
          text-align: center;
          padding: 2rem;
          background: #f8f9fa;
          border-radius: 4px;
        }

        .seo-preview {
          margin-top: 3rem;
          padding-top: 2rem;
          border-top: 2px solid #e9ecef;
        }

        .seo-preview h3 {
          margin: 0 0 1rem 0;
          color: #495057;
          font-size: 1.125rem;
        }

        .search-result-preview {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 4px;
          border: 1px solid #e9ecef;
        }

        .search-title {
          color: #1a0dab;
          font-size: 1.125rem;
          font-weight: 500;
          margin-bottom: 0.25rem;
          text-decoration: underline;
        }

        .search-url {
          color: #006621;
          font-size: 0.875rem;
          margin-bottom: 0.25rem;
        }

        .search-description {
          color: #545454;
          font-size: 0.875rem;
          line-height: 1.4;
        }

        /* Hindi text support */
        .post-content,
        .post-title,
        .post-excerpt {
          font-family: "Noto Sans Devanagari", "Mangal", "Kruti Dev 010", Arial, sans-serif;
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .post-preview {
            padding: 1rem;
          }

          .preview-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          .post-title {
            font-size: 2rem;
          }

          .post-meta {
            font-size: 0.8rem;
          }

          .post-content {
            font-size: 1rem;
          }

          .post-content h1 { font-size: 1.75rem; }
          .post-content h2 { font-size: 1.5rem; }
          .post-content h3 { font-size: 1.25rem; }
        }

        /* Print styles */
        @media print {
          .preview-header {
            display: none;
          }

          .seo-preview {
            display: none;
          }

          .post-preview {
            padding: 0;
          }
        }
      `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:120,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostPreview.jsx",lineNumber:47,columnNumber:5},void 0)},Ve=({initialData:s=null,onSave:n,onCancel:o,loading:m=!1,isEditing:r=!1})=>{const[i,f]=a.useState(!1),[b,u]=a.useState(!1),[p,c]=a.useState(null),[_,g]=a.useState(!1),{register:j,handleSubmit:y,watch:E,setValue:l,getValues:d,formState:{errors:t,isDirty:v},reset:A}=te({defaultValues:{title:(s==null?void 0:s.title)||"",slug:(s==null?void 0:s.slug)||"",content:(s==null?void 0:s.content)||"",excerpt:(s==null?void 0:s.excerpt)||"",featured_image_url:(s==null?void 0:s.featured_image_url)||"",meta_title:(s==null?void 0:s.meta_title)||"",meta_description:(s==null?void 0:s.meta_description)||"",status:(s==null?void 0:s.status)||"draft"}}),P=E("title"),w=E("slug"),U=E("content");a.useEffect(()=>{if(P&&!r){const D=pe(P);l("slug",D)}},[P,l,r]),a.useEffect(()=>{const S=setTimeout(()=>h(null,null,function*(){if(w&&w!==(s==null?void 0:s.slug)){g(!0);try{(yield be(w,s==null?void 0:s.id))||k.error("This slug is already taken. Please choose a different one.")}catch(Z){}finally{g(!1)}}}),500);return()=>clearTimeout(S)},[w,s==null?void 0:s.slug,s==null?void 0:s.id]),a.useEffect(()=>{if(v&&(P||U)){const D=setTimeout(()=>h(null,null,function*(){yield z()}),3e4);return()=>clearTimeout(D)}},[v,P,U]);const z=()=>h(null,null,function*(){if(v){u(!0);try{const D=E();if(r&&(s!=null&&s.id))yield K(s.id,x(N({},D),{status:"draft"}));else if(D.title||D.content){const S=yield Y(x(N({},D),{status:"draft"}));S&&!s&&window.history.replaceState(null,"",`/admin/edit/${S.id}`)}c(new Date)}catch(D){}finally{u(!1)}}}),L=D=>h(null,null,function*(){try{let S;return r?S=yield K(s.id,D):S=yield Y(D),n(D,D.status==="draft"),S}catch(S){throw k.error("Failed to save post. Please try again."),S}}),T=()=>{l("status","draft"),y(L)()},V=()=>{l("status","published"),l("published_at",new Date().toISOString()),y(L)()},I=a.useCallback(D=>{l("content",D,{shouldDirty:!0})},[l]),F=a.useCallback(D=>{l("featured_image_url",D,{shouldDirty:!0})},[l]);return e.jsxDEV("div",{className:"post-editor",children:[e.jsxDEV("div",{className:"editor-header",children:[e.jsxDEV("div",{className:"editor-actions",children:[e.jsxDEV("button",{type:"button",onClick:()=>f(!i),className:"btn btn-outline",children:i?"📝 Edit":"👁️ Preview"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:152,columnNumber:11},void 0),e.jsxDEV("div",{className:"save-status",children:[b&&e.jsxDEV("span",{className:"auto-saving",children:"Auto-saving..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:161,columnNumber:28},void 0),p&&!b&&e.jsxDEV("span",{className:"last-saved",children:["Saved ",p.toLocaleTimeString()]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:163,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:160,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:151,columnNumber:9},void 0),e.jsxDEV("div",{className:"primary-actions",children:[e.jsxDEV("button",{type:"button",onClick:o,className:"btn btn-secondary",disabled:m,children:"Cancel"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:171,columnNumber:11},void 0),e.jsxDEV("button",{type:"button",onClick:T,className:"btn btn-outline",disabled:m,children:m?"Saving...":"Save Draft"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:179,columnNumber:11},void 0),e.jsxDEV("button",{type:"button",onClick:V,className:"btn btn-primary",disabled:m||!P,children:m?"Publishing...":"Publish"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:187,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:170,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:150,columnNumber:7},void 0),e.jsxDEV("div",{className:"editor-content",children:i?e.jsxDEV(he,{data:E()},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:200,columnNumber:11},void 0):e.jsxDEV("form",{onSubmit:y(L),className:"post-form",children:[e.jsxDEV("div",{className:"form-row",children:e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"title",children:"Title *"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:205,columnNumber:17},void 0),e.jsxDEV("input",x(N({id:"title",type:"text"},j("title",{required:"Title is required",minLength:{value:3,message:"Title must be at least 3 characters"}})),{className:`form-control ${t.title?"error":""}`,placeholder:"Enter post title..."}),void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:206,columnNumber:17},void 0),t.title&&e.jsxDEV("span",{className:"error-message",children:t.title.message},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:216,columnNumber:34},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:204,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:203,columnNumber:13},void 0),e.jsxDEV("div",{className:"form-row",children:e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"slug",children:["URL Slug *",_&&e.jsxDEV("span",{className:"checking",children:"Checking..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:224,columnNumber:36},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:222,columnNumber:17},void 0),e.jsxDEV("div",{className:"slug-input",children:[e.jsxDEV("span",{className:"slug-prefix",children:["/",window.location.host,"/"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:227,columnNumber:19},void 0),e.jsxDEV("input",x(N({id:"slug",type:"text"},j("slug",{required:"Slug is required",pattern:{value:/^[a-z0-9-]+$/,message:"Slug can only contain lowercase letters, numbers, and hyphens"}})),{className:`form-control ${t.slug?"error":""}`,placeholder:"post-url-slug"}),void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:228,columnNumber:19},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:226,columnNumber:17},void 0),t.slug&&e.jsxDEV("span",{className:"error-message",children:t.slug.message},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:242,columnNumber:33},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:221,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:220,columnNumber:13},void 0),e.jsxDEV("div",{className:"form-row",children:e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"excerpt",children:"Excerpt"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:248,columnNumber:17},void 0),e.jsxDEV("textarea",x(N({id:"excerpt"},j("excerpt")),{className:"form-control",rows:"3",placeholder:"Brief description of the post..."}),void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:249,columnNumber:17},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:247,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:246,columnNumber:13},void 0),e.jsxDEV("div",{className:"form-row",children:e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{children:"Featured Image"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:261,columnNumber:17},void 0),e.jsxDEV(ke,{currentImage:E("featured_image_url"),onImageUpload:F},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:262,columnNumber:17},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:260,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:259,columnNumber:13},void 0),e.jsxDEV("div",{className:"form-row",children:e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"content",children:"Content"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:271,columnNumber:17},void 0),e.jsxDEV(ge,{value:U,onChange:I,placeholder:"Write your post content here..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:272,columnNumber:17},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:270,columnNumber:15},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:269,columnNumber:13},void 0),e.jsxDEV("details",{className:"seo-section",children:[e.jsxDEV("summary",{children:"SEO Settings"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:281,columnNumber:15},void 0),e.jsxDEV("div",{className:"seo-fields",children:[e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"meta_title",children:"Meta Title"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:284,columnNumber:19},void 0),e.jsxDEV("input",x(N({id:"meta_title",type:"text"},j("meta_title")),{className:"form-control",placeholder:"SEO title (leave empty to use post title)"}),void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:285,columnNumber:19},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:283,columnNumber:17},void 0),e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"meta_description",children:"Meta Description"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:294,columnNumber:19},void 0),e.jsxDEV("textarea",x(N({id:"meta_description"},j("meta_description")),{className:"form-control",rows:"2",placeholder:"SEO description (leave empty to use excerpt)"}),void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:295,columnNumber:19},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:293,columnNumber:17},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:282,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:280,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:202,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:198,columnNumber:7},void 0),e.jsxDEV("style",{jsx:"true",children:`
        .post-editor {
          max-width: 100%;
        }

        .editor-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 8px 8px 0 0;
          margin-bottom: 0;
        }

        .editor-actions {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .save-status {
          font-size: 0.875rem;
          color: #6c757d;
        }

        .auto-saving {
          color: #ffc107;
        }

        .last-saved {
          color: #28a745;
        }

        .primary-actions {
          display: flex;
          gap: 0.5rem;
        }

        .editor-content {
          background: white;
          border: 1px solid #e9ecef;
          border-top: none;
          border-radius: 0 0 8px 8px;
          min-height: 600px;
        }

        .post-form {
          padding: 2rem;
        }

        .form-row {
          margin-bottom: 1.5rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
        }

        .form-group label {
          margin-bottom: 0.5rem;
          font-weight: 500;
          color: #212529;
        }

        .checking {
          color: #ffc107;
          font-size: 0.75rem;
          margin-left: 0.5rem;
        }

        .form-control {
          padding: 0.75rem;
          border: 1px solid #ced4da;
          border-radius: 4px;
          font-size: 1rem;
          transition: border-color 0.2s;
        }

        .form-control:focus {
          outline: none;
          border-color: #1976d2;
          box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
        }

        .form-control.error {
          border-color: #dc3545;
        }

        .slug-input {
          display: flex;
          align-items: center;
          border: 1px solid #ced4da;
          border-radius: 4px;
          overflow: hidden;
        }

        .slug-prefix {
          background: #f8f9fa;
          padding: 0.75rem;
          color: #6c757d;
          font-size: 0.875rem;
          border-right: 1px solid #ced4da;
        }

        .slug-input input {
          border: none;
          flex: 1;
        }

        .error-message {
          color: #dc3545;
          font-size: 0.875rem;
          margin-top: 0.25rem;
        }

        .seo-section {
          margin-top: 2rem;
          border: 1px solid #e9ecef;
          border-radius: 4px;
        }

        .seo-section summary {
          padding: 1rem;
          cursor: pointer;
          background: #f8f9fa;
          font-weight: 500;
        }

        .seo-fields {
          padding: 1rem;
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .btn {
          padding: 0.5rem 1rem;
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          text-decoration: none;
          display: inline-block;
          transition: all 0.2s;
          font-size: 0.875rem;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-primary {
          background: #1976d2;
          color: white;
          border-color: #1976d2;
        }

        .btn-primary:hover:not(:disabled) {
          background: #1565c0;
          border-color: #1565c0;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
          border-color: #6c757d;
        }

        .btn-secondary:hover:not(:disabled) {
          background: #5a6268;
          border-color: #5a6268;
        }

        .btn-outline {
          background: transparent;
          color: #6c757d;
          border-color: #6c757d;
        }

        .btn-outline:hover:not(:disabled) {
          background: #6c757d;
          color: white;
        }

        @media (max-width: 768px) {
          .editor-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
          }

          .primary-actions {
            justify-content: center;
          }

          .post-form {
            padding: 1rem;
          }

          .slug-input {
            flex-direction: column;
          }

          .slug-prefix {
            border-right: none;
            border-bottom: 1px solid #ced4da;
          }
        }
      `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:311,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/PostEditor.jsx",lineNumber:149,columnNumber:5},void 0)},Pe=({selectedPosts:s,onActionComplete:n,onClearSelection:o})=>{const[m,r]=a.useState(!1),i=u=>h(null,null,function*(){if(s.length===0){k.error("Please select posts to perform bulk action");return}const p=b(u,s.length);if(confirm(p)){r(!0);try{let c;switch(u){case"publish":c=yield B(s,"published"),k.success(`${c} posts published successfully`);break;case"draft":c=yield B(s,"draft"),k.success(`${c} posts moved to draft`);break;case"private":c=yield B(s,"private"),k.success(`${c} posts made private`);break;case"delete":c=yield f(s),k.success(`${c} posts deleted successfully`);break;default:throw new Error("Invalid bulk action")}n(),o()}catch(c){k.error(`Failed to ${u} posts. Please try again.`)}finally{r(!1)}}}),f=u=>h(null,null,function*(){let p=0;for(const c of u)try{yield fe(c),p++}catch(_){}return p}),b=(u,p)=>{const c=p===1?"post":"posts";switch(u){case"publish":return`Are you sure you want to publish ${p} ${c}?`;case"draft":return`Are you sure you want to move ${p} ${c} to draft?`;case"private":return`Are you sure you want to make ${p} ${c} private?`;case"delete":return`Are you sure you want to delete ${p} ${c}? This action cannot be undone.`;default:return`Are you sure you want to perform this action on ${p} ${c}?`}};return s.length===0?null:e.jsxDEV("div",{className:"bulk-actions",children:[e.jsxDEV("div",{className:"bulk-info",children:[e.jsxDEV("span",{className:"selected-count",children:[s.length," post",s.length!==1?"s":""," selected"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:89,columnNumber:9},void 0),e.jsxDEV("button",{type:"button",onClick:o,className:"clear-selection",disabled:m,children:"Clear Selection"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:92,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:88,columnNumber:7},void 0),e.jsxDEV("div",{className:"bulk-buttons",children:[e.jsxDEV("button",{type:"button",onClick:()=>i("publish"),className:"btn btn-sm btn-success",disabled:m,children:m?"Processing...":"Publish"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:103,columnNumber:9},void 0),e.jsxDEV("button",{type:"button",onClick:()=>i("draft"),className:"btn btn-sm btn-warning",disabled:m,children:m?"Processing...":"Move to Draft"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:111,columnNumber:9},void 0),e.jsxDEV("button",{type:"button",onClick:()=>i("private"),className:"btn btn-sm btn-secondary",disabled:m,children:m?"Processing...":"Make Private"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:119,columnNumber:9},void 0),e.jsxDEV("button",{type:"button",onClick:()=>i("delete"),className:"btn btn-sm btn-danger",disabled:m,children:m?"Processing...":"Delete"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:127,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:102,columnNumber:7},void 0),e.jsxDEV("style",{jsx:"true",children:`
        .bulk-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          margin-bottom: 1rem;
          flex-wrap: wrap;
          gap: 1rem;
        }

        .bulk-info {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .selected-count {
          font-weight: 500;
          color: #495057;
        }

        .clear-selection {
          background: none;
          border: none;
          color: #6c757d;
          cursor: pointer;
          text-decoration: underline;
          font-size: 0.875rem;
        }

        .clear-selection:hover:not(:disabled) {
          color: #495057;
        }

        .clear-selection:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .bulk-buttons {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
        }

        .btn {
          padding: 0.375rem 0.75rem;
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          text-decoration: none;
          display: inline-block;
          transition: all 0.2s;
          font-size: 0.875rem;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-sm {
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
        }

        .btn-success {
          background: #28a745;
          color: white;
          border-color: #28a745;
        }

        .btn-success:hover:not(:disabled) {
          background: #218838;
          border-color: #218838;
        }

        .btn-warning {
          background: #ffc107;
          color: #212529;
          border-color: #ffc107;
        }

        .btn-warning:hover:not(:disabled) {
          background: #e0a800;
          border-color: #e0a800;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
          border-color: #6c757d;
        }

        .btn-secondary:hover:not(:disabled) {
          background: #5a6268;
          border-color: #5a6268;
        }

        .btn-danger {
          background: #dc3545;
          color: white;
          border-color: #dc3545;
        }

        .btn-danger:hover:not(:disabled) {
          background: #c82333;
          border-color: #c82333;
        }

        @media (max-width: 768px) {
          .bulk-actions {
            flex-direction: column;
            align-items: stretch;
          }

          .bulk-info {
            justify-content: space-between;
          }

          .bulk-buttons {
            justify-content: center;
          }
        }
      `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:137,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/BulkActions.jsx",lineNumber:87,columnNumber:5},void 0)},je=()=>{var E,l;const[s,n]=a.useState(""),[o,m]=a.useState(""),[r,i]=a.useState(!1),[f,b]=a.useState(!1),{signIn:u,resetPassword:p}=J(),c=le(),g=((l=(E=M().state)==null?void 0:E.from)==null?void 0:l.pathname)||"/admin/posts",j=d=>h(null,null,function*(){if(d.preventDefault(),!s||!o)return;i(!0);const{error:t}=yield u(s,o);t||c(g,{replace:!0}),i(!1)}),y=d=>h(null,null,function*(){if(d.preventDefault(),!s){alert("Please enter your email address first");return}yield p(s),b(!1)});return e.jsxDEV("div",{className:"login-container",children:[e.jsxDEV("div",{className:"login-card",children:[e.jsxDEV("div",{className:"login-header",children:[e.jsxDEV("h1",{children:"Admin Login"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:45,columnNumber:11},void 0),e.jsxDEV("p",{children:"Sign in to manage your blog"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:46,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:44,columnNumber:9},void 0),f?e.jsxDEV("form",{onSubmit:y,className:"login-form",children:[e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"reset-email",children:"Email"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:97,columnNumber:15},void 0),e.jsxDEV("input",{id:"reset-email",type:"email",value:s,onChange:d=>n(d.target.value),placeholder:"Enter your email",required:!0},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:98,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:96,columnNumber:13},void 0),e.jsxDEV("button",{type:"submit",className:"login-button",disabled:!s,children:"Send Reset Email"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:108,columnNumber:13},void 0),e.jsxDEV("button",{type:"button",className:"forgot-password-link",onClick:()=>b(!1),children:"Back to Login"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:116,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:95,columnNumber:11},void 0):e.jsxDEV("form",{onSubmit:j,className:"login-form",children:[e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"email",children:"Email"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:52,columnNumber:15},void 0),e.jsxDEV("input",{id:"email",type:"email",value:s,onChange:d=>n(d.target.value),placeholder:"Enter your email",required:!0,disabled:r},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:53,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:51,columnNumber:13},void 0),e.jsxDEV("div",{className:"form-group",children:[e.jsxDEV("label",{htmlFor:"password",children:"Password"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:65,columnNumber:15},void 0),e.jsxDEV("input",{id:"password",type:"password",value:o,onChange:d=>m(d.target.value),placeholder:"Enter your password",required:!0,disabled:r},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:66,columnNumber:15},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:64,columnNumber:13},void 0),e.jsxDEV("button",{type:"submit",className:"login-button",disabled:r||!s||!o,children:r?"Signing in...":"Sign In"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:77,columnNumber:13},void 0),e.jsxDEV("button",{type:"button",className:"forgot-password-link",onClick:()=>b(!0),disabled:r,children:"Forgot your password?"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:85,columnNumber:13},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:50,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:43,columnNumber:7},void 0),e.jsxDEV("style",{jsx:!0,children:`
        .login-container {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 1rem;
        }

        .login-card {
          background: white;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
          padding: 2.5rem;
          width: 100%;
          max-width: 400px;
        }

        .login-header {
          text-align: center;
          margin-bottom: 2rem;
        }

        .login-header h1 {
          margin: 0 0 0.5rem 0;
          color: #212529;
          font-size: 2rem;
          font-weight: 600;
        }

        .login-header p {
          margin: 0;
          color: #6c757d;
          font-size: 1rem;
        }

        .login-form {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-group label {
          font-weight: 500;
          color: #212529;
          font-size: 0.9rem;
        }

        .form-group input {
          padding: 0.75rem;
          border: 2px solid #e9ecef;
          border-radius: 8px;
          font-size: 1rem;
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-group input:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group input:disabled {
          background-color: #f8f9fa;
          cursor: not-allowed;
        }

        .login-button {
          padding: 0.875rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
        }

        .login-button:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .login-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .forgot-password-link {
          background: none;
          border: none;
          color: #667eea;
          font-size: 0.9rem;
          cursor: pointer;
          text-decoration: underline;
          padding: 0.5rem;
        }

        .forgot-password-link:hover:not(:disabled) {
          color: #764ba2;
        }

        .forgot-password-link:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:127,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/Login.jsx",lineNumber:42,columnNumber:5},void 0)},Se=Object.freeze(Object.defineProperty({__proto__:null,default:je},Symbol.toStringTag,{value:"Module"})),De=({children:s})=>{const{user:n,loading:o}=J(),m=M();return o?e.jsxDEV("div",{className:"loading-container",children:[e.jsxDEV("div",{className:"spinner"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ProtectedRoute.jsx",lineNumber:11,columnNumber:9},void 0),e.jsxDEV("p",{children:"Checking authentication..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ProtectedRoute.jsx",lineNumber:12,columnNumber:9},void 0),e.jsxDEV("style",{jsx:!0,children:`
          .loading-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            background: #f8f9fa;
          }

          .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e9ecef;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          p {
            color: #6c757d;
            font-size: 1rem;
            margin: 0;
          }
        `},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ProtectedRoute.jsx",lineNumber:14,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ProtectedRoute.jsx",lineNumber:10,columnNumber:7},void 0):n?s:e.jsxDEV(ae,{to:"/admin/login",state:{from:m},replace:!0},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/components/admin/ProtectedRoute.jsx",lineNumber:51,columnNumber:12},void 0)},Le=Object.freeze(Object.defineProperty({__proto__:null,default:De},Symbol.toStringTag,{value:"Module"}));export{ye as A,Pe as B,we as F,Ne as H,Se as L,xe as P,q as S,Ve as a,Le as b,J as u};
