var x=(s,a,o)=>new Promise((l,m)=>{var p=i=>{try{n(o.next(i))}catch(t){m(t)}},c=i=>{try{n(o.throw(i))}catch(t){m(t)}},n=i=>i.done?l(i.value):Promise.resolve(i.value).then(p,c);n((o=o.apply(s,a)).next())});import{r,j as e}from"./react-vendor-QWuLYCV5.js";import{q as H}from"./utils-BwAkzwKy.js";import{S as _,P as k}from"./components-BP4YpoFo.js";import"./vendor-CJchTyIO.js";import"./supabase-vendor-DEo8APS7.js";const P=({searchQuery:s})=>{const[a,o]=r.useState([]),[l,m]=r.useState(!0),[p,c]=r.useState(null),[n,i]=r.useState(!0),[t,N]=r.useState(0),j=12;r.useEffect(()=>{b(!0)},[s]);const b=r.useCallback((u=!1)=>x(null,null,function*(){try{m(!0),c(null);const d=u?0:t,{posts:g,hasMore:E}=yield H(d,j,s);u?(o(g),N(1)):(o(f=>[...f,...g]),N(f=>f+1)),i(E)}catch(d){c("Failed to load posts")}finally{m(!1)}}),[t,s]);if(l&&a.length===0)return e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV(_,{type:"post",count:6},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:50,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:49,columnNumber:7},void 0);if(p)return e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"error",children:p},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:58,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:57,columnNumber:7},void 0);if(a.length===0&&!l)return e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"loading",children:s?`No posts found for "${s}"`:"No posts available"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:66,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:65,columnNumber:7},void 0);const[v,...D]=a;return e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:"main-grid",children:[v&&!s&&e.jsxDEV(k,{post:v,featured:!0,priority:!0},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:80,columnNumber:11},void 0),(s?a:D).map((u,d)=>e.jsxDEV(k,{post:u,priority:d<3,showDebug:d===0},u.id,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:84,columnNumber:11},void 0))]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:78,columnNumber:7},void 0),n&&!s&&e.jsxDEV("div",{className:"load-more-container",children:e.jsxDEV("button",{onClick:()=>b(!1),disabled:l,className:"load-more-btn",children:l?e.jsxDEV("div",{className:"loading-inline",children:[e.jsxDEV("div",{className:"spinner",style:{width:"16px",height:"16px",marginRight:"8px"}},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:102,columnNumber:17},void 0),"Loading..."]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:101,columnNumber:15},void 0):"Load More Posts"},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:95,columnNumber:11},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:94,columnNumber:9},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Home.jsx",lineNumber:77,columnNumber:5},void 0)},M=r.memo(P);export{M as default};
