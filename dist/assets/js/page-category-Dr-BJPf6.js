var c=(o,n,i)=>new Promise((m,s)=>{var d=r=>{try{a(i.next(r))}catch(l){s(l)}},p=r=>{try{a(i.throw(r))}catch(l){s(l)}},a=r=>r.done?m(r.value):Promise.resolve(r.value).then(d,p);a((i=i.apply(o,n)).next())});import{c as N,r as u,j as e}from"./react-vendor-QWuLYCV5.js";import{o as b,p as x}from"./utils-BwAkzwKy.js";import{P as v}from"./components-BP4YpoFo.js";import"./vendor-CJchTyIO.js";import"./supabase-vendor-DEo8APS7.js";const E=({searchQuery:o})=>{const{slug:n}=N(),[i,m]=u.useState([]),[s,d]=u.useState(null),[p,a]=u.useState(!0),[r,l]=u.useState(null);u.useEffect(()=>{n&&f()},[n,o]);const f=()=>c(null,null,function*(){try{a(!0);const t=yield b(n);if(!t)throw new Error("Category not found");d(t);const g=yield x(t.id,o);m(g)}catch(t){l("Failed to load category posts")}finally{a(!1)}});return p?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"loading",children:"Loading posts..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:46,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:45,columnNumber:7},void 0):r?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"error",children:r},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:54,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:53,columnNumber:7},void 0):i.length===0?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"loading",children:o?`No posts found in "${s==null?void 0:s.name}" for "${o}"`:`No posts found in "${s==null?void 0:s.name}"`},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:62,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:61,columnNumber:7},void 0):e.jsxDEV(e.Fragment,{children:[s&&e.jsxDEV("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[e.jsxDEV("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:s.name},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:82,columnNumber:11},void 0),s.description&&e.jsxDEV("p",{style:{color:"#666",fontSize:"16px"},children:s.description},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:91,columnNumber:13},void 0),e.jsxDEV("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[i.length," ",i.length===1?"post":"posts"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:95,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:75,columnNumber:9},void 0),e.jsxDEV("div",{className:"main-grid",children:i.map(t=>e.jsxDEV(v,{post:t},t.id,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:103,columnNumber:11},void 0))},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:101,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Category.jsx",lineNumber:73,columnNumber:5},void 0)};export{E as default};
