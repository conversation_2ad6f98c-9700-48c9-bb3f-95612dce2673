var i=(t,e,r)=>new Promise((a,s)=>{var o=n=>{try{l(r.next(n))}catch(d){s(d)}},c=n=>{try{l(r.throw(n))}catch(d){s(d)}},l=n=>n.done?a(n.value):Promise.resolve(n.value).then(o,c);l((r=r.apply(t,e)).next())});import{c as _}from"./supabase-vendor-DEo8APS7.js";import{s as L}from"./vendor-CJchTyIO.js";const S="https://cgmlpbxwmqynmshecaqn.supabase.co",T="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnbWxwYnh3bXF5bm1zaGVjYXFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4ODYzOTIsImV4cCI6MjA2OTQ2MjM5Mn0.PJambQA-fPcmJPjsLuLJrTtYZXqOx7wzuslJjDjzo_A",w=_(S,T,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"sayari-blog@1.0.0"}}}),y=_(S,T,{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"sayari-blog-public@1.0.0"}}}),A=(t,e=150)=>{if(!t)return"";const r=t.replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").trim();if(r.length<=e)return r;const a=r.substring(0,e),s=a.lastIndexOf(" ");return s>e*.8?a.substring(0,s)+"...":a+"..."},J=(t,e=null)=>{if(e)return E(e);if(!t)return null;const r=t.match(/<img[^>]+src="([^"]*)"[^>]*>/i);return r?E(r[1]):null},N=t=>{if(!t)return"";const e=new Date(t),a=Math.abs(new Date-e),s=Math.ceil(a/(1e3*60*60*24));return s===1?"Yesterday":s<7?`${s} days ago`:s<30?`${Math.ceil(s/7)} weeks ago`:e.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})},X=(t,e)=>{let r;return function(...s){const o=()=>{clearTimeout(r),t(...s)};clearTimeout(r),r=setTimeout(o,e)}},E=t=>{if(!t)return null;const e="https://pub-your_account_id.r2.dev/blog-images",r=!e.includes("your_account_id");if(t.includes("supabase.co/storage/v1/object/public/images/"))try{const a=t.split("/images/");if(a.length===2){const s=a[1];if(r)return`${e.replace(/\/$/,"")}/${s}`}}catch(a){}if(t.includes("your-account.r2.cloudflarestorage.com")||t.includes("blog-images.your-account.r2.cloudflarestorage.com"))try{const a=t.split("/blog-images/");if(a.length===2){const s=a[1];if(r)return`${e.replace(/\/$/,"")}/${s}`}}catch(a){}return t};window.location.hostname==="localhost"||window.location.hostname==="[::1]"||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/);class ${constructor(){this.metrics={},this.observers=[],this.init()}init(){this.trackLCP(),this.trackFID(),this.trackCLS(),this.trackFCP(),this.trackTTFB(),this.trackNavigationTiming(),this.trackResourceTiming()}trackLCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(r=>{const a=r.getEntries(),s=a[a.length-1];this.metrics.lcp={value:s.startTime,element:s.element,timestamp:Date.now()},this.reportMetric("LCP",s.startTime)});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}trackFID(){if("PerformanceObserver"in window){const e=new PerformanceObserver(r=>{r.getEntries().forEach(s=>{this.metrics.fid={value:s.processingStart-s.startTime,timestamp:Date.now()},this.reportMetric("FID",s.processingStart-s.startTime)})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}trackCLS(){if("PerformanceObserver"in window){let e=0;const r=new PerformanceObserver(a=>{a.getEntries().forEach(o=>{o.hadRecentInput||(e+=o.value)}),this.metrics.cls={value:e,timestamp:Date.now()},this.reportMetric("CLS",e)});r.observe({entryTypes:["layout-shift"]}),this.observers.push(r)}}trackFCP(){if("PerformanceObserver"in window){const e=new PerformanceObserver(r=>{r.getEntries().forEach(s=>{s.name==="first-contentful-paint"&&(this.metrics.fcp={value:s.startTime,timestamp:Date.now()},this.reportMetric("FCP",s.startTime))})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}}trackTTFB(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const r=e[0],a=r.responseStart-r.requestStart;this.metrics.ttfb={value:a,timestamp:Date.now()},this.reportMetric("TTFB",a)}}}trackNavigationTiming(){if("performance"in window&&"getEntriesByType"in performance){const e=performance.getEntriesByType("navigation");if(e.length>0){const r=e[0];this.metrics.navigation={domContentLoaded:r.domContentLoadedEventEnd-r.domContentLoadedEventStart,loadComplete:r.loadEventEnd-r.loadEventStart,domInteractive:r.domInteractive-r.navigationStart,timestamp:Date.now()}}}}trackResourceTiming(){if("PerformanceObserver"in window){const e=new PerformanceObserver(r=>{r.getEntries().forEach(s=>{s.initiatorType==="img"&&this.trackImageLoad(s)})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}trackImageLoad(e){const r=e.responseEnd-e.startTime;this.metrics.images||(this.metrics.images=[]),this.metrics.images.push({url:e.name,loadTime:r,size:e.transferSize,timestamp:Date.now()}),r>1e3}reportMetric(e,r){window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(r)})}getMetrics(){return this.metrics}getPerformanceScore(){const{lcp:e,fid:r,cls:a,fcp:s}=this.metrics;let o=100;return e&&(e.value>4e3?o-=30:e.value>2500&&(o-=15)),r&&(r.value>300?o-=25:r.value>100&&(o-=10)),a&&(a.value>.25?o-=25:a.value>.1&&(o-=10)),s&&(s.value>3e3?o-=20:s.value>1800&&(o-=10)),Math.max(0,o)}disconnect(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}}const Y=()=>{if(typeof window!="undefined"){const t=new $;return window.addEventListener("load",()=>{setTimeout(()=>{const e=t.getPerformanceScore()},5e3)}),t}return null};let u={posts:null,authors:null,categories:null,tags:null};function v(t){return i(this,null,function*(){try{const e=yield fetch(`/data/static/${t}`);if(!e.ok)throw new Error(`Failed to load ${t}: ${e.status}`);return yield e.json()}catch(e){return null}})}function b(){return i(this,null,function*(){if(u.posts||(u.posts=yield v("posts.json")),u.posts)return u.posts;const{data:t,error:e}=yield y.from("posts").select(`
      id,
      title,
      slug,
      content,
      excerpt,
      author_id,
      published_at,
      status,
      featured_image_url,
      authors:author_id (
        id,
        username,
        display_name,
        bio
      )
    `).eq("status","published").order("published_at",{ascending:!1});if(e)throw new Error(`Failed to fetch posts: ${e.message}`);return t||[]})}function W(t){return i(this,null,function*(){return(yield b()).find(r=>r.slug===t)||null})}function G(t=0,e=10,r=""){return i(this,null,function*(){const a=yield b();let s=a;if(r&&r.trim()){const n=r.toLowerCase();s=a.filter(d=>{var g,f;return d.title.toLowerCase().includes(n)||((g=d.excerpt)==null?void 0:g.toLowerCase().includes(n))||((f=d.content)==null?void 0:f.toLowerCase().includes(n))})}const o=t*e,c=o+e;return{posts:s.slice(o,c),hasMore:c<s.length,total:s.length}})}function k(){return i(this,null,function*(){if(u.authors||(u.authors=yield v("authors.json")),u.authors)return u.authors;const{data:t,error:e}=yield y.from("users").select("id, user_login, display_name, user_registered").order("display_name");if(e)throw new Error(`Failed to fetch authors: ${e.message}`);return t||[]})}function Z(t){return i(this,null,function*(){return(yield k()).find(r=>r.username===t)||null})}function K(t,e=""){return i(this,null,function*(){let a=(yield b()).filter(s=>s.author_id===t);if(e&&e.trim()){const s=e.toLowerCase();a=a.filter(o=>{var c,l;return o.title.toLowerCase().includes(s)||((c=o.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=o.content)==null?void 0:l.toLowerCase().includes(s))})}return a})}function R(){return i(this,null,function*(){if(u.categories||(u.categories=yield v("categories.json")),u.categories)return u.categories;const{data:t,error:e}=yield y.from("categories").select("id, name, slug, description").order("name");if(e)throw new Error(`Failed to fetch categories: ${e.message}`);return t||[]})}function H(t){return i(this,null,function*(){return(yield R()).find(r=>r.slug===t)||null})}function Q(t,e=""){return i(this,null,function*(){let a=(yield b()).filter(s=>s.categories&&s.categories.some(o=>o.id===t));if(e&&e.trim()){const s=e.toLowerCase();a=a.filter(o=>{var c,l;return o.title.toLowerCase().includes(s)||((c=o.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=o.content)==null?void 0:l.toLowerCase().includes(s))})}return a})}function q(){return i(this,null,function*(){if(u.tags||(u.tags=yield v("tags.json")),u.tags)return u.tags;const{data:t,error:e}=yield y.from("tags").select("id, name, slug, description").order("name");if(e)throw new Error(`Failed to fetch tags: ${e.message}`);return t||[]})}function ee(t){return i(this,null,function*(){return(yield q()).find(r=>r.slug===t)||null})}function te(t,e=""){return i(this,null,function*(){let a=(yield b()).filter(s=>s.tags&&s.tags.some(o=>o.id===t));if(e&&e.trim()){const s=e.toLowerCase();a=a.filter(o=>{var c,l;return o.title.toLowerCase().includes(s)||((c=o.excerpt)==null?void 0:c.toLowerCase().includes(s))||((l=o.content)==null?void 0:l.toLowerCase().includes(s))})}return a})}const O={accountId:void 0,accessKeyId:void 0,secretAccessKey:void 0,bucketName:"blog-images",publicUrl:void 0,endpoint:void 0};function U(t){const e=Date.now(),r=Math.random().toString(36).substring(2,15),a=t.split(".").pop();return`blog-images/${e}-${r}.${a}`}function re(r){return i(this,arguments,function*(t,e={}){try{if(!t)throw new Error("No file provided");if(!["image/jpeg","image/png","image/gif","image/webp"].includes(t.type))throw new Error("Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.");const s=10*1024*1024;if(t.size>s)throw new Error("File size too large. Maximum size is 10MB.");const o=U(t.name),{bucketName:c,publicUrl:l}=O;e.onProgress&&e.onProgress(10);const n=yield t.arrayBuffer(),d=btoa(String.fromCharCode(...new Uint8Array(n)));e.onProgress&&e.onProgress(30);const g=yield M(o,d,t.type);if(e.onProgress&&e.onProgress(90),!g.success)throw new Error(g.error||"Upload failed");return e.onProgress&&e.onProgress(100),{success:!0,url:l?`${l}/${o}`:g.url,filename:o,size:t.size,type:t.type,uploadedAt:new Date().toISOString()}}catch(a){return{success:!1,error:a.message}}})}function M(t,e,r){return i(this,null,function*(){try{return yield new Promise(a=>setTimeout(a,1500)),{success:!0,url:`https://blog-images.your-account.r2.cloudflarestorage.com/${t}`,filename:t}}catch(a){return{success:!1,error:a.message}}})}const se=(r,...a)=>i(null,[r,...a],function*(t,e={}){const{maxWidth:s=1200,maxHeight:o=800,quality:c=.8,format:l="image/jpeg"}=e;return new Promise((n,d)=>{const g=document.createElement("canvas"),f=g.getContext("2d"),h=new Image;h.onload=()=>{let{width:m,height:p}=h;m>s&&(p=p*s/m,m=s),p>o&&(m=m*o/p,p=o),g.width=m,g.height=p,f.drawImage(h,0,0,m,p),g.toBlob(P=>{if(P){const F=new File([P],t.name,{type:l,lastModified:Date.now()});n(F)}else d(new Error("Failed to compress image"))},l,c)},h.onerror=()=>{d(new Error("Failed to load image"))},h.src=URL.createObjectURL(t)})}),x={अ:"a",आ:"aa",इ:"i",ई:"ii",उ:"u",ऊ:"uu",ए:"e",ऐ:"ai",ओ:"o",औ:"au",क:"ka",ख:"kha",ग:"ga",घ:"gha",ङ:"nga",च:"cha",छ:"chha",ज:"ja",झ:"jha",ञ:"nya",ट:"ta",ठ:"tha",ड:"da",ढ:"dha",ण:"na",त:"ta",थ:"tha",द:"da",ध:"dha",न:"na",प:"pa",फ:"pha",ब:"ba",भ:"bha",म:"ma",य:"ya",र:"ra",ल:"la",व:"va",श:"sha",ष:"sha",स:"sa",ह:"ha",क्ष:"ksha",त्र:"tra",ज्ञ:"gya","ं":"n","ः":"h","्":"","ा":"aa","ि":"i","ी":"ii","ु":"u","ू":"uu","े":"e","ै":"ai","ो":"o","ौ":"au"},B=t=>{if(!t)return"";let e=t;return Object.entries(x).forEach(([r,a])=>{const s=new RegExp(r,"g");e=e.replace(s,a)}),e},ae=t=>{if(!t)return"";const e=B(t),r=L(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g});return r?r.substring(0,100):`post-${Date.now()}`},j=(t,e=null)=>i(null,null,function*(){if(!t)return!1;try{let r=w.from("posts").select("id").eq("slug",t);e&&(r=r.neq("id",e));const{data:a,error:s}=yield r;return s?!1:!a||a.length===0}catch(r){return!1}}),C=(t,e=null)=>i(null,null,function*(){let r=t,a=1;for(;!(yield j(r,e));)if(r=`${t}-${a}`,a++,a>100){r=`${t}-${Date.now()}`;break}return r}),I=t=>t?t.length<3?{isValid:!1,message:"Slug must be at least 3 characters long"}:t.length>100?{isValid:!1,message:"Slug must be less than 100 characters"}:/^[a-z0-9-]+$/.test(t)?t.startsWith("-")||t.endsWith("-")?{isValid:!1,message:"Slug cannot start or end with a hyphen"}:t.includes("--")?{isValid:!1,message:"Slug cannot contain consecutive hyphens"}:["admin","api","www","mail","ftp","localhost","test","staging","dev","development","prod","production","blog","post","page","category","tag","author","search"].includes(t)?{isValid:!1,message:"This slug is reserved and cannot be used"}:{isValid:!0,message:""}:{isValid:!1,message:"Slug can only contain lowercase letters, numbers, and hyphens"}:{isValid:!1,message:"Slug is required"},oe=t=>i(null,null,function*(){try{if(!t.title)throw new Error("Title is required");const e=I(t.slug);if(!e.isValid)throw new Error(e.message);const r=yield C(t.slug),{data:{user:a}}=yield w.auth.getUser();if(!a)throw new Error("You must be logged in to create posts");const s={title:t.title.trim(),slug:r,content:t.content||"",excerpt:t.excerpt||"",status:t.status||"draft",featured_image_url:t.featured_image_url||null,meta_title:t.meta_title||null,meta_description:t.meta_description||null,author_id:a.id,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};s.status==="published"&&(s.published_at=t.published_at||new Date().toISOString());const{data:o,error:c}=yield w.from("posts").insert([s]).select().single();if(c)throw new Error(`Failed to create post: ${c.message}`);return o}catch(e){throw e}}),ne=(t,e)=>i(null,null,function*(){try{if(!t)throw new Error("Post ID is required");if(!e.title)throw new Error("Title is required");if(e.slug){const o=I(e.slug);if(!o.isValid)throw new Error(o.message);const c=yield C(e.slug,t);e.slug=c}const r={title:e.title.trim(),content:e.content||"",excerpt:e.excerpt||"",status:e.status||"draft",featured_image_url:e.featured_image_url||null,meta_title:e.meta_title||null,meta_description:e.meta_description||null,updated_at:new Date().toISOString()};if(e.slug&&(r.slug=e.slug),r.status==="published"&&e.published_at)r.published_at=e.published_at;else if(r.status==="published"){const{data:o}=yield w.from("posts").select("published_at").eq("id",t).single();o!=null&&o.published_at||(r.published_at=new Date().toISOString())}const{data:a,error:s}=yield w.from("posts").update(r).eq("id",t).select().single();if(s)throw new Error(`Failed to update post: ${s.message}`);return a}catch(r){throw r}}),ie=t=>i(null,null,function*(){try{if(!t)throw new Error("Post ID is required");const{error:e}=yield w.from("posts").delete().eq("id",t);if(e)throw new Error(`Failed to delete post: ${e.message}`);return!0}catch(e){throw e}}),ce=(...e)=>i(null,[...e],function*(t={}){try{const{page:r=0,limit:a=10,status:s=null,search:o="",orderBy:c="updated_at",orderDirection:l="desc"}=t;let n=w.from("posts").select("id, title, slug, status, published_at, created_at, updated_at",{count:"exact"});s&&s!=="all"&&(n=n.eq("status",s)),o&&o.trim()&&(n=n.ilike("title",`%${o.trim()}%`)),n=n.order(c,{ascending:l==="asc"});const d=r*a,g=d+a-1;n=n.range(d,g);const{data:f,error:h,count:m}=yield n;if(h)throw new Error(`Failed to fetch posts: ${h.message}`);return{posts:f||[],total:m||0,page:r,limit:a,hasMore:f&&f.length===a}}catch(r){throw r}}),le=(t,e)=>i(null,null,function*(){try{if(!t||t.length===0)throw new Error("Post IDs are required");if(!["draft","published","private"].includes(e))throw new Error("Invalid status");const r={status:e,updated_at:new Date().toISOString()};e==="published"&&(r.published_at=new Date().toISOString());const{data:a,error:s}=yield w.from("posts").update(r).in("id",t).select("id");if(s)throw new Error(`Failed to update posts: ${s.message}`);return a?a.length:0}catch(r){throw r}});export{A as a,se as b,E as c,X as d,ae as e,N as f,J as g,ne as h,oe as i,le as j,ie as k,Z as l,K as m,k as n,H as o,Q as p,G as q,W as r,w as s,b as t,re as u,j as v,ee as w,te as x,Y as y,ce as z};
