var c=(t,o,r)=>new Promise((m,s)=>{var d=i=>{try{n(r.next(i))}catch(l){s(l)}},p=i=>{try{n(r.throw(i))}catch(l){s(l)}},n=i=>i.done?m(i.value):Promise.resolve(i.value).then(d,p);n((r=r.apply(t,o)).next())});import{c as N,r as u,j as e}from"./react-vendor-QWuLYCV5.js";import{w as b,x}from"./utils-BwAkzwKy.js";import{P as v}from"./components-BP4YpoFo.js";import"./vendor-CJchTyIO.js";import"./supabase-vendor-DEo8APS7.js";const U=({searchQuery:t})=>{const{slug:o}=N(),[r,m]=u.useState([]),[s,d]=u.useState(null),[p,n]=u.useState(!0),[i,l]=u.useState(null);u.useEffect(()=>{o&&f()},[o,t]);const f=()=>c(null,null,function*(){try{n(!0);const a=yield b(o);if(!a)throw new Error("Tag not found");d(a);const g=yield x(a.id,t);m(g)}catch(a){l("Failed to load tag posts")}finally{n(!1)}});return p?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"loading",children:"Loading posts..."},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:46,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:45,columnNumber:7},void 0):i?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"error",children:i},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:54,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:53,columnNumber:7},void 0):r.length===0?e.jsxDEV("div",{className:"main-grid",children:e.jsxDEV("div",{className:"loading",children:t?`No posts found with tag "${s==null?void 0:s.name}" for "${t}"`:`No posts found with tag "${s==null?void 0:s.name}"`},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:62,columnNumber:9},void 0)},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:61,columnNumber:7},void 0):e.jsxDEV(e.Fragment,{children:[s&&e.jsxDEV("div",{style:{textAlign:"center",marginBottom:"30px",padding:"20px",background:"white",border:"1px solid #f0f0f0"},children:[e.jsxDEV("h1",{style:{fontSize:"28px",fontWeight:"bold",color:"#333",marginBottom:"10px"},children:["#",s.name]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:82,columnNumber:11},void 0),s.description&&e.jsxDEV("p",{style:{color:"#666",fontSize:"16px"},children:s.description},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:91,columnNumber:13},void 0),e.jsxDEV("p",{style:{color:"#999",fontSize:"14px",marginTop:"10px"},children:[r.length," ",r.length===1?"post":"posts"]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:95,columnNumber:11},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:75,columnNumber:9},void 0),e.jsxDEV("div",{className:"main-grid",children:r.map(a=>e.jsxDEV(v,{post:a},a.id,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:103,columnNumber:11},void 0))},void 0,!1,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:101,columnNumber:7},void 0)]},void 0,!0,{fileName:"/Users/<USER>/Desktop/site_v2/src/pages/Tag.jsx",lineNumber:73,columnNumber:5},void 0)};export{U as default};
